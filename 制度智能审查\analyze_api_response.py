#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析API响应并提取流程数据
"""

import json
import ast
import os
import sys

def analyze_api_response(file_path):
    """分析API响应文件并提取流程数据"""
    print(f"分析文件: {file_path}")
    
    try:
        # 读取JSON文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"文件加载成功，数据类型: {type(data)}")
        
        # 检查raw_response字段
        if 'raw_response' in data:
            raw_response = data['raw_response']
            print(f"raw_response类型: {type(raw_response)}")
            print(f"raw_response内容前200字符: {raw_response[:200]}")
            
            try:
                # 尝试解析raw_response为Python对象
                parsed = ast.literal_eval(raw_response)
                print(f"解析后类型: {type(parsed)}")
                
                if isinstance(parsed, dict):
                    print(f"解析后的键: {list(parsed.keys())}")
                    
                    # 检查output字段
                    if 'output' in parsed:
                        output = parsed['output']
                        print(f"output类型: {type(output)}")
                        print(f"output内容前200字符: {output[:200]}")
                        
                        # 提取流程步骤
                        extract_process_steps(output)
                    else:
                        print("未找到output字段")
                else:
                    print("解析后的数据不是字典")
            
            except Exception as e:
                print(f"解析raw_response失败: {e}")
                
                # 尝试直接解析为JSON
                try:
                    parsed_json = json.loads(raw_response)
                    print(f"JSON解析后类型: {type(parsed_json)}")
                    
                    if isinstance(parsed_json, dict):
                        print(f"JSON解析后的键: {list(parsed_json.keys())}")
                    else:
                        print("JSON解析后的数据不是字典")
                        
                except Exception as e2:
                    print(f"JSON解析raw_response失败: {e2}")
        else:
            print("未找到raw_response字段")
        
        # 检查sent_content字段
        if 'sent_content' in data:
            sent_content = data['sent_content']
            print(f"\nsent_content类型: {type(sent_content)}")
            print(f"sent_content内容: {sent_content}")
        else:
            print("\n未找到sent_content字段")
    
    except Exception as e:
        print(f"分析文件失败: {e}")

def extract_process_steps(text):
    """从文本中提取流程步骤"""
    print("\n尝试从文本中提取流程步骤:")
    
    # 查找流程环节部分
    if "审批流程环节" in text:
        print("找到'审批流程环节'部分")
        
        # 尝试提取编号和步骤
        steps = []
        lines = text.split('\n')
        
        for line in lines:
            # 查找类似 "1. **域长提出变更申请**" 的行
            if line.strip().startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')) and '**' in line:
                print(f"找到步骤: {line.strip()}")
                steps.append(line.strip())
        
        if steps:
            print(f"\n共找到 {len(steps)} 个步骤:")
            for step in steps:
                print(f"  - {step}")
        else:
            print("未找到任何步骤")
    else:
        print("未找到'审批流程环节'部分")
    
    # 查找部门职责节点表
    if "部门职责节点表" in text:
        print("\n找到'部门职责节点表'部分")
        
        # 尝试提取表格内容
        table_start = text.find("| 流程环节")
        if table_start != -1:
            table_end = text.find("\n\n", table_start)
            if table_end == -1:
                table_end = len(text)
            
            table_text = text[table_start:table_end]
            print(f"\n表格内容:\n{table_text}")
        else:
            print("未找到表格开始标记")
    else:
        print("\n未找到'部门职责节点表'部分")

if __name__ == "__main__":
    # 获取API响应文件路径
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # 默认使用最新的API响应文件
        dir_path = os.path.dirname(os.path.realpath(__file__))
        api_files = [f for f in os.listdir(dir_path) if f.startswith('api_response_') and f.endswith('.json')]
        
        if api_files:
            # 按文件名排序，最新的在最后
            api_files.sort()
            file_path = os.path.join(dir_path, api_files[-1])
        else:
            print("未找到API响应文件")
            sys.exit(1)
    
    analyze_api_response(file_path) 