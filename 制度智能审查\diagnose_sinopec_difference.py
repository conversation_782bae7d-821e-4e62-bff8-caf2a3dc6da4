#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中石化平台流程解析差异诊断脚本
"""

import json
import requests
import tempfile
import base64
import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from llm_processor import LLMProcessor
from api_config import get_api_config

def test_sinopec_direct_call():
    """直接调用中石化API进行测试"""
    print("🔧 直接调用中石化AI平台API测试...")
    
    # 测试文档内容
    test_content = """
    海南炼化公司域长负责制运行管理细则
    
    第一条 申请流程
    员工需要申请域长审批的事项时，应按以下流程进行：
    1. 填写申请表格
    2. 直接主管初步审核
    3. 域长最终审批
    4. 相关部门备案
    
    第二条 审批权限
    域长有权对以下事项进行审批：
    1. 10万元以下的费用支出
    2. 人员调动申请
    3. 设备采购申请
    
    第三条 时间要求
    1. 申请人提交申请后，直接主管应在2个工作日内完成初审
    2. 域长应在3个工作日内完成最终审批
    3. 相关部门应在1个工作日内完成备案
    """
    
    # 获取API配置
    config = get_api_config('sinopec')
    if not config or not config.get('enabled'):
        print("❌ 中石化API未配置或未启用")
        return None
    
    print(f"📋 API配置信息:")
    print(f"   Base URL: {config['base_url']}")
    print(f"   Workflow ID: {config['workflow_id']}")
    print(f"   Stream: {config.get('stream', False)}")
    print(f"   API Key: {config['api_key'][:10]}...")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', encoding='utf-8', delete=False) as temp_file:
        temp_file.write(test_content)
        temp_file_path = temp_file.name
    
    try:
        # 读取文件并编码为base64
        with open(temp_file_path, 'rb') as f:
            file_content = f.read()
            base64_content = base64.b64encode(file_content).decode('utf-8')
        
        print(f"📄 文档内容长度: {len(test_content)} 字符")
        print(f"📄 Base64编码长度: {len(base64_content)} 字符")
        
        # 构建API请求
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "content": [
                {
                    "field_name": "files",
                    "type": "file",
                    "value": base64_content
                }
            ],
            "stream": config.get("stream", False)
        }
        
        api_url = f"{config['base_url']}/{config['workflow_id']}"
        
        print(f"🔗 请求URL: {api_url}")
        print(f"📊 请求数据结构: {list(data.keys())}")
        print(f"📊 Content字段数量: {len(data['content'])}")
        
        # 发起API调用
        print("🚀 发起API调用...")
        response = requests.post(
            api_url,
            headers=headers,
            json=data,
            timeout=120,
            verify=config.get("verify_ssl", True)
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功")
            
            # 详细分析响应结构
            print(f"\n📋 响应结构分析:")
            print(f"   顶层字段: {list(result.keys())}")
            
            if 'data' in result:
                data_keys = list(result['data'].keys()) if isinstance(result['data'], dict) else "非字典类型"
                print(f"   data字段内容: {data_keys}")
                
                # 检查各种可能的内容字段
                data_obj = result['data']
                if isinstance(data_obj, dict):
                    print(f"\n🔍 详细字段检查:")
                    for key, value in data_obj.items():
                        if isinstance(value, str):
                            print(f"   {key}: {type(value)} - 长度{len(value)} - 前50字符: {value[:50]}")
                        else:
                            print(f"   {key}: {type(value)} - {value}")
            
            # 保存原始响应
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            response_file = f"sinopec_response_{timestamp}.json"
            with open(response_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"💾 原始响应已保存到: {response_file}")
            
            return result
            
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            return None
            
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file_path)
        except:
            pass

def test_system_processing():
    """测试系统的处理流程"""
    print("\n🔧 测试系统处理流程...")
    
    test_content = """
    海南炼化公司域长负责制运行管理细则
    
    第一条 申请流程
    员工需要申请域长审批的事项时，应按以下流程进行：
    1. 填写申请表格
    2. 直接主管初步审核
    3. 域长最终审批
    4. 相关部门备案
    
    第二条 审批权限
    域长有权对以下事项进行审批：
    1. 10万元以下的费用支出
    2. 人员调动申请
    3. 设备采购申请
    
    第三条 时间要求
    1. 申请人提交申请后，直接主管应在2个工作日内完成初审
    2. 域长应在3个工作日内完成最终审批
    3. 相关部门应在1个工作日内完成备案
    """
    
    processor = LLMProcessor()
    
    try:
        print("📋 调用系统处理流程...")
        result = processor.extract_process_with_llm(test_content, "sinopec")
        
        if result.get('success'):
            print("✅ 系统处理成功")
            
            # 分析系统处理结果
            print(f"\n📊 系统处理结果分析:")
            print(f"   成功状态: {result.get('success')}")
            print(f"   提供商: {result.get('llm_info', {}).get('provider')}")
            print(f"   处理时间: {result.get('llm_info', {}).get('processing_time')}")
            
            # 检查解析结果
            if 'parsed_result' in result:
                parsed = result['parsed_result']
                print(f"   解析成功: {parsed.get('success')}")
                if parsed.get('success') and 'data' in parsed:
                    data = parsed['data']
                    print(f"   流程名称: {data.get('process_name')}")
                    print(f"   步骤数量: {data.get('total_steps')}")
                    print(f"   复杂度: {data.get('complexity')}")
                    
                    # 显示步骤信息
                    if 'steps' in data and data['steps']:
                        print(f"\n📋 解析出的步骤:")
                        for i, step in enumerate(data['steps'][:5], 1):
                            print(f"   {i}. {step.get('description', '')[:100]}")
                    
                    # 检查是否有原始分析内容
                    if 'original_analysis' in data:
                        original = data['original_analysis']
                        print(f"\n📝 原始分析内容长度: {len(original)} 字符")
                        print(f"   前200字符: {original[:200]}")
                        
                        # 检查是否是回退响应
                        if 'warning' in data:
                            print(f"⚠️ 警告信息: {data['warning']}")
                        if 'recommendation' in data:
                            print(f"💡 建议: {data['recommendation']}")
            
            # 保存系统处理结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            system_result_file = f"system_result_{timestamp}.json"
            with open(system_result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"💾 系统处理结果已保存到: {system_result_file}")
            
            return result
            
        else:
            print(f"❌ 系统处理失败: {result.get('error')}")
            return None
            
    except Exception as e:
        print(f"❌ 系统处理异常: {e}")
        return None

def compare_results(api_result, system_result):
    """对比API直接调用和系统处理的结果"""
    print("\n🔍 结果对比分析...")
    
    if not api_result or not system_result:
        print("❌ 缺少对比数据")
        return
    
    print("📊 对比项目:")
    
    # 1. 响应时间对比
    if system_result.get('llm_info', {}).get('processing_time'):
        print(f"   系统处理时间: {system_result['llm_info']['processing_time']}")
    
    # 2. 内容长度对比
    api_content = ""
    if 'data' in api_result and isinstance(api_result['data'], dict):
        for key, value in api_result['data'].items():
            if isinstance(value, str) and len(value) > 50:
                api_content = value
                break
    
    system_content = ""
    if (system_result.get('parsed_result', {}).get('success') and 
        'original_analysis' in system_result['parsed_result']['data']):
        system_content = system_result['parsed_result']['data']['original_analysis']
    
    print(f"   API原始内容长度: {len(api_content)} 字符")
    print(f"   系统处理内容长度: {len(system_content)} 字符")
    
    # 3. 内容相似性检查
    if api_content and system_content:
        # 简单的相似性检查
        api_words = set(api_content.split())
        system_words = set(system_content.split())
        
        if api_words and system_words:
            intersection = api_words.intersection(system_words)
            union = api_words.union(system_words)
            similarity = len(intersection) / len(union) if union else 0
            print(f"   内容相似度: {similarity:.2%}")
        
        # 检查是否是相同内容
        if api_content.strip() == system_content.strip():
            print("✅ 内容完全一致")
        else:
            print("⚠️ 内容存在差异")
            
            # 显示内容差异
            print(f"\n📋 API原始内容前300字符:")
            print(f"   {api_content[:300]}")
            print(f"\n📋 系统处理内容前300字符:")
            print(f"   {system_content[:300]}")
    
    # 4. 结构化数据对比
    if (system_result.get('parsed_result', {}).get('success') and 
        'steps' in system_result['parsed_result']['data']):
        steps = system_result['parsed_result']['data']['steps']
        print(f"\n📋 系统解析出的流程步骤 ({len(steps)}个):")
        for i, step in enumerate(steps[:10], 1):
            print(f"   {i}. {step.get('description', '')[:80]}")

def generate_improvement_suggestions():
    """生成改进建议"""
    print("\n💡 改进建议:")
    
    suggestions = [
        "1. 检查中石化API是否返回了完整的分析结果",
        "2. 验证API调用参数是否与平台直接运行时一致",
        "3. 考虑调整响应解析逻辑，确保获取到真实的AI分析内容",
        "4. 检查是否需要额外的API调用来获取完整结果",
        "5. 对比不同stream参数设置的影响",
        "6. 验证base64编码的文件内容是否正确",
        "7. 检查工作流配置是否与预期一致"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")

def main():
    """主函数"""
    print("🚀 中石化平台流程解析差异诊断")
    print("=" * 60)
    
    # 1. 直接调用API测试
    api_result = test_sinopec_direct_call()
    
    # 2. 系统处理流程测试
    system_result = test_system_processing()
    
    # 3. 结果对比
    compare_results(api_result, system_result)
    
    # 4. 改进建议
    generate_improvement_suggestions()
    
    print("\n" + "=" * 60)
    print("✅ 诊断完成！")
    
    print("\n📋 诊断总结:")
    if api_result and system_result:
        print("   ✅ 成功获取了API和系统处理的结果")
        print("   📊 可以进行详细的对比分析")
        print("   💾 结果已保存到JSON文件中")
    else:
        print("   ⚠️ 部分测试失败，请检查配置和网络连接")
    
    print("\n🔧 下一步操作建议:")
    print("   1. 查看保存的JSON文件，分析具体差异")
    print("   2. 在中石化平台直接运行相同内容，对比结果")
    print("   3. 根据差异调整系统的处理逻辑")
    print("   4. 考虑使用API查看器页面进行实时对比")

if __name__ == "__main__":
    main() 