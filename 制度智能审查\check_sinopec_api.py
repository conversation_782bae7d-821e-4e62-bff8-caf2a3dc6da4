#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import requests
import tempfile
import os
from api_config import get_api_config

def check_official_requirements():
    """检查官方文档要求与当前实现的对比"""
    print("🔍 中石化API官方文档要求检查")
    print("=" * 60)
    
    print("📋 官方文档要求:")
    print("1. URL: https://agent.ai.sinopec.com/aicoapi/gateway/v2/workflow/api_run/{workflow_id}")
    print("2. Method: POST")
    print("3. Headers: Content-Type: application/json, Authorization: Bearer {api_key}")
    print("4. Body: {\"content\":[{\"field_name\":\"files\",\"type\":\"file\",\"value\":\"\"}],\"stream\":true}")
    print("5. 文件上传: field_name=\"files\", type=\"file\"")
    print("6. 流式输出: stream=true")
    
    print(f"\n📊 当前配置:")
    config = get_api_config("sinopec")
    print(f"- API Key: {config.get('api_key', '')[:10]}...")
    print(f"- Base URL: {config.get('base_url')}")
    print(f"- Workflow ID: {config.get('workflow_id')}")
    print(f"- Input Field: {config.get('input_field')}")
    print(f"- Input Type: {config.get('input_type')}")
    print(f"- Stream: {config.get('stream')}")
    
    return config

def test_file_upload_method():
    """测试正确的文件上传方式"""
    print(f"\n🔧 测试正确的文件上传方式")
    print("=" * 60)
    
    config = get_api_config("sinopec")
    
    # 测试文档内容
    test_content = """采购管理制度

第一条：需求申请
各部门需要采购物品时，应填写《采购申请表》，详细说明采购物品的名称、规格、数量、预算等信息。

第二条：部门审核
部门负责人收到采购申请后，应在2个工作日内完成审核，确认采购的必要性和预算的合理性。

第三条：采购执行
采购部门根据审核通过的申请表，联系供应商进行询价、比价，选择最优方案执行采购。

第四条：验收入库
物品到达后，由申请部门和仓库共同验收，确认物品质量和数量无误后办理入库手续。"""
    
    print(f"📄 测试文档长度: {len(test_content)} 字符")
    
    # 方式1: 直接传输文本内容（当前方式）
    print(f"\n--- 方式1: 直接传输文本内容 ---")
    result1 = test_direct_text_upload(config, test_content)
    
    # 方式2: 创建临时文件上传（推荐方式）
    print(f"\n--- 方式2: 创建临时文件上传 ---")
    result2 = test_file_content_upload(config, test_content)
    
    # 方式3: Base64编码文件内容
    print(f"\n--- 方式3: Base64编码文件内容 ---")
    result3 = test_base64_file_upload(config, test_content)
    
    return result1, result2, result3

def test_direct_text_upload(config, content):
    """测试直接传输文本内容"""
    try:
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "content": [
                {
                    "field_name": "files",
                    "type": "file",
                    "value": content
                }
            ],
            "stream": True
        }
        
        api_url = f"{config['base_url']}/{config['workflow_id']}"
        print(f"🔗 请求URL: {api_url}")
        print(f"📋 请求数据长度: {len(json.dumps(data))} 字符")
        
        response = requests.post(
            api_url,
            headers=headers,
            json=data,
            timeout=30,
            verify=False,
            stream=True
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            content_result = process_stream_response(response)
            print(f"✅ 方式1成功，响应长度: {len(content_result)} 字符")
            return {"success": True, "content": content_result, "method": "direct_text"}
        else:
            print(f"❌ 方式1失败: {response.status_code} - {response.text}")
            return {"success": False, "error": response.text, "method": "direct_text"}
            
    except Exception as e:
        print(f"❌ 方式1异常: {e}")
        return {"success": False, "error": str(e), "method": "direct_text"}

def test_file_content_upload(config, content):
    """测试创建临时文件上传"""
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # 读取文件内容
        with open(temp_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "content": [
                {
                    "field_name": "files",
                    "type": "file", 
                    "value": file_content
                }
            ],
            "stream": True
        }
        
        api_url = f"{config['base_url']}/{config['workflow_id']}"
        print(f"🔗 请求URL: {api_url}")
        print(f"📁 临时文件: {temp_file_path}")
        print(f"📋 文件内容长度: {len(file_content)} 字符")
        
        response = requests.post(
            api_url,
            headers=headers,
            json=data,
            timeout=30,
            verify=False,
            stream=True
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        # 清理临时文件
        os.unlink(temp_file_path)
        
        if response.status_code == 200:
            content_result = process_stream_response(response)
            print(f"✅ 方式2成功，响应长度: {len(content_result)} 字符")
            return {"success": True, "content": content_result, "method": "file_content"}
        else:
            print(f"❌ 方式2失败: {response.status_code} - {response.text}")
            return {"success": False, "error": response.text, "method": "file_content"}
            
    except Exception as e:
        print(f"❌ 方式2异常: {e}")
        return {"success": False, "error": str(e), "method": "file_content"}

def test_base64_file_upload(config, content):
    """测试Base64编码文件内容"""
    try:
        import base64
        
        # Base64编码内容
        content_bytes = content.encode('utf-8')
        base64_content = base64.b64encode(content_bytes).decode('utf-8')
        
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "content": [
                {
                    "field_name": "files",
                    "type": "file",
                    "value": base64_content
                }
            ],
            "stream": True
        }
        
        api_url = f"{config['base_url']}/{config['workflow_id']}"
        print(f"🔗 请求URL: {api_url}")
        print(f"📋 Base64内容长度: {len(base64_content)} 字符")
        
        response = requests.post(
            api_url,
            headers=headers,
            json=data,
            timeout=30,
            verify=False,
            stream=True
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            content_result = process_stream_response(response)
            print(f"✅ 方式3成功，响应长度: {len(content_result)} 字符")
            return {"success": True, "content": content_result, "method": "base64_file"}
        else:
            print(f"❌ 方式3失败: {response.status_code} - {response.text}")
            return {"success": False, "error": response.text, "method": "base64_file"}
            
    except Exception as e:
        print(f"❌ 方式3异常: {e}")
        return {"success": False, "error": str(e), "method": "base64_file"}

def process_stream_response(response):
    """处理流式响应"""
    full_content = ""
    chunk_count = 0
    
    try:
        print("📡 开始处理流式响应...")
        
        for line in response.iter_lines(decode_unicode=True):
            if line:
                chunk_count += 1
                print(f"📦 接收到数据块 {chunk_count}: {line[:100]}...")
                
                if line.startswith('data: '):
                    data_str = line[6:]
                    if data_str.strip() == '[DONE]':
                        print("🏁 流式响应结束")
                        break
                    
                    try:
                        data_obj = json.loads(data_str)
                        print(f"📊 解析JSON成功: {list(data_obj.keys())}")
                        
                        # 根据官方文档提取内容
                        content = extract_content_from_official_format(data_obj)
                        if content:
                            full_content += content
                            print(f"📝 提取内容: {content[:100]}...")
                            
                    except json.JSONDecodeError as e:
                        print(f"⚠️ JSON解析失败: {e}")
                        continue
        
        print(f"📊 流式响应处理完成，总内容长度: {len(full_content)} 字符")
        return full_content
        
    except Exception as e:
        print(f"❌ 流式响应处理异常: {e}")
        return f"流式响应处理失败: {str(e)}"

def extract_content_from_official_format(data_obj):
    """根据官方文档格式提取内容"""
    content = ""
    
    # 根据官方文档，检查返回参数结构
    if "data" in data_obj:
        data = data_obj["data"]
        
        # 检查text数组
        if "text" in data and isinstance(data["text"], list):
            for text_item in data["text"]:
                if isinstance(text_item, str):
                    content += text_item
                elif isinstance(text_item, dict) and "output" in text_item:
                    if text_item["output"]:  # output为true表示最终结果
                        content += str(text_item.get("content", ""))
        
        # 检查直接的文本内容
        elif "text" in data:
            content = str(data["text"])
        
        # 检查其他可能的字段
        elif "output" in data:
            content = str(data["output"])
    
    return content

def analyze_results(results):
    """分析测试结果"""
    print(f"\n📊 测试结果分析")
    print("=" * 60)
    
    successful_methods = []
    failed_methods = []
    
    for i, result in enumerate(results, 1):
        method = result.get("method", f"方式{i}")
        if result.get("success"):
            successful_methods.append(method)
            content_length = len(result.get("content", ""))
            print(f"✅ {method}: 成功，内容长度 {content_length} 字符")
        else:
            failed_methods.append(method)
            error = result.get("error", "未知错误")
            print(f"❌ {method}: 失败，错误: {error[:100]}...")
    
    print(f"\n📋 总结:")
    print(f"✅ 成功的方法: {successful_methods}")
    print(f"❌ 失败的方法: {failed_methods}")
    
    if successful_methods:
        print(f"\n💡 推荐使用: {successful_methods[0]}")
        return successful_methods[0]
    else:
        print(f"\n⚠️ 所有方法都失败了，需要检查API配置或网络连接")
        return None

def main():
    """主函数"""
    print("🏭 中石化API接口详细检查")
    print("=" * 80)
    
    # 检查官方要求
    config = check_official_requirements()
    
    # 测试文档内容
    test_content = """采购管理制度

第一条：需求申请
各部门需要采购物品时，应填写《采购申请表》，详细说明采购物品的名称、规格、数量、预算等信息。

第二条：部门审核
部门负责人收到采购申请后，应在2个工作日内完成审核，确认采购的必要性和预算的合理性。"""
    
    print(f"\n📄 测试文档长度: {len(test_content)} 字符")
    
    # 测试当前实现方式
    print(f"\n--- 测试当前实现方式 ---")
    result = test_direct_text_upload(config, test_content)
    
    print(f"\n📊 测试结果:")
    if result.get("success"):
        print(f"✅ 文件上传成功")
        print(f"✅ 获得返回值，长度: {len(result.get('content', ''))} 字符")
        content = result.get('content', '')
        if content and len(content) > 0:
            print(f"📝 返回内容预览: {content[:200]}...")
        else:
            print(f"⚠️ 返回内容为空")
    else:
        print(f"❌ 文件上传失败: {result.get('error', '未知错误')}")
    
    return result

if __name__ == "__main__":
    main() 