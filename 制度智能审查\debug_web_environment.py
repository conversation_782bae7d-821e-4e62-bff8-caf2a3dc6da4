#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Web应用程序环境中的问题
"""

import sys
import os
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('web_debug')

def debug_web_environment():
    """调试Web环境"""
    try:
        logger.info("=== 调试Web环境 ===")
        
        # 1. 检查Python路径
        logger.info("1. Python路径信息:")
        logger.info(f"  Python可执行文件: {sys.executable}")
        logger.info(f"  Python版本: {sys.version}")
        logger.info(f"  当前工作目录: {os.getcwd()}")
        
        # 2. 检查sys.path
        logger.info("2. sys.path内容:")
        for i, path in enumerate(sys.path):
            logger.info(f"  [{i}] {path}")
        
        # 3. 模拟web_app.py的导入过程
        logger.info("3. 模拟web_app.py的导入过程...")
        
        # 模拟web_app.py中的导入
        try:
            from llm_processor import LLMProcessor, extract_with_llm, get_api_config
            logger.info("✓ 导入成功")
        except Exception as e:
            logger.error(f"✗ 导入失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 4. 模拟web_app.py中的实例创建
        logger.info("4. 模拟web_app.py中的实例创建...")
        try:
            llm = LLMProcessor()
            logger.info("✓ 实例创建成功")
            
            # 检查方法
            if hasattr(llm, '_is_sinopec_api_configured'):
                logger.info("✓ _is_sinopec_api_configured方法存在")
                
                # 调用方法
                result = llm._is_sinopec_api_configured()
                logger.info(f"✓ 方法调用成功: {result}")
            else:
                logger.error("✗ _is_sinopec_api_configured方法不存在")
                
                # 列出所有方法
                methods = [m for m in dir(llm) if not m.startswith('__')]
                logger.info(f"可用方法: {methods}")
                return False
                
        except Exception as e:
            logger.error(f"✗ 实例创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 5. 检查模块缓存
        logger.info("5. 检查模块缓存...")
        if 'llm_processor' in sys.modules:
            module = sys.modules['llm_processor']
            logger.info(f"✓ llm_processor模块在缓存中: {module}")
            logger.info(f"  模块文件: {getattr(module, '__file__', 'N/A')}")
            
            if hasattr(module, 'LLMProcessor'):
                cls = module.LLMProcessor
                logger.info(f"✓ LLMProcessor类: {cls}")
                
                # 检查类的方法
                if hasattr(cls, '_is_sinopec_api_configured'):
                    logger.info("✓ _is_sinopec_api_configured方法在类中存在")
                else:
                    logger.error("✗ _is_sinopec_api_configured方法在类中不存在")
                    class_methods = [m for m in dir(cls) if not m.startswith('__')]
                    logger.info(f"类方法: {class_methods}")
            else:
                logger.error("✗ LLMProcessor类不在模块中")
        else:
            logger.error("✗ llm_processor模块不在缓存中")
        
        logger.info("=== Web环境调试完成 ===")
        return True
        
    except Exception as e:
        logger.error(f"调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_web_environment()
    if success:
        print("Web环境调试成功!")
    else:
        print("Web环境调试失败!")
        sys.exit(1) 