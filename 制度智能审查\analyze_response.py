import json
import os
import re
import logging
from typing import Dict, Any, List, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('analyzer')

class ResponseAnalyzer:
    """响应分析器，分析API响应并提取有用信息"""
    
    def __init__(self):
        """初始化分析器"""
        pass
    
    def load_file(self, file_path: str) -> str:
        """从文件加载数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"已加载文件: {file_path}, 大小: {len(content)} 字符")
            return content
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            return ""
    
    def extract_workflow_finished(self, text: str) -> Optional[Dict[str, Any]]:
        """尝试提取workflow_finished事件"""
        try:
            # 尝试使用正则表达式查找workflow_finished事件
            pattern = r'event":\s*"workflow_finished".*?"data":\s*(\{.*\})'
            match = re.search(pattern, text, re.DOTALL)
            
            if match:
                data_str = match.group(1)
                # 修复可能的JSON格式问题
                data_str = data_str.replace('\\n', '\\\\n').replace('\\t', '\\\\t')
                
                # 尝试解析为JSON
                try:
                    data = json.loads(data_str)
                    logger.info(f"成功提取workflow_finished事件数据")
                    return data
                except json.JSONDecodeError:
                    logger.warning(f"解析workflow_finished事件数据失败")
            
            logger.warning("未找到workflow_finished事件")
            return None
        except Exception as e:
            logger.error(f"提取workflow_finished事件失败: {e}")
            return None
    
    def extract_text_content(self, text: str) -> str:
        """尝试从响应中提取文本内容"""
        try:
            # 尝试多种方式提取文本内容
            
            # 1. 尝试提取workflow_finished事件中的文本呈现字段
            workflow_data = self.extract_workflow_finished(text)
            if workflow_data and "文本呈现" in workflow_data:
                content = workflow_data["文本呈现"]
                logger.info(f"从workflow_finished事件中提取到文本呈现, 长度: {len(content)} 字符")
                return content
            
            # 2. 尝试使用正则表达式直接查找文本呈现字段
            pattern = r'"文本呈现":\s*"(.*?)"(?:,|$)'
            match = re.search(pattern, text, re.DOTALL)
            if match:
                content = match.group(1)
                # 处理转义字符
                content = content.replace('\\n', '\n').replace('\\t', '\t').replace('\\"', '"')
                logger.info(f"使用正则表达式提取到文本呈现, 长度: {len(content)} 字符")
                return content
            
            # 3. 尝试查找node_chunk事件中的文本
            chunks = []
            chunk_pattern = r'"event":\s*"node_chunk".*?"data".*?"choices".*?"delta".*?"content":\s*"(.*?)"'
            for match in re.finditer(chunk_pattern, text, re.DOTALL):
                chunk = match.group(1)
                chunk = chunk.replace('\\n', '\n').replace('\\t', '\t').replace('\\"', '"')
                chunks.append(chunk)
            
            if chunks:
                content = ''.join(chunks)
                logger.info(f"从node_chunk事件中提取到文本, 长度: {len(content)} 字符")
                return content
            
            logger.warning("未能提取到文本内容")
            return ""
        except Exception as e:
            logger.error(f"提取文本内容失败: {e}")
            return ""
    
    def extract_process_steps(self, text: str) -> List[Dict[str, str]]:
        """从文本中提取流程步骤"""
        steps = []
        try:
            # 查找流程步骤部分
            # 多种模式匹配，提高兼容性
            
            # 查找"审批流程环节"部分
            process_section = None
            patterns = [
                r'审批流程环节.*?\n(.*?)(?:\n\n---|\n\n###|$)',
                r'流程环节.*?\n(.*?)(?:\n\n---|\n\n###|$)',
                r'[0-9]+\.\s+\*\*([^*]+)\*\*'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text, re.DOTALL)
                if match:
                    if pattern.startswith('[0-9]+'):
                        # 直接搜索步骤的模式
                        process_section = text
                    else:
                        # 搜索章节的模式
                        process_section = match.group(1)
                    break
            
            if process_section:
                # 提取步骤
                # 尝试多种步骤格式模式
                step_patterns = [
                    # 模式1: 数字.空格**步骤名称**
                    re.compile(r'(\d+)\.\s+\*\*([^*]+)\*\*'),
                    # 模式2: 数字.空格步骤名称
                    re.compile(r'(\d+)\.\s+([^\n]+)'),
                    # 模式3: **步骤名称**(数字)
                    re.compile(r'\*\*([^*]+)\*\*\s*\((\d+)\)')
                ]
                
                for step_pattern in step_patterns:
                    steps_matches = step_pattern.finditer(process_section)
                    steps_found = False
                    
                    for match in steps_matches:
                        steps_found = True
                        if len(match.groups()) >= 2:
                            if step_pattern.pattern.startswith(r'(\d+)'):
                                # 模式1和2
                                step_number = match.group(1)
                                step_name = match.group(2).strip()
                            else:
                                # 模式3
                                step_name = match.group(1).strip()
                                step_number = match.group(2)
                            
                            # 查找引用原文
                            original_text = ""
                            try:
                                # 尝试多种引用格式
                                quote_patterns = [
                                    re.compile(r'\*\*' + re.escape(step_name) + r'\*\*.*?>\s+([^;]+);', re.DOTALL),
                                    re.compile(r'\*\*' + re.escape(step_name) + r'\*\*.*?`([^`]+)`', re.DOTALL),
                                    re.compile(r'' + re.escape(step_name) + r'.*?引用原文.*?[`\'"]([^`\'"]+)[`\'"]', re.DOTALL)
                                ]
                                
                                for quote_pattern in quote_patterns:
                                    quote_match = quote_pattern.search(process_section)
                                    if quote_match:
                                        original_text = quote_match.group(1).strip()
                                        break
                            except Exception as e:
                                logger.warning(f"提取引用原文失败: {e}")
                            
                            steps.append({
                                "step_number": step_number,
                                "step_name": step_name,
                                "original_text": original_text
                            })
                    
                    # 如果找到了步骤，不再尝试其他模式
                    if steps_found:
                        break
                
                # 如果没有找到步骤，尝试直接查找数字序号的行
                if not steps:
                    line_pattern = re.compile(r'(\d+)[\.、]\s*(.+)')
                    for line in process_section.split('\n'):
                        match = line_pattern.match(line.strip())
                        if match:
                            step_number = match.group(1)
                            step_name = match.group(2).strip()
                            
                            steps.append({
                                "step_number": step_number,
                                "step_name": step_name,
                                "original_text": ""
                            })
            
            logger.info(f"提取到 {len(steps)} 个流程步骤")
        except Exception as e:
            logger.error(f"提取流程步骤失败: {e}")
        
        return steps
    
    def extract_departments_table(self, text: str) -> List[Dict[str, str]]:
        """从文本中提取部门职责表"""
        departments = []
        try:
            # 查找部门职责表部分
            # 多种模式匹配，提高兼容性
            
            # 查找表格部分
            table_section = None
            patterns = [
                r'部门职责节点表.*?\n\n\|(.*?)(?:\n\n---|\n\n###|$)',
                r'流程参与部门.*?\n\n\|(.*?)(?:\n\n---|\n\n###|$)',
                r'部门职责表.*?\n\n\|(.*?)(?:\n\n---|\n\n###|$)',
                r'\|\s*流程环节\s*\|(.*?)(?:\n\n---|\n\n###|$)'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text, re.DOTALL)
                if match:
                    table_section = "|\n|" + match.group(1)
                    break
            
            if not table_section and '|' in text:
                # 尝试直接查找表格
                table_lines = []
                capture = False
                
                for line in text.split('\n'):
                    if '|' in line and ('流程环节' in line or '步骤' in line or '节点' in line):
                        capture = True
                        table_lines.append(line)
                    elif capture and '|' in line:
                        table_lines.append(line)
                    elif capture and not line.strip():
                        # 空行结束表格
                        break
                
                if table_lines:
                    table_section = '\n'.join(table_lines)
            
            if table_section:
                # 解析表格
                rows = table_section.strip().split('\n')
                
                # 识别表头，找到列索引
                header = rows[0] if rows else ""
                step_col = -1
                dept_col = -1
                role_col = -1
                
                # 检查表头中的列名
                headers = [h.strip() for h in header.split('|')]
                for i, h in enumerate(headers):
                    h_lower = h.lower()
                    if any(term in h_lower for term in ['流程环节', '步骤', '节点']):
                        step_col = i
                    elif any(term in h_lower for term in ['负责部门', '责任部门', '部门']):
                        dept_col = i
                    elif any(term in h_lower for term in ['职责类型', '角色', '职责']):
                        role_col = i
                
                # 跳过表头和分隔行
                data_rows = [r for r in rows if r and '---' not in r]
                if len(data_rows) > 1:
                    data_rows = data_rows[1:]
                
                for row in data_rows:
                    cols = [col.strip() for col in row.split('|')]
                    if len(cols) > max(step_col, dept_col, role_col):
                        try:
                            step_name = cols[step_col] if step_col >= 0 else ""
                            department = cols[dept_col] if dept_col >= 0 else ""
                            role_type = cols[role_col] if role_col >= 0 else ""
                            
                            if step_name and department:
                                departments.append({
                                    "step_name": step_name,
                                    "department": department,
                                    "role_type": role_type
                                })
                        except Exception as e:
                            logger.warning(f"解析表格行失败: {e}")
            
            logger.info(f"提取到 {len(departments)} 个部门职责")
        except Exception as e:
            logger.error(f"提取部门职责表失败: {e}")
        
        return departments
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """分析文件内容"""
        result = {
            "success": False,
            "file_path": file_path,
            "text_content": "",
            "process_steps": [],
            "departments": [],
            "errors": []
        }
        
        try:
            # 加载文件
            raw_content = self.load_file(file_path)
            if not raw_content:
                result["errors"].append("文件为空或加载失败")
                return result
            
            # 提取文本内容
            text_content = self.extract_text_content(raw_content)
            result["text_content"] = text_content
            
            if text_content:
                # 提取流程步骤
                process_steps = self.extract_process_steps(text_content)
                result["process_steps"] = process_steps
                
                # 提取部门职责表
                departments = self.extract_departments_table(text_content)
                result["departments"] = departments
                
                result["success"] = True
            else:
                result["errors"].append("未能提取到文本内容")
        
        except Exception as e:
            logger.error(f"分析文件失败: {e}")
            result["errors"].append(f"分析异常: {str(e)}")
        
        return result

def main():
    """主函数"""
    # 设置参数
    input_file = "test_outputs/raw_response_20250619_150524.txt"
    output_file = "test_outputs/analyzed_result.json"
    
    # 创建分析器
    analyzer = ResponseAnalyzer()
    
    # 分析文件
    result = analyzer.analyze_file(input_file)
    
    # 保存结果
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        logger.info(f"分析结果已保存到 {output_file}")
        
        # 显示分析摘要
        steps_count = len(result["process_steps"])
        depts_count = len(result["departments"])
        
        print("\n分析摘要:")
        print(f"- 文本内容长度: {len(result['text_content'])} 字符")
        print(f"- 提取到流程步骤: {steps_count} 个")
        print(f"- 提取到部门职责: {depts_count} 个")
        
        if steps_count > 0:
            print("\n流程步骤:")
            for step in result["process_steps"]:
                print(f"  {step['step_number']}. {step['step_name']}")
        
        if depts_count > 0:
            print("\n部门职责:")
            for dept in result["departments"]:
                print(f"  - {dept['step_name']}: {dept['department']} ({dept['role_type']})")
        
        if result["errors"]:
            print("\n错误:")
            for error in result["errors"]:
                print(f"  - {error}")
    
    except Exception as e:
        logger.error(f"保存分析结果失败: {e}")
    
    return 0

if __name__ == "__main__":
    main()