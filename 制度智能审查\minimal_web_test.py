#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小的Web应用程序测试LLMProcessor
"""

from flask import Flask, request, jsonify
import logging
import traceback

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('minimal_web')

app = Flask(__name__)

@app.route('/')
def index():
    return "最小Web测试应用程序"

@app.route('/test_llm_processor', methods=['POST'])
def test_llm_processor():
    """测试LLMProcessor"""
    try:
        logger.info("开始测试LLMProcessor...")
        
        # 1. 导入
        logger.info("1. 导入LLMProcessor...")
        from llm_processor import LLMProcessor
        logger.info("✓ 导入成功")
        
        # 2. 创建实例
        logger.info("2. 创建LLMProcessor实例...")
        processor = LLMProcessor()
        logger.info("✓ 实例创建成功")
        
        # 3. 检查方法
        logger.info("3. 检查_is_sinopec_api_configured方法...")
        if hasattr(processor, '_is_sinopec_api_configured'):
            logger.info("✓ _is_sinopec_api_configured方法存在")
            
            # 调用方法
            config_status = processor._is_sinopec_api_configured()
            logger.info(f"✓ API配置状态: {config_status}")
        else:
            logger.error("✗ _is_sinopec_api_configured方法不存在")
            methods = [m for m in dir(processor) if not m.startswith('__')]
            logger.error(f"可用方法: {methods}")
            return jsonify({
                'success': False,
                'error': '_is_sinopec_api_configured方法不存在',
                'available_methods': methods
            })
        
        # 4. 测试extract_process_with_llm方法
        test_text = request.json.get('text', '测试文本') if request.is_json else '测试文本'
        logger.info(f"4. 测试extract_process_with_llm方法，文本: {test_text[:50]}...")
        
        result = processor.extract_process_with_llm(test_text)
        logger.info(f"✓ extract_process_with_llm调用成功")
        
        return jsonify({
            'success': True,
            'message': 'LLMProcessor测试成功',
            'config_status': config_status,
            'llm_result': {
                'success': result.get('success', False),
                'message': result.get('message', ''),
                'has_data': bool(result.get('data'))
            }
        })
        
    except Exception as e:
        logger.error(f"✗ LLMProcessor测试失败: {e}")
        error_trace = traceback.format_exc()
        logger.error(f"详细错误: {error_trace}")
        
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': error_trace
        })

if __name__ == '__main__':
    logger.info("启动最小Web测试应用程序...")
    app.run(host='0.0.0.0', port=5001, debug=True) 