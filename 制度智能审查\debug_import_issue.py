#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试导入和初始化问题
"""

import sys
import os
import logging
import traceback

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('debug')

def debug_imports():
    """调试导入问题"""
    try:
        logger.info("=== 开始调试导入问题 ===")
        
        # 1. 检查模块是否存在
        logger.info("1. 检查llm_processor.py文件是否存在...")
        if os.path.exists('llm_processor.py'):
            logger.info("✓ llm_processor.py文件存在")
        else:
            logger.error("✗ llm_processor.py文件不存在")
            return False
        
        # 2. 尝试导入模块
        logger.info("2. 尝试导入llm_processor模块...")
        try:
            import llm_processor
            logger.info("✓ llm_processor模块导入成功")
        except Exception as e:
            logger.error(f"✗ llm_processor模块导入失败: {e}")
            traceback.print_exc()
            return False
        
        # 3. 检查LLMProcessor类是否存在
        logger.info("3. 检查LLMProcessor类是否存在...")
        if hasattr(llm_processor, 'LLMProcessor'):
            logger.info("✓ LLMProcessor类存在")
        else:
            logger.error("✗ LLMProcessor类不存在")
            return False
        
        # 4. 尝试创建LLMProcessor实例
        logger.info("4. 尝试创建LLMProcessor实例...")
        try:
            processor = llm_processor.LLMProcessor()
            logger.info("✓ LLMProcessor实例创建成功")
        except Exception as e:
            logger.error(f"✗ LLMProcessor实例创建失败: {e}")
            traceback.print_exc()
            return False
        
        # 5. 检查实例的属性和方法
        logger.info("5. 检查实例的属性和方法...")
        
        # 检查config属性
        if hasattr(processor, 'config'):
            logger.info(f"✓ config属性存在: {type(processor.config)}")
        else:
            logger.error("✗ config属性不存在")
        
        # 检查_is_sinopec_api_configured方法
        if hasattr(processor, '_is_sinopec_api_configured'):
            logger.info("✓ _is_sinopec_api_configured方法存在")
            
            # 尝试调用方法
            try:
                result = processor._is_sinopec_api_configured()
                logger.info(f"✓ _is_sinopec_api_configured方法调用成功，返回: {result}")
            except Exception as e:
                logger.error(f"✗ _is_sinopec_api_configured方法调用失败: {e}")
                traceback.print_exc()
                return False
        else:
            logger.error("✗ _is_sinopec_api_configured方法不存在")
            
            # 列出所有可用的方法
            methods = [method for method in dir(processor) if not method.startswith('__')]
            logger.info(f"可用的方法: {methods}")
            return False
        
        # 6. 测试extract_process_with_llm方法
        logger.info("6. 测试extract_process_with_llm方法...")
        if hasattr(processor, 'extract_process_with_llm'):
            logger.info("✓ extract_process_with_llm方法存在")
            
            # 尝试调用方法（使用简单的测试文本）
            try:
                test_text = "测试文本"
                result = processor.extract_process_with_llm(test_text)
                logger.info(f"✓ extract_process_with_llm方法调用成功")
                logger.info(f"  结果类型: {type(result)}")
                logger.info(f"  结果成功状态: {result.get('success', 'N/A')}")
            except Exception as e:
                logger.error(f"✗ extract_process_with_llm方法调用失败: {e}")
                traceback.print_exc()
                return False
        else:
            logger.error("✗ extract_process_with_llm方法不存在")
            return False
        
        logger.info("=== 所有测试通过 ===")
        return True
        
    except Exception as e:
        logger.error(f"调试过程中出现意外错误: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_imports()
    if success:
        print("调试成功，所有组件正常工作!")
    else:
        print("调试失败，发现问题!")
        sys.exit(1) 