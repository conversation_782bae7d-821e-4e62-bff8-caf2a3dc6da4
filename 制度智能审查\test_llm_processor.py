#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM处理器
"""

import os
import sys
import json
import logging
from llm_processor import extract_with_llm, LLMProcessor
from api_config import get_api_config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test')

def test_extract_with_llm():
    """测试extract_with_llm函数"""
    # 测试文本
    test_text = """
    海南炼化公司域长负责制运行管理细则

    第一章 总则
    第一条 为规范海南炼化公司域长负责制运行，保障生产装置安全平稳运行，特制定本细则。

    第二章 审批流程
    第五条 域长变更审批流程如下：
    （一）域长提出变更申请；
    （二）部门主管进行初审；
    （三）安全生产部进行合规性审核；
    （四）总经理办公会审批；
    （五）人力资源部备案。
    """
    
    logger.info("开始测试extract_with_llm函数")
    logger.info(f"测试文本长度: {len(test_text)}")
    
    # 调用函数
    result = extract_with_llm(test_text, "sinopec")
    
    # 输出结果
    logger.info(f"调用结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 保存响应到文件
    with open('test_llm_response.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    logger.info(f"响应已保存到 test_llm_response.json")
    
    return result

def test_llm_processor_direct():
    """直接测试LLMProcessor类"""
    # 测试文本
    test_text = """
    海南炼化公司域长负责制运行管理细则

    第一章 总则
    第一条 为规范海南炼化公司域长负责制运行，保障生产装置安全平稳运行，特制定本细则。

    第二章 审批流程
    第五条 域长变更审批流程如下：
    （一）域长提出变更申请；
    （二）部门主管进行初审；
    （三）安全生产部进行合规性审核；
    （四）总经理办公会审批；
    （五）人力资源部备案。
    """
    
    logger.info("开始直接测试LLMProcessor类")
    
    # 创建处理器实例
    processor = LLMProcessor()
    
    # 检查配置
    config = get_api_config('sinopec')
    logger.info(f"API配置: {json.dumps(config, ensure_ascii=False, indent=2)}")
    
    # 检查API配置状态
    is_configured = processor._is_sinopec_api_configured()
    logger.info(f"API配置状态: {'已配置' if is_configured else '未配置'}")
    
    if not is_configured:
        logger.error("API未配置，无法继续测试")
        return
    
    # 调用处理方法
    result = processor.extract_process_with_llm(test_text)
    
    # 输出结果
    logger.info(f"处理结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 保存响应到文件
    with open('test_processor_response.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    logger.info(f"响应已保存到 test_processor_response.json")
    
    return result

if __name__ == "__main__":
    # 测试extract_with_llm函数
    test_extract_with_llm()
    
    # 直接测试LLMProcessor类
    test_llm_processor_direct() 