import datetime
import requests
from lxml import html
import pandas as pd
import sys

# 替换为实际目录路径
module_path = "e:/Project/path/to/some_module_directory"
if module_path not in sys.path:
    sys.path.append(module_path)

try:
    import some_module
    print(f"成功导入 some_module，模块内容: {dir(some_module)}")
    from some_module import DeepSeek
except ImportError as e:
    print(f"导入失败，错误信息: {e}")
    print("请确保 DeepSeek 类所在的模块已正确导入")
    sys.exit(1)

def analysis_chemical_product_price(focus, url):
    today = datetime.datetime.now().strftime("%Y-%m-%d")

    response = requests.get(url)
    response.encoding = 'utf8'
    tree = html.fromstring(response.text)

    # 爬取图片
    img_urls = tree.xpath('/html/body/div[5]/div[1]/div[1]/div/img/@src')[0]
    f_img = f"![图片]({img_urls}) "
    # print(f_img)

    # 能源商品涨跌榜
    content = []
    len_li_flu = len(tree.xpath('/html/body/div[5]/div[1]/div[2]/div[2]/div[2]/ul/li'))
    for i in range(len_li_flu):
        brand_name = tree.xpath(f'/html/body/div[5]/div[1]/div[2]/div[2]/div[2]/ul/li[{i + 1}]/div[1]/a/text()')[0]
        price = tree.xpath(f'/html/body/div[5]/div[1]/div[2]/div[2]/div[2]/ul/li[{i + 1}]/div[2]/text()')[0]
        fluctuation = tree.xpath(f'/html/body/div[5]/div[1]/div[2]/div[2]/div[2]/ul/li[{i + 1}]/div[3]/text()')[0]
        content.append([brand_name, price, fluctuation])
    data = pd.DataFrame(content, columns=['商品名称', '价格', '涨跌幅'])
    mkform = data.to_markdown(index=False)

    prompt = "你是一名化工行业的数据分析师，根据：\n" + mkform + "\n的数据生成一段分析概要，字数要求200以内"
    price_analysis = DeepSeek("***********************************",
                              "https://api.deepseek.com/v1/chat/completions").call_deepseek(prompt)

    # 项目展示
    len_li_item = len(tree.xpath('/html/body/div[5]/div[2]/div[1]/ul/li'))
    items = []
    for i in range(len_li_item):
        date = tree.xpath(f'/html/body/div[5]/div[2]/div[1]/ul/li[1]/span/text()')[0].split()[-1]
        if date != today:
            break
        title = tree.xpath(f'/html/body/div[5]/div[2]/div[1]/ul/li[{i + 1}]/a/text()')[0]
        link = tree.xpath(f'/html/body/div[5]/div[2]/div[1]/ul/li[{i + 1}]/a/@href')[0]
        items.append([title, "https://news.chemnet.com" + link, date])
    new_data = pd.DataFrame(items, columns=['标题', '链接', '日期'])
    markdown_table = new_data.to_markdown(index=False)

    result = "## " + focus + "\n" + f_img + "\n" + mkform + "\n" + price_analysis + "\n"
    print("-----" + focus + "爬取完成")
    return result, result + markdown_table + "\n"