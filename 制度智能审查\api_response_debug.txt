API响应调试 - 2025-06-19 09:20:42
============================================================
请求URL: https://agent.ai.sinopec.com/aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349
请求数据: {
  "content": [
    {
      "field_name": "files",
      "type": "file",
      "value": "公司采购管理制度审批流程：\n    1. 申请部门提交采购需求申请\n    2. 部门负责人进行初步审核\n    3. 采购部门评估供应商和价格\n    4. 财务部门审核预算和资金\n    5. 分管副总经理审批\n    6. 总经理最终签字确认"
    }
  ],
  "stream": true
}
响应状态码: 200
总事件数: 2
事件类型分布: {'workflow_start': 1, 'workflow_finished': 1}

原始响应:
event:ADD
data:{"event": "workflow_start", "session_id": "ff6d0b5b3cc747f3a2d6b7f16f570958", "step_id": null, "created": 1750296041675, "data": {}, "url_list": [], "search_results": [], "record_id": "ff6d0b5b3cc747f3a2d6b7f16f570958"}
event:FINISH
data:{"event": "workflow_finished", "session_id": "ff6d0b5b3cc747f3a2d6b7f16f570958", "step_id": null, "created": 1750296042698, "data": {"usage": {"max_token": "8k"}}, "url_list": [], "search_results": [], "record_id": "ff6d0b5b3cc747f3a2d6b7f16f570958", "usage": {"max_token": "8k"}}


解析后的事件:

事件 1:
{
  "event": "workflow_start",
  "session_id": "ff6d0b5b3cc747f3a2d6b7f16f570958",
  "step_id": null,
  "created": 1750296041675,
  "data": {},
  "url_list": [],
  "search_results": [],
  "record_id": "ff6d0b5b3cc747f3a2d6b7f16f570958"
}

事件 2:
{
  "event": "workflow_finished",
  "session_id": "ff6d0b5b3cc747f3a2d6b7f16f570958",
  "step_id": null,
  "created": 1750296042698,
  "data": {
    "usage": {
      "max_token": "8k"
    }
  },
  "url_list": [],
  "search_results": [],
  "record_id": "ff6d0b5b3cc747f3a2d6b7f16f570958",
  "usage": {
    "max_token": "8k"
  }
}
