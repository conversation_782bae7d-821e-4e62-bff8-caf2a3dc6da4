#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中石化专用LLM处理器 - 仅支持中石化API
"""

import json
import logging
import re
import requests
import time
from typing import Dict, Any, List
from api_config import get_api_config

# 配置日志
logging.basicConfig(level=logging.INFO)
llm_logger = logging.getLogger('llm')

class LLMProcessor:
    """中石化专用LLM处理器"""
    
    def __init__(self):
        # 初始化配置
        try:
            self.config = get_api_config('sinopec')
            llm_logger.info("LLMProcessor初始化完成，已加载配置")
        except Exception as e:
            llm_logger.error(f"LLMProcessor初始化配置失败: {e}")
            self.config = {}  # 确保config至少是一个空字典
    
    def _is_sinopec_api_configured(self):
        """检查中石化API是否已配置"""
        try:
            # 确保config已经初始化
            if not hasattr(self, 'config') or self.config is None:
                llm_logger.warning("配置未初始化，尝试重新加载")
                self.config = get_api_config('sinopec')
                
            if not self.config:
                llm_logger.warning("未找到中石化API配置")
                return False
            
            # 检查必要的配置项
            required_keys = ['api_key', 'base_url', 'workflow_id', 'enabled']
            for key in required_keys:
                if key not in self.config or not self.config[key]:
                    llm_logger.warning(f"缺少必要的配置项: {key}")
                    return False
            
            # 检查API是否启用
            if not self.config.get('enabled', False):
                llm_logger.warning("中石化API未启用")
                return False
                
            llm_logger.info("中石化API配置验证通过")
            return True
        except Exception as e:
            llm_logger.error(f"检查API配置时出错: {e}")
            return False
    
    def extract_process_with_llm(self, text: str, provider: str = "sinopec") -> Dict[str, Any]:
        """
        使用中石化LLM提取流程信息
        
        Args:
            text: 要分析的文档文本
            provider: 固定为"sinopec"
        
        Returns:
            包含提取结果的字典
        """
        start_time = time.time()
        
        try:
            # 重新加载最新配置
            self.config = get_api_config('sinopec')
            
            llm_logger.info(f"开始使用中石化LLM提取流程")
            llm_logger.info(f"发送文档内容，长度: {len(text)} 字符")
            
            # 调用中石化API（会返回实际发送的内容和AI响应）
            api_result = self._call_sinopec(text)
            
            # 如果返回的是元组，包含发送的内容和响应
            if isinstance(api_result, tuple):
                sent_content, raw_response = api_result
            else:
                sent_content = text
                raw_response = api_result
            
            processing_time = time.time() - start_time
            llm_logger.info(f"中石化LLM响应获取成功，耗时: {processing_time:.2f}秒，响应长度: {len(raw_response)} 字符")
            
            # 解析响应
            result = self._parse_response(raw_response, text)
            
            if result["success"]:
                llm_logger.info("流程提取完成")
                
                # 添加LLM分析信息
                result["llm_info"] = {
                    "provider": "sinopec",
                    "processing_time": f"{processing_time:.2f}秒",
                    "response_length": len(raw_response),
                    "content_length": len(text),
                    "content_type": "文档内容"
                }
                
                result["llm_analysis"] = {
                    "content": text,  # 用户原始文档内容
                    "content_type": "用户上传的文档内容",
                    "sent_to_ai": sent_content,  # 实际发送给AI的内容
                    "raw_response": raw_response,
                    "analysis_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    'note': "中石化AI平台分析结果"
                }
                
            return result
            
        except Exception as e:
            llm_logger.error(f"中石化LLM提取过程出错: {e}")
            return {
                "success": False,
                "message": f"中石化LLM处理失败: {str(e)}",
                "data": {}
            }
    
    def _call_sinopec(self, document_content: str, use_stream=True) -> tuple:
        """调用中石化API"""
        try:
            if not self._is_sinopec_api_configured():
                raise ValueError("中石化API未配置或未启用")
            
            # 获取API配置
            api_key = self.config.get('api_key', '')
            base_url = self.config.get('base_url', '')
            workflow_id = self.config.get('workflow_id', '')
            input_field = self.config.get('input_field', 'test')
            input_type = self.config.get('input_type', 'input')
            verify_ssl = self.config.get('verify_ssl', False)  # 获取SSL验证设置
            
            # 构建API URL
            api_url = f"{base_url}/{workflow_id}"  # 修复URL格式
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {api_key}'
            }
            
            # 构建请求数据
            data = {
                "content": [
                    {
                        "field_name": input_field,
                        "type": input_type,
                        "value": document_content
                    }
                ],
                "stream": use_stream
            }
            
            # 发送API请求，禁用SSL证书验证
            response = requests.post(api_url, headers=headers, json=data, stream=use_stream, verify=verify_ssl)
            
            # 检查响应状态
            if response.status_code != 200:
                raise ValueError(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
            
            # 处理流式响应
            if use_stream:
                full_response = ""
                all_chunks = []
                
                for line in response.iter_lines():
                    if not line:
                        continue
                        
                    line_str = line.decode('utf-8')
                    
                    # 跳过SSE前缀
                    if line_str.startswith('data: '):
                        line_str = line_str[6:]
                    
                    # 保存所有块用于后续处理
                    all_chunks.append(line_str)
                    
                    # 处理特殊情况：完成事件
                    if '"event": "workflow_finished"' in line_str or '"event": "workflow_finish"' in line_str:
                        try:
                            chunk_data = json.loads(line_str)
                            
                            # 检查是否为工作流完成事件
                            if chunk_data.get("event") == "workflow_finished":
                                # 直接检查data中的文本呈现字段
                                if "data" in chunk_data and "文本呈现" in chunk_data["data"]:
                                    final_result = chunk_data["data"]["文本呈现"]
                                    if final_result:
                                        return document_content, final_result
                        except Exception as e:
                            # 解析失败，继续累积响应
                            pass
                    
                    # 累积响应
                    full_response += line_str
                    
                # 流式响应处理完成后，尝试提取有效内容
                try:
                    # 尝试解析完整响应
                    if full_response:
                        parsed_response = self._parse_stream_response(full_response)
                        return document_content, parsed_response
                except Exception as e:
                    # 如果解析失败，返回原始响应
                    return document_content, full_response
            else:
                # 非流式响应
                return document_content, response.text
                
        except Exception as e:
            raise Exception(f"调用中石化API失败: {str(e)}")
    
    def _parse_stream_response(self, response_text):
        """解析流式响应"""
        try:
            # 尝试分割成多个JSON对象
            chunks = []
            current_chunk = ""
            
            # 处理可能的多行JSON或SSE格式
            for line in response_text.split('\n'):
                line = line.strip()
                
                # 跳过空行
                if not line:
                    continue
                    
                # 处理SSE格式
                if line.startswith('data: '):
                    line = line[6:]
                
                # 尝试解析当前行为JSON
                try:
                    chunk_data = json.loads(line)
                    chunks.append(chunk_data)
                except:
                    # 如果不是有效JSON，添加到当前块
                    current_chunk += line
                    
                    # 尝试解析累积的块
                    try:
                        chunk_data = json.loads(current_chunk)
                        chunks.append(chunk_data)
                        current_chunk = ""
                    except:
                        # 继续累积
                        pass
            
            # 处理收集到的所有块
            final_result = ""
            full_content = ""
            
            # 优先从后往前查找workflow_finished事件
            for chunk in reversed(chunks):
                # 检查是否为工作流完成事件
                if chunk.get("event") == "workflow_finished":
                    # 直接检查data中的文本呈现字段
                    if "data" in chunk and "文本呈现" in chunk["data"]:
                        final_result = chunk["data"]["文本呈现"]
                        if final_result:
                            return final_result
            
            # 如果没有找到workflow_finished事件，尝试其他方式
            for chunk in chunks:
                # 尝试从不同位置提取结果
                
                # 中石化特有的文本呈现字段
                if "data" in chunk and "文本呈现" in chunk["data"]:
                    final_result = chunk["data"]["文本呈现"]
                
                # 尝试从output字段提取
                elif 'output' in chunk:
                    final_result = chunk['output']
                    
                # 尝试从text_rendering字段提取
                elif 'text_rendering' in chunk:
                    final_result = chunk['text_rendering']
                    
                # 尝试从text数组提取
                elif 'text' in chunk and isinstance(chunk['text'], list):
                    final_result = ''.join(chunk['text'])
                    
                # 如果找到了结果，添加到完整内容
                if final_result:
                    full_content += final_result
            
            # 如果找到了内容，返回
            if full_content:
                return full_content
                
            # 如果没有找到内容，返回原始响应
            return response_text
            
        except Exception as e:
            # 解析失败，返回原始响应
            return response_text
    
    def _parse_response(self, response: str, original_text: str) -> Dict[str, Any]:
        """解析中石化AI响应"""
        try:
            # 尝试解析JSON响应
            try:
                # 如果是JSON字符串，先解析成对象
                if isinstance(response, str) and (response.strip().startswith('{') or response.strip().startswith('[')):
                    result_obj = json.loads(response)
                else:
                    result_obj = response
                
                # 检查是否有文本呈现字段
                content = None
                
                # 如果是字典类型，尝试提取文本呈现
                if isinstance(result_obj, dict):
                    # 检查是否有event字段，判断是否为workflow_finished事件
                    if result_obj.get("event") == "workflow_finished" and "data" in result_obj:
                        # 直接从data中提取文本呈现
                        if "文本呈现" in result_obj["data"]:
                            content = result_obj["data"]["文本呈现"]
                    
                    # 如果没有找到，尝试其他可能的位置
                    if not content and "data" in result_obj:
                        data = result_obj["data"]
                        if isinstance(data, dict):
                            # 尝试从data.data.文本呈现提取
                            if "data" in data and isinstance(data["data"], dict) and "文本呈现" in data["data"]:
                                content = data["data"]["文本呈现"]
                
                # 如果找到了内容，构建成功响应
                if content:
                    return {
                        "success": True,
                        "data": {
                            "raw_response": response,
                            "original_text": original_text,
                            "content": content
                        },
                        "message": "成功提取流程内容"
                    }
            except Exception as e:
                # JSON解析失败，继续使用原始响应
                pass
                
            # 直接返回原始响应
            return {
                "success": True,
                "data": {
                    "raw_response": response,
                    "original_text": original_text
                },
                "message": "成功获取API响应"
            }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"处理响应时发生异常: {str(e)}",
                "data": {
                    "raw_response": response,
                    "original_text": original_text,
                    "error": str(e)
                }
            }

def extract_with_llm(text: str, provider: str = "sinopec") -> Dict[str, Any]:
    """
    便捷函数：使用中石化LLM提取流程信息
    
    Args:
        text: 要分析的文档文本
        provider: 固定为"sinopec"
    
    Returns:
        包含提取结果的字典
    """
    processor = LLMProcessor()
    return processor.extract_process_with_llm(text, "sinopec") 