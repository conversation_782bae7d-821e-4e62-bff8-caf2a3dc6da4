@echo off
chcp 65001 >nul
echo ========================================
echo 制度智能审查系统启动脚本
echo ========================================
echo.

echo [1/4] 检查当前目录...
cd /d "%~dp0"
echo 当前目录: %CD%

echo.
echo [2/4] 检查Python环境...

:: 检查python命令
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 找到Python环境
    python --version
    set PYTHON_CMD=python
    goto :check_version
)

:: 检查python3命令
python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 找到Python3环境
    python3 --version
    set PYTHON_CMD=python3
    goto :check_version
)

:: 检查py命令（Windows Python Launcher）
py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 找到Python Launcher
    py --version
    set PYTHON_CMD=py
    goto :check_version
)

:: 都没找到Python
echo ❌ 错误: 未找到Python环境
echo.
echo 请确保已安装Python 3.8+并且：
echo 1. 安装时勾选了"Add Python to PATH"选项，或
echo 2. 手动将Python添加到系统环境变量PATH中
echo.
echo 您可以：
echo - 从 https://python.org 下载并重新安装Python
echo - 安装时务必勾选"Add Python to PATH"
echo - 或者以管理员身份运行此脚本
echo.
pause
exit /b 1

:check_version
:: 检查Python版本是否满足要求
%PYTHON_CMD% -c "import sys; sys.exit(0 if sys.version_info >= (3, 8) else 1)" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python版本过低，需要Python 3.8+
    %PYTHON_CMD% -c "import sys; print(f'当前版本: {sys.version}')"
    pause
    exit /b 1
)

echo.
echo [3/4] 检查必要依赖...

:: 检查关键依赖包
set MISSING_DEPS=0
echo 检查关键依赖包:

%PYTHON_CMD% -c "import flask" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 缺少Flask依赖
    set MISSING_DEPS=1
) else (
    echo ✓ flask
)

%PYTHON_CMD% -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 缺少requests依赖
    set MISSING_DEPS=1
) else (
    echo ✓ requests
)

%PYTHON_CMD% -c "import docx" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 缺少python-docx依赖
    set MISSING_DEPS=1
) else (
    echo ✓ python-docx
)

%PYTHON_CMD% -c "import PyPDF2" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 缺少PyPDF2依赖
    set MISSING_DEPS=1
) else (
    echo ✓ PyPDF2
)

%PYTHON_CMD% -c "import openpyxl" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 缺少openpyxl依赖
    set MISSING_DEPS=1
) else (
    echo ✓ openpyxl
)

if %MISSING_DEPS% equ 1 (
    echo.
    echo 正在安装缺少的依赖包...
    %PYTHON_CMD% -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败，请手动运行:
        echo pip install -r requirements.txt
        pause
        exit /b 1
    ) else (
        echo ✓ 依赖安装完成
    )
)

echo.
echo [4/4] 启动Web应用...
echo 正在启动服务器，请稍候...

:: 创建自动打开浏览器的临时Python脚本
echo import webbrowser, time > temp_browser.py
echo time.sleep(1.5) >> temp_browser.py
echo webbrowser.open('http://127.0.0.1:5000') >> temp_browser.py

:: 在后台启动浏览器脚本
start /b %PYTHON_CMD% temp_browser.py

echo.
echo 🚀 服务器启动成功!
echo 📱 访问地址: http://127.0.0.1:5000
echo 🛑 按 Ctrl+C 停止服务器
echo ========================================
echo.

:: 启动Flask应用
%PYTHON_CMD% web_app.py

:: 清理临时文件
if exist temp_browser.py del /f /q temp_browser.py

echo.
echo 服务器已停止
pause 