#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟web_app.py调用的测试脚本
"""

import sys
import os
import logging

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('web_test')

def test_web_simulation():
    """模拟web_app.py中的调用"""
    try:
        logger.info("开始模拟web_app.py中的调用...")
        
        # 导入LLMProcessor (模拟web_app.py中的导入)
        from llm_processor import LLMProcessor, extract_with_llm, get_api_config
        logger.info("成功导入LLMProcessor及相关函数")
        
        # 测试文本
        text_content = """
        第五条  运行管理机制
        （一）域长提出变更申请
        """
        
        # 模拟web_app.py中的调用方式
        logger.info("开始模拟web_app.py中的LLMProcessor创建和调用...")
        
        # 这里强制每次都创建新的LLMProcessor实例，避免缓存问题
        llm = LLMProcessor()
        logger.info("✓ LLMProcessor实例创建成功")
        
        # 调用extract_process_with_llm方法 (模拟web_app.py中的调用)
        llm_result = llm.extract_process_with_llm(text_content, provider='sinopec')
        logger.info(f"✓ extract_process_with_llm调用成功")
        logger.info(f"  结果: {llm_result.get('success', False)}")
        logger.info(f"  消息: {llm_result.get('message', 'No message')}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_web_simulation()
    if success:
        print("模拟测试成功!")
    else:
        print("模拟测试失败!")
        sys.exit(1) 