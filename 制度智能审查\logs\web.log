2025-06-17 09:40:46 - web - INFO - web_app.py:823 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-17 09:40:46 - web - INFO - web_app.py:830 - 文件大小: 66833 字节
2025-06-17 09:40:46 - web - INFO - web_app.py:885 - 文本提取成功，内容长度: 3916 字符
2025-06-17 09:40:46 - web - INFO - web_app.py:488 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-17 09:40:50 - web - ERROR - web_app.py:535 - 处理文件失败: API调用失败且本地解析已禁用: 中石化AI平台API网络错误，已重试2次: HTTPSConnectionPool(host='agent.ai.sinopec.com', port=443): Max retries exceeded with url: /aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349 (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)')))
Traceback (most recent call last):
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connection.py", line 741, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connection.py", line 920, in _ssl_wrap_socket_and_match_hostname
    ssl_sock = ssl_wrap_socket(
               ^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\util\ssl_.py", line 480, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(sock, context, tls_in_tls, server_hostname)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\util\ssl_.py", line 524, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\ssl.py", line 1041, in _create
    self.do_handshake()
  File "C:\Program Files\Python312\Lib\ssl.py", line 1319, in do_handshake
    self._sslobj.do_handshake()
ssl.SSLCertVerificationError: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connectionpool.py", line 488, in _make_request
    raise new_e
urllib3.exceptions.SSLError: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='agent.ai.sinopec.com', port=443): Max retries exceeded with url: /aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349 (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 426, in _call_sinopec
    response = requests.post(
               ^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\requests\api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\requests\adapters.py", line 698, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='agent.ai.sinopec.com', port=443): Max retries exceeded with url: /aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349 (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 56, in extract_process_with_llm
    raw_response = self._call_sinopec(prompt)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 486, in _call_sinopec
    raise Exception(f"中石化AI平台API网络错误，已重试{max_retries}次: {e}")
Exception: 中石化AI平台API网络错误，已重试2次: HTTPSConnectionPool(host='agent.ai.sinopec.com', port=443): Max retries exceeded with url: /aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349 (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)')))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 490, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 746, in extract_with_llm
    return llm_processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 106, in extract_process_with_llm
    raise Exception(f"API调用失败且本地解析已禁用: {e}") from e
Exception: API调用失败且本地解析已禁用: 中石化AI平台API网络错误，已重试2次: HTTPSConnectionPool(host='agent.ai.sinopec.com', port=443): Max retries exceeded with url: /aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349 (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)')))
2025-06-17 09:40:50 - web - ERROR - web_app.py:149 - 处理文件时出错: API调用失败且本地解析已禁用: 中石化AI平台API网络错误，已重试2次: HTTPSConnectionPool(host='agent.ai.sinopec.com', port=443): Max retries exceeded with url: /aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349 (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)')))
Traceback (most recent call last):
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connection.py", line 741, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connection.py", line 920, in _ssl_wrap_socket_and_match_hostname
    ssl_sock = ssl_wrap_socket(
               ^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\util\ssl_.py", line 480, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(sock, context, tls_in_tls, server_hostname)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\util\ssl_.py", line 524, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\ssl.py", line 1041, in _create
    self.do_handshake()
  File "C:\Program Files\Python312\Lib\ssl.py", line 1319, in do_handshake
    self._sslobj.do_handshake()
ssl.SSLCertVerificationError: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connectionpool.py", line 488, in _make_request
    raise new_e
urllib3.exceptions.SSLError: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='agent.ai.sinopec.com', port=443): Max retries exceeded with url: /aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349 (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 426, in _call_sinopec
    response = requests.post(
               ^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\requests\api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\requests\adapters.py", line 698, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='agent.ai.sinopec.com', port=443): Max retries exceeded with url: /aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349 (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 56, in extract_process_with_llm
    raw_response = self._call_sinopec(prompt)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 486, in _call_sinopec
    raise Exception(f"中石化AI平台API网络错误，已重试{max_retries}次: {e}")
Exception: 中石化AI平台API网络错误，已重试2次: HTTPSConnectionPool(host='agent.ai.sinopec.com', port=443): Max retries exceeded with url: /aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349 (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)')))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 140, in upload_file
    result = process_uploaded_file(file_path, use_llm, llm_provider, output_format)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\web_app.py", line 490, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 746, in extract_with_llm
    return llm_processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 106, in extract_process_with_llm
    raise Exception(f"API调用失败且本地解析已禁用: {e}") from e
Exception: API调用失败且本地解析已禁用: 中石化AI平台API网络错误，已重试2次: HTTPSConnectionPool(host='agent.ai.sinopec.com', port=443): Max retries exceeded with url: /aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349 (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1010)')))
2025-06-17 09:42:41 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 09:44:34 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 09:46:40 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 09:46:42 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 09:46:51 - web - INFO - web_app.py:823 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-17 09:46:51 - web - INFO - web_app.py:830 - 文件大小: 66833 字节
2025-06-17 09:46:51 - web - INFO - web_app.py:885 - 文本提取成功，内容长度: 3916 字符
2025-06-17 09:46:51 - web - INFO - web_app.py:488 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-17 09:47:06 - web - ERROR - web_app.py:535 - 处理文件失败: API调用失败且本地解析已禁用: 响应解析异常且本地解析已禁用: 无法解析LLM响应为有效JSON格式，且本地解析已禁用
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 597, in _parse_response
    raise Exception("无法解析LLM响应为有效JSON格式，且本地解析已禁用")
Exception: 无法解析LLM响应为有效JSON格式，且本地解析已禁用

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 78, in extract_process_with_llm
    raise e
  File "F:\Python_project\制度智能审查\llm_processor.py", line 66, in extract_process_with_llm
    result = self._parse_response(raw_response, text)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 605, in _parse_response
    raise Exception(f"响应解析异常且本地解析已禁用: {e}") from e
Exception: 响应解析异常且本地解析已禁用: 无法解析LLM响应为有效JSON格式，且本地解析已禁用

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 490, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 756, in extract_with_llm
    return llm_processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 106, in extract_process_with_llm
    raise Exception(f"API调用失败且本地解析已禁用: {e}") from e
Exception: API调用失败且本地解析已禁用: 响应解析异常且本地解析已禁用: 无法解析LLM响应为有效JSON格式，且本地解析已禁用
2025-06-17 09:47:06 - web - ERROR - web_app.py:149 - 处理文件时出错: API调用失败且本地解析已禁用: 响应解析异常且本地解析已禁用: 无法解析LLM响应为有效JSON格式，且本地解析已禁用
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 597, in _parse_response
    raise Exception("无法解析LLM响应为有效JSON格式，且本地解析已禁用")
Exception: 无法解析LLM响应为有效JSON格式，且本地解析已禁用

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 78, in extract_process_with_llm
    raise e
  File "F:\Python_project\制度智能审查\llm_processor.py", line 66, in extract_process_with_llm
    result = self._parse_response(raw_response, text)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 605, in _parse_response
    raise Exception(f"响应解析异常且本地解析已禁用: {e}") from e
Exception: 响应解析异常且本地解析已禁用: 无法解析LLM响应为有效JSON格式，且本地解析已禁用

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 140, in upload_file
    result = process_uploaded_file(file_path, use_llm, llm_provider, output_format)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\web_app.py", line 490, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 756, in extract_with_llm
    return llm_processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 106, in extract_process_with_llm
    raise Exception(f"API调用失败且本地解析已禁用: {e}") from e
Exception: API调用失败且本地解析已禁用: 响应解析异常且本地解析已禁用: 无法解析LLM响应为有效JSON格式，且本地解析已禁用
2025-06-17 09:50:11 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 09:50:48 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 09:51:38 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 09:52:12 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 09:53:49 - web - INFO - web_app.py:823 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-17 09:53:49 - web - INFO - web_app.py:830 - 文件大小: 66833 字节
2025-06-17 09:53:49 - web - INFO - web_app.py:885 - 文本提取成功，内容长度: 3916 字符
2025-06-17 09:53:49 - web - INFO - web_app.py:488 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-17 09:54:14 - web - INFO - web_app.py:897 - 开始生成Excel报告
2025-06-17 09:54:15 - web - INFO - web_app.py:904 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250617_095414.xlsx
2025-06-17 09:54:15 - web - INFO - web_app.py:531 - 文件处理完成，生成了1个下载文件
2025-06-17 10:07:24 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 10:07:26 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 10:08:06 - web - INFO - web_app.py:823 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-17 10:08:06 - web - INFO - web_app.py:830 - 文件大小: 66833 字节
2025-06-17 10:08:06 - web - INFO - web_app.py:885 - 文本提取成功，内容长度: 3916 字符
2025-06-17 10:08:06 - web - INFO - web_app.py:488 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-17 10:08:25 - web - INFO - web_app.py:897 - 开始生成Excel报告
2025-06-17 10:08:25 - web - INFO - web_app.py:904 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250617_100825.xlsx
2025-06-17 10:08:25 - web - INFO - web_app.py:531 - 文件处理完成，生成了1个下载文件
2025-06-17 10:25:39 - web - INFO - web_app.py:823 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\test_approval_process.txt
2025-06-17 10:25:39 - web - INFO - web_app.py:830 - 文件大小: 1056 字节
2025-06-17 10:25:39 - web - INFO - web_app.py:885 - 文本提取成功，内容长度: 437 字符
2025-06-17 10:25:39 - web - INFO - web_app.py:488 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-17 10:26:11 - web - INFO - web_app.py:897 - 开始生成Excel报告
2025-06-17 10:26:11 - web - INFO - web_app.py:904 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250617_102611.xlsx
2025-06-17 10:26:11 - web - INFO - web_app.py:531 - 文件处理完成，生成了1个下载文件
2025-06-17 10:29:18 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 10:30:32 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 10:34:04 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 10:34:14 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 10:34:40 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 10:34:56 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 10:35:32 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 11:02:08 - web - INFO - web_app.py:32 - Web应用启动
2025-06-17 11:02:46 - web - INFO - web_app.py:602 - 访问日志管理页面
2025-06-17 15:57:12 - web - INFO - web_app.py:823 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-17 15:57:12 - web - INFO - web_app.py:830 - 文件大小: 66833 字节
2025-06-17 15:57:12 - web - INFO - web_app.py:885 - 文本提取成功，内容长度: 3916 字符
2025-06-17 15:57:12 - web - INFO - web_app.py:488 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-17 15:57:26 - web - INFO - web_app.py:897 - 开始生成Excel报告
2025-06-17 15:57:26 - web - INFO - web_app.py:904 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250617_155726.xlsx
2025-06-17 15:57:26 - web - INFO - web_app.py:531 - 文件处理完成，生成了1个下载文件
2025-06-18 08:48:22 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 08:48:23 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 08:48:35 - web - INFO - web_app.py:823 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\test_approval_process.txt
2025-06-18 08:48:35 - web - INFO - web_app.py:830 - 文件大小: 1056 字节
2025-06-18 08:48:35 - web - INFO - web_app.py:885 - 文本提取成功，内容长度: 437 字符
2025-06-18 08:48:35 - web - INFO - web_app.py:488 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 08:48:50 - web - INFO - web_app.py:897 - 开始生成Excel报告
2025-06-18 08:48:50 - web - INFO - web_app.py:904 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_084850.xlsx
2025-06-18 08:48:50 - web - INFO - web_app.py:531 - 文件处理完成，生成了1个下载文件
2025-06-18 14:23:24 - web - INFO - web_app.py:823 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\test_approval_process.txt
2025-06-18 14:23:24 - web - INFO - web_app.py:830 - 文件大小: 1056 字节
2025-06-18 14:23:24 - web - INFO - web_app.py:885 - 文本提取成功，内容长度: 437 字符
2025-06-18 14:23:24 - web - INFO - web_app.py:488 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 14:23:50 - web - INFO - web_app.py:897 - 开始生成Excel报告
2025-06-18 14:23:50 - web - INFO - web_app.py:904 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_142350.xlsx
2025-06-18 14:23:50 - web - INFO - web_app.py:531 - 文件处理完成，生成了1个下载文件
2025-06-18 14:25:35 - web - INFO - web_app.py:823 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 14:25:35 - web - INFO - web_app.py:830 - 文件大小: 66833 字节
2025-06-18 14:25:35 - web - INFO - web_app.py:885 - 文本提取成功，内容长度: 3916 字符
2025-06-18 14:25:35 - web - INFO - web_app.py:488 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 14:25:57 - web - INFO - web_app.py:897 - 开始生成Excel报告
2025-06-18 14:25:57 - web - INFO - web_app.py:904 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_142557.xlsx
2025-06-18 14:25:57 - web - INFO - web_app.py:531 - 文件处理完成，生成了1个下载文件
2025-06-18 14:27:50 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 14:28:30 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 14:32:36 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 14:33:16 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 14:35:01 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 14:35:26 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 14:36:01 - web - INFO - web_app.py:899 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 14:36:01 - web - INFO - web_app.py:906 - 文件大小: 66833 字节
2025-06-18 14:36:01 - web - INFO - web_app.py:961 - 文本提取成功，内容长度: 3916 字符
2025-06-18 14:36:01 - web - INFO - web_app.py:558 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 14:36:28 - web - INFO - web_app.py:973 - 开始生成Excel报告
2025-06-18 14:36:28 - web - INFO - web_app.py:980 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_143628.xlsx
2025-06-18 14:36:28 - web - INFO - web_app.py:601 - 文件处理完成，生成了1个下载文件
2025-06-18 16:46:34 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 16:46:35 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 16:46:41 - web - INFO - web_app.py:899 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 16:46:41 - web - INFO - web_app.py:906 - 文件大小: 66833 字节
2025-06-18 16:46:41 - web - INFO - web_app.py:961 - 文本提取成功，内容长度: 3916 字符
2025-06-18 16:46:41 - web - INFO - web_app.py:558 - 开始处理文本，使用LLM: True, 提供商: openai
2025-06-18 16:47:09 - web - INFO - web_app.py:899 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 16:47:09 - web - INFO - web_app.py:906 - 文件大小: 66833 字节
2025-06-18 16:47:09 - web - INFO - web_app.py:961 - 文本提取成功，内容长度: 3916 字符
2025-06-18 16:47:09 - web - INFO - web_app.py:558 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 16:47:17 - web - INFO - web_app.py:973 - 开始生成Excel报告
2025-06-18 16:47:18 - web - INFO - web_app.py:980 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_164717.xlsx
2025-06-18 16:47:18 - web - INFO - web_app.py:601 - 文件处理完成，生成了1个下载文件
2025-06-18 16:47:23 - web - INFO - web_app.py:973 - 开始生成Excel报告
2025-06-18 16:47:23 - web - INFO - web_app.py:980 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_164723.xlsx
2025-06-18 16:47:23 - web - INFO - web_app.py:601 - 文件处理完成，生成了1个下载文件
2025-06-18 16:49:17 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 16:49:27 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 16:49:51 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 16:50:33 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 16:50:46 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 16:50:55 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 16:51:32 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 16:53:00 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 16:53:00 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 16:53:00 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 16:53:00 - web - INFO - web_app.py:569 - 开始处理文本，使用LLM: True, 提供商: openai
2025-06-18 16:53:46 - web - INFO - web_app.py:984 - 开始生成Excel报告
2025-06-18 16:53:46 - web - INFO - web_app.py:991 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_165346.xlsx
2025-06-18 16:53:46 - web - INFO - web_app.py:612 - 文件处理完成，生成了1个下载文件
2025-06-18 16:58:08 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 16:59:48 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 16:59:48 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 16:59:48 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 16:59:48 - web - INFO - web_app.py:569 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 16:59:49 - web - ERROR - web_app.py:616 - 处理文件失败: API调用失败且本地解析已禁用: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f1227d7bdfe749e8bfa3e05af1d5c8d2', 'step_id': '', 'created': 1750237189645, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 44, in extract_process_with_llm
    raw_response = self._call_sinopec(text)  # 直接传输文档内容
                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 529, in _call_sinopec
    raise Exception(f"中石化AI平台API响应中未找到有效内容字段: {result}")
Exception: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f1227d7bdfe749e8bfa3e05af1d5c8d2', 'step_id': '', 'created': 1750237189645, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 571, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 1048, in extract_with_llm
    return llm_processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 117, in extract_process_with_llm
    raise Exception(f"API调用失败且本地解析已禁用: {e}") from e
Exception: API调用失败且本地解析已禁用: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f1227d7bdfe749e8bfa3e05af1d5c8d2', 'step_id': '', 'created': 1750237189645, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}
2025-06-18 16:59:49 - web - ERROR - web_app.py:149 - 处理文件时出错: API调用失败且本地解析已禁用: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f1227d7bdfe749e8bfa3e05af1d5c8d2', 'step_id': '', 'created': 1750237189645, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 44, in extract_process_with_llm
    raw_response = self._call_sinopec(text)  # 直接传输文档内容
                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 529, in _call_sinopec
    raise Exception(f"中石化AI平台API响应中未找到有效内容字段: {result}")
Exception: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f1227d7bdfe749e8bfa3e05af1d5c8d2', 'step_id': '', 'created': 1750237189645, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 140, in upload_file
    result = process_uploaded_file(file_path, use_llm, llm_provider, output_format)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\web_app.py", line 571, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 1048, in extract_with_llm
    return llm_processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 117, in extract_process_with_llm
    raise Exception(f"API调用失败且本地解析已禁用: {e}") from e
Exception: API调用失败且本地解析已禁用: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f1227d7bdfe749e8bfa3e05af1d5c8d2', 'step_id': '', 'created': 1750237189645, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}
2025-06-18 17:00:04 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 17:00:04 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 17:00:04 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 17:00:04 - web - INFO - web_app.py:569 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 17:00:05 - web - ERROR - web_app.py:616 - 处理文件失败: API调用失败且本地解析已禁用: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f00ab14b287e4392b0e1c460e7a6a874', 'step_id': '', 'created': 1750237205359, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 44, in extract_process_with_llm
    raw_response = self._call_sinopec(text)  # 直接传输文档内容
                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 529, in _call_sinopec
    raise Exception(f"中石化AI平台API响应中未找到有效内容字段: {result}")
Exception: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f00ab14b287e4392b0e1c460e7a6a874', 'step_id': '', 'created': 1750237205359, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 571, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 1048, in extract_with_llm
    return llm_processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 117, in extract_process_with_llm
    raise Exception(f"API调用失败且本地解析已禁用: {e}") from e
Exception: API调用失败且本地解析已禁用: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f00ab14b287e4392b0e1c460e7a6a874', 'step_id': '', 'created': 1750237205359, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}
2025-06-18 17:00:05 - web - ERROR - web_app.py:149 - 处理文件时出错: API调用失败且本地解析已禁用: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f00ab14b287e4392b0e1c460e7a6a874', 'step_id': '', 'created': 1750237205359, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 44, in extract_process_with_llm
    raw_response = self._call_sinopec(text)  # 直接传输文档内容
                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 529, in _call_sinopec
    raise Exception(f"中石化AI平台API响应中未找到有效内容字段: {result}")
Exception: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f00ab14b287e4392b0e1c460e7a6a874', 'step_id': '', 'created': 1750237205359, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 140, in upload_file
    result = process_uploaded_file(file_path, use_llm, llm_provider, output_format)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\web_app.py", line 571, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 1048, in extract_with_llm
    return llm_processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 117, in extract_process_with_llm
    raise Exception(f"API调用失败且本地解析已禁用: {e}") from e
Exception: API调用失败且本地解析已禁用: 中石化AI平台API响应中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'f00ab14b287e4392b0e1c460e7a6a874', 'step_id': '', 'created': 1750237205359, 'data': {'usage': {'max_token': '8k'}}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 0, 'prompt_tokens': 0, 'completion_tokens': 0, 'max_token': '8k'}}}
2025-06-18 17:01:19 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:01:52 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:02:40 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:04:00 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 17:04:00 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 17:04:00 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 17:04:00 - web - INFO - web_app.py:569 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 17:04:02 - web - INFO - web_app.py:984 - 开始生成Excel报告
2025-06-18 17:04:02 - web - INFO - web_app.py:991 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_170402.xlsx
2025-06-18 17:04:02 - web - INFO - web_app.py:612 - 文件处理完成，生成了1个下载文件
2025-06-18 17:13:25 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:14:00 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:14:32 - web - INFO - web_app.py:916 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 17:14:32 - web - INFO - web_app.py:923 - 文件大小: 66833 字节
2025-06-18 17:14:32 - web - INFO - web_app.py:978 - 文本提取成功，内容长度: 3916 字符
2025-06-18 17:14:32 - web - INFO - web_app.py:569 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 17:14:34 - web - INFO - web_app.py:990 - 开始生成Excel报告
2025-06-18 17:14:34 - web - INFO - web_app.py:997 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_171434.xlsx
2025-06-18 17:14:34 - web - INFO - web_app.py:612 - 文件处理完成，生成了1个下载文件
2025-06-18 17:15:22 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:16:20 - web - INFO - web_app.py:916 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 17:16:20 - web - INFO - web_app.py:923 - 文件大小: 66833 字节
2025-06-18 17:16:20 - web - INFO - web_app.py:978 - 文本提取成功，内容长度: 3916 字符
2025-06-18 17:16:20 - web - INFO - web_app.py:569 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 17:16:22 - web - INFO - web_app.py:990 - 开始生成Excel报告
2025-06-18 17:16:22 - web - INFO - web_app.py:997 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_171622.xlsx
2025-06-18 17:16:22 - web - INFO - web_app.py:612 - 文件处理完成，生成了1个下载文件
2025-06-18 17:18:59 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:20:40 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:21:16 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:24:47 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:25:12 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:25:21 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:25:44 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:26:47 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:29:49 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:33:36 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:35:22 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:36:22 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:37:24 - web - INFO - web_app.py:916 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 17:37:24 - web - INFO - web_app.py:923 - 文件大小: 66833 字节
2025-06-18 17:37:24 - web - INFO - web_app.py:978 - 文本提取成功，内容长度: 3916 字符
2025-06-18 17:37:24 - web - INFO - web_app.py:569 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 17:37:38 - web - INFO - web_app.py:990 - 开始生成Excel报告
2025-06-18 17:37:38 - web - INFO - web_app.py:997 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_173738.xlsx
2025-06-18 17:37:38 - web - INFO - web_app.py:612 - 文件处理完成，生成了1个下载文件
2025-06-18 17:42:26 - web - INFO - web_app.py:916 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 17:42:26 - web - INFO - web_app.py:923 - 文件大小: 66833 字节
2025-06-18 17:42:26 - web - INFO - web_app.py:978 - 文本提取成功，内容长度: 3916 字符
2025-06-18 17:42:26 - web - INFO - web_app.py:569 - 开始处理文本，使用LLM: True, 提供商: openai
2025-06-18 17:43:00 - web - INFO - web_app.py:990 - 开始生成Excel报告
2025-06-18 17:43:00 - web - INFO - web_app.py:997 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_174300.xlsx
2025-06-18 17:43:00 - web - INFO - web_app.py:612 - 文件处理完成，生成了1个下载文件
2025-06-18 17:47:23 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:47:32 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:48:22 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:50:11 - web - INFO - web_app.py:916 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 17:50:11 - web - INFO - web_app.py:923 - 文件大小: 66833 字节
2025-06-18 17:50:11 - web - INFO - web_app.py:978 - 文本提取成功，内容长度: 3916 字符
2025-06-18 17:50:11 - web - INFO - web_app.py:569 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 17:50:31 - web - INFO - web_app.py:990 - 开始生成Excel报告
2025-06-18 17:50:31 - web - INFO - web_app.py:997 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_175031.xlsx
2025-06-18 17:50:31 - web - INFO - web_app.py:612 - 文件处理完成，生成了1个下载文件
2025-06-18 17:52:33 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:56:18 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:56:29 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:56:54 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:57:15 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 17:58:06 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:04:14 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:06:14 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:15:31 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:15:32 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:15:44 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 18:15:44 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 18:15:44 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 18:15:44 - web - INFO - web_app.py:563 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 18:15:57 - web - ERROR - web_app.py:610 - 处理文件失败: API调用失败: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'a8c22a627d524c12ac9f6a8b9dcc5bc0', 'step_id': '', 'created': 1750241757939, 'data': {'usage': {'completion_tokens': 554, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 947}, '文本呈现': '### 示例分析（假设制度文本为某公司《采购管理制度》节选）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算复核**：财务部核查预算可用性  \n4. **采购方案审批**：采购部制定方案，分管领导批准  \n5. **供应商会签**：采购部、法务部、技术部联合评审供应商  \n6. **合同签订**：法务部审核条款，总经理签字确认  \n7. **验收备案**：使用部门验收，行政部存档  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门       | 审核           |  \n| 预算复核         | 财务部         | 复核           |  \n| 采购方案审批     | 采购部         | 发起           |  \n|                  | 分管领导       | 确认（批准）   |  \n| 供应商会签       | 采购部         | 协作（牵头）   |  \n|                  | 法务部         | 协作（合规审核）|  \n|                  | 技术部         | 协作（技术评估）|  \n| 合同签订         | 法务部         | 审核           |  \n|                  | 总经理         | 确认（签字）   |  \n| 验收备案         | 使用部门       | 确认（验收）   |  \n|                  | 行政部         | 备案           |  \n\n---  \n### 关键解析逻辑说明  \n1. **锚定关键词**：  \n   - 原文出现“分管领导批准”“法务部审核条款”等，对应**确认**和**审核**职责；  \n   - “联合评审供应商”中的“联合”提示多部门**协作**（会签）。  \n\n2. **隐性环节推断**：  \n   - “财务部核查预算”未明确使用“审批”一词，但根据动作本质归类为**复核**。  \n\n3. **多角色匹配**：  \n   - 同一环节（如合同签订）涉及多部门时，需拆分职责类型（法务部审核为**审核端**，总经理签字为**确认端**）。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为方法论示例。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 947, 'prompt_tokens': 393, 'completion_tokens': 554, 'max_token': '8k'}}}
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 42, in extract_process_with_llm
    raw_response = self._call_sinopec(text)
                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 320, in _call_sinopec
    raise Exception(f"中石化AI平台API返回中未找到有效内容字段: {result}")
Exception: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'a8c22a627d524c12ac9f6a8b9dcc5bc0', 'step_id': '', 'created': 1750241757939, 'data': {'usage': {'completion_tokens': 554, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 947}, '文本呈现': '### 示例分析（假设制度文本为某公司《采购管理制度》节选）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算复核**：财务部核查预算可用性  \n4. **采购方案审批**：采购部制定方案，分管领导批准  \n5. **供应商会签**：采购部、法务部、技术部联合评审供应商  \n6. **合同签订**：法务部审核条款，总经理签字确认  \n7. **验收备案**：使用部门验收，行政部存档  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门       | 审核           |  \n| 预算复核         | 财务部         | 复核           |  \n| 采购方案审批     | 采购部         | 发起           |  \n|                  | 分管领导       | 确认（批准）   |  \n| 供应商会签       | 采购部         | 协作（牵头）   |  \n|                  | 法务部         | 协作（合规审核）|  \n|                  | 技术部         | 协作（技术评估）|  \n| 合同签订         | 法务部         | 审核           |  \n|                  | 总经理         | 确认（签字）   |  \n| 验收备案         | 使用部门       | 确认（验收）   |  \n|                  | 行政部         | 备案           |  \n\n---  \n### 关键解析逻辑说明  \n1. **锚定关键词**：  \n   - 原文出现“分管领导批准”“法务部审核条款”等，对应**确认**和**审核**职责；  \n   - “联合评审供应商”中的“联合”提示多部门**协作**（会签）。  \n\n2. **隐性环节推断**：  \n   - “财务部核查预算”未明确使用“审批”一词，但根据动作本质归类为**复核**。  \n\n3. **多角色匹配**：  \n   - 同一环节（如合同签订）涉及多部门时，需拆分职责类型（法务部审核为**审核端**，总经理签字为**确认端**）。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为方法论示例。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 947, 'prompt_tokens': 393, 'completion_tokens': 554, 'max_token': '8k'}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 565, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 435, in extract_with_llm
    return processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 99, in extract_process_with_llm
    raise Exception(f"API调用失败: {e}") from e
Exception: API调用失败: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'a8c22a627d524c12ac9f6a8b9dcc5bc0', 'step_id': '', 'created': 1750241757939, 'data': {'usage': {'completion_tokens': 554, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 947}, '文本呈现': '### 示例分析（假设制度文本为某公司《采购管理制度》节选）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算复核**：财务部核查预算可用性  \n4. **采购方案审批**：采购部制定方案，分管领导批准  \n5. **供应商会签**：采购部、法务部、技术部联合评审供应商  \n6. **合同签订**：法务部审核条款，总经理签字确认  \n7. **验收备案**：使用部门验收，行政部存档  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门       | 审核           |  \n| 预算复核         | 财务部         | 复核           |  \n| 采购方案审批     | 采购部         | 发起           |  \n|                  | 分管领导       | 确认（批准）   |  \n| 供应商会签       | 采购部         | 协作（牵头）   |  \n|                  | 法务部         | 协作（合规审核）|  \n|                  | 技术部         | 协作（技术评估）|  \n| 合同签订         | 法务部         | 审核           |  \n|                  | 总经理         | 确认（签字）   |  \n| 验收备案         | 使用部门       | 确认（验收）   |  \n|                  | 行政部         | 备案           |  \n\n---  \n### 关键解析逻辑说明  \n1. **锚定关键词**：  \n   - 原文出现“分管领导批准”“法务部审核条款”等，对应**确认**和**审核**职责；  \n   - “联合评审供应商”中的“联合”提示多部门**协作**（会签）。  \n\n2. **隐性环节推断**：  \n   - “财务部核查预算”未明确使用“审批”一词，但根据动作本质归类为**复核**。  \n\n3. **多角色匹配**：  \n   - 同一环节（如合同签订）涉及多部门时，需拆分职责类型（法务部审核为**审核端**，总经理签字为**确认端**）。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为方法论示例。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 947, 'prompt_tokens': 393, 'completion_tokens': 554, 'max_token': '8k'}}}
2025-06-18 18:15:57 - web - ERROR - web_app.py:149 - 处理文件时出错: API调用失败: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'a8c22a627d524c12ac9f6a8b9dcc5bc0', 'step_id': '', 'created': 1750241757939, 'data': {'usage': {'completion_tokens': 554, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 947}, '文本呈现': '### 示例分析（假设制度文本为某公司《采购管理制度》节选）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算复核**：财务部核查预算可用性  \n4. **采购方案审批**：采购部制定方案，分管领导批准  \n5. **供应商会签**：采购部、法务部、技术部联合评审供应商  \n6. **合同签订**：法务部审核条款，总经理签字确认  \n7. **验收备案**：使用部门验收，行政部存档  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门       | 审核           |  \n| 预算复核         | 财务部         | 复核           |  \n| 采购方案审批     | 采购部         | 发起           |  \n|                  | 分管领导       | 确认（批准）   |  \n| 供应商会签       | 采购部         | 协作（牵头）   |  \n|                  | 法务部         | 协作（合规审核）|  \n|                  | 技术部         | 协作（技术评估）|  \n| 合同签订         | 法务部         | 审核           |  \n|                  | 总经理         | 确认（签字）   |  \n| 验收备案         | 使用部门       | 确认（验收）   |  \n|                  | 行政部         | 备案           |  \n\n---  \n### 关键解析逻辑说明  \n1. **锚定关键词**：  \n   - 原文出现“分管领导批准”“法务部审核条款”等，对应**确认**和**审核**职责；  \n   - “联合评审供应商”中的“联合”提示多部门**协作**（会签）。  \n\n2. **隐性环节推断**：  \n   - “财务部核查预算”未明确使用“审批”一词，但根据动作本质归类为**复核**。  \n\n3. **多角色匹配**：  \n   - 同一环节（如合同签订）涉及多部门时，需拆分职责类型（法务部审核为**审核端**，总经理签字为**确认端**）。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为方法论示例。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 947, 'prompt_tokens': 393, 'completion_tokens': 554, 'max_token': '8k'}}}
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 42, in extract_process_with_llm
    raw_response = self._call_sinopec(text)
                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 320, in _call_sinopec
    raise Exception(f"中石化AI平台API返回中未找到有效内容字段: {result}")
Exception: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'a8c22a627d524c12ac9f6a8b9dcc5bc0', 'step_id': '', 'created': 1750241757939, 'data': {'usage': {'completion_tokens': 554, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 947}, '文本呈现': '### 示例分析（假设制度文本为某公司《采购管理制度》节选）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算复核**：财务部核查预算可用性  \n4. **采购方案审批**：采购部制定方案，分管领导批准  \n5. **供应商会签**：采购部、法务部、技术部联合评审供应商  \n6. **合同签订**：法务部审核条款，总经理签字确认  \n7. **验收备案**：使用部门验收，行政部存档  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门       | 审核           |  \n| 预算复核         | 财务部         | 复核           |  \n| 采购方案审批     | 采购部         | 发起           |  \n|                  | 分管领导       | 确认（批准）   |  \n| 供应商会签       | 采购部         | 协作（牵头）   |  \n|                  | 法务部         | 协作（合规审核）|  \n|                  | 技术部         | 协作（技术评估）|  \n| 合同签订         | 法务部         | 审核           |  \n|                  | 总经理         | 确认（签字）   |  \n| 验收备案         | 使用部门       | 确认（验收）   |  \n|                  | 行政部         | 备案           |  \n\n---  \n### 关键解析逻辑说明  \n1. **锚定关键词**：  \n   - 原文出现“分管领导批准”“法务部审核条款”等，对应**确认**和**审核**职责；  \n   - “联合评审供应商”中的“联合”提示多部门**协作**（会签）。  \n\n2. **隐性环节推断**：  \n   - “财务部核查预算”未明确使用“审批”一词，但根据动作本质归类为**复核**。  \n\n3. **多角色匹配**：  \n   - 同一环节（如合同签订）涉及多部门时，需拆分职责类型（法务部审核为**审核端**，总经理签字为**确认端**）。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为方法论示例。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 947, 'prompt_tokens': 393, 'completion_tokens': 554, 'max_token': '8k'}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 140, in upload_file
    result = process_uploaded_file(file_path, use_llm, llm_provider, output_format)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\web_app.py", line 565, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 435, in extract_with_llm
    return processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 99, in extract_process_with_llm
    raise Exception(f"API调用失败: {e}") from e
Exception: API调用失败: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': 'a8c22a627d524c12ac9f6a8b9dcc5bc0', 'step_id': '', 'created': 1750241757939, 'data': {'usage': {'completion_tokens': 554, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 947}, '文本呈现': '### 示例分析（假设制度文本为某公司《采购管理制度》节选）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算复核**：财务部核查预算可用性  \n4. **采购方案审批**：采购部制定方案，分管领导批准  \n5. **供应商会签**：采购部、法务部、技术部联合评审供应商  \n6. **合同签订**：法务部审核条款，总经理签字确认  \n7. **验收备案**：使用部门验收，行政部存档  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门       | 审核           |  \n| 预算复核         | 财务部         | 复核           |  \n| 采购方案审批     | 采购部         | 发起           |  \n|                  | 分管领导       | 确认（批准）   |  \n| 供应商会签       | 采购部         | 协作（牵头）   |  \n|                  | 法务部         | 协作（合规审核）|  \n|                  | 技术部         | 协作（技术评估）|  \n| 合同签订         | 法务部         | 审核           |  \n|                  | 总经理         | 确认（签字）   |  \n| 验收备案         | 使用部门       | 确认（验收）   |  \n|                  | 行政部         | 备案           |  \n\n---  \n### 关键解析逻辑说明  \n1. **锚定关键词**：  \n   - 原文出现“分管领导批准”“法务部审核条款”等，对应**确认**和**审核**职责；  \n   - “联合评审供应商”中的“联合”提示多部门**协作**（会签）。  \n\n2. **隐性环节推断**：  \n   - “财务部核查预算”未明确使用“审批”一词，但根据动作本质归类为**复核**。  \n\n3. **多角色匹配**：  \n   - 同一环节（如合同签订）涉及多部门时，需拆分职责类型（法务部审核为**审核端**，总经理签字为**确认端**）。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为方法论示例。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 947, 'prompt_tokens': 393, 'completion_tokens': 554, 'max_token': '8k'}}}
2025-06-18 18:16:11 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 18:16:11 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 18:16:11 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 18:16:11 - web - INFO - web_app.py:563 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 18:16:25 - web - ERROR - web_app.py:610 - 处理文件失败: API调用失败: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': '698149b563204806a0699cd84364c0d5', 'step_id': '', 'created': 1750241785606, 'data': {'usage': {'completion_tokens': 555, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 948}, '文本呈现': '### 示例分析（假设制度文本为《XX公司采购管理制度》）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算审核**：财务部核对预算额度  \n4. **采购部复核**：采购部评估供应商及价格合规性  \n5. **会签审批**：  \n   - 金额≤10万：分管领导审批  \n   - 金额＞10万：分管领导+总经理双签  \n6. **合同签订**：法务部审核条款，采购部签署合同  \n7. **验收确认**：需求部门与质检部联合验收  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门负责人 | 审核           |  \n| 预算审核         | 财务部         | 审核           |  \n| 采购部复核       | 采购部         | 复核           |  \n| 会签审批（≤10万）| 分管领导       | 确认           |  \n| 会签审批（＞10万）| 分管领导+总经理 | 确认（双签）   |  \n| 合同签订         | 法务部         | 审核（条款）   |  \n| 合同签订         | 采购部         | 确认（签署）   |  \n| 验收确认         | 需求部门+质检部| 协作（验收）   |  \n\n---  \n### **关键分析逻辑说明**  \n1. **关键词锚定**：  \n   - 发现条款“采购申请需经部门负责人**审核**后**提交**财务部**核准**预算” → 提取“审核”“提交”“核准”对应环节。  \n   - 发现“重大采购需**会签**分管领导与总经理” → 区分金额阈值，明确双签节点。  \n\n2. **隐性流程推断**：  \n   - 条款“合同签署前需法务部**核实**”未明确流程顺序，结合上下文补充为“合同签订”前置环节。  \n\n3. **部门职责交叉验证**：  \n   - 质检部仅在验收条款中出现，但未提及其审核权，故归类为“协作”而非“审核”。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为模板演示。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 948, 'prompt_tokens': 393, 'completion_tokens': 555, 'max_token': '8k'}}}
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 42, in extract_process_with_llm
    raw_response = self._call_sinopec(text)
                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 320, in _call_sinopec
    raise Exception(f"中石化AI平台API返回中未找到有效内容字段: {result}")
Exception: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': '698149b563204806a0699cd84364c0d5', 'step_id': '', 'created': 1750241785606, 'data': {'usage': {'completion_tokens': 555, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 948}, '文本呈现': '### 示例分析（假设制度文本为《XX公司采购管理制度》）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算审核**：财务部核对预算额度  \n4. **采购部复核**：采购部评估供应商及价格合规性  \n5. **会签审批**：  \n   - 金额≤10万：分管领导审批  \n   - 金额＞10万：分管领导+总经理双签  \n6. **合同签订**：法务部审核条款，采购部签署合同  \n7. **验收确认**：需求部门与质检部联合验收  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门负责人 | 审核           |  \n| 预算审核         | 财务部         | 审核           |  \n| 采购部复核       | 采购部         | 复核           |  \n| 会签审批（≤10万）| 分管领导       | 确认           |  \n| 会签审批（＞10万）| 分管领导+总经理 | 确认（双签）   |  \n| 合同签订         | 法务部         | 审核（条款）   |  \n| 合同签订         | 采购部         | 确认（签署）   |  \n| 验收确认         | 需求部门+质检部| 协作（验收）   |  \n\n---  \n### **关键分析逻辑说明**  \n1. **关键词锚定**：  \n   - 发现条款“采购申请需经部门负责人**审核**后**提交**财务部**核准**预算” → 提取“审核”“提交”“核准”对应环节。  \n   - 发现“重大采购需**会签**分管领导与总经理” → 区分金额阈值，明确双签节点。  \n\n2. **隐性流程推断**：  \n   - 条款“合同签署前需法务部**核实**”未明确流程顺序，结合上下文补充为“合同签订”前置环节。  \n\n3. **部门职责交叉验证**：  \n   - 质检部仅在验收条款中出现，但未提及其审核权，故归类为“协作”而非“审核”。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为模板演示。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 948, 'prompt_tokens': 393, 'completion_tokens': 555, 'max_token': '8k'}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 565, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 435, in extract_with_llm
    return processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 99, in extract_process_with_llm
    raise Exception(f"API调用失败: {e}") from e
Exception: API调用失败: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': '698149b563204806a0699cd84364c0d5', 'step_id': '', 'created': 1750241785606, 'data': {'usage': {'completion_tokens': 555, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 948}, '文本呈现': '### 示例分析（假设制度文本为《XX公司采购管理制度》）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算审核**：财务部核对预算额度  \n4. **采购部复核**：采购部评估供应商及价格合规性  \n5. **会签审批**：  \n   - 金额≤10万：分管领导审批  \n   - 金额＞10万：分管领导+总经理双签  \n6. **合同签订**：法务部审核条款，采购部签署合同  \n7. **验收确认**：需求部门与质检部联合验收  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门负责人 | 审核           |  \n| 预算审核         | 财务部         | 审核           |  \n| 采购部复核       | 采购部         | 复核           |  \n| 会签审批（≤10万）| 分管领导       | 确认           |  \n| 会签审批（＞10万）| 分管领导+总经理 | 确认（双签）   |  \n| 合同签订         | 法务部         | 审核（条款）   |  \n| 合同签订         | 采购部         | 确认（签署）   |  \n| 验收确认         | 需求部门+质检部| 协作（验收）   |  \n\n---  \n### **关键分析逻辑说明**  \n1. **关键词锚定**：  \n   - 发现条款“采购申请需经部门负责人**审核**后**提交**财务部**核准**预算” → 提取“审核”“提交”“核准”对应环节。  \n   - 发现“重大采购需**会签**分管领导与总经理” → 区分金额阈值，明确双签节点。  \n\n2. **隐性流程推断**：  \n   - 条款“合同签署前需法务部**核实**”未明确流程顺序，结合上下文补充为“合同签订”前置环节。  \n\n3. **部门职责交叉验证**：  \n   - 质检部仅在验收条款中出现，但未提及其审核权，故归类为“协作”而非“审核”。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为模板演示。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 948, 'prompt_tokens': 393, 'completion_tokens': 555, 'max_token': '8k'}}}
2025-06-18 18:16:25 - web - ERROR - web_app.py:149 - 处理文件时出错: API调用失败: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': '698149b563204806a0699cd84364c0d5', 'step_id': '', 'created': 1750241785606, 'data': {'usage': {'completion_tokens': 555, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 948}, '文本呈现': '### 示例分析（假设制度文本为《XX公司采购管理制度》）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算审核**：财务部核对预算额度  \n4. **采购部复核**：采购部评估供应商及价格合规性  \n5. **会签审批**：  \n   - 金额≤10万：分管领导审批  \n   - 金额＞10万：分管领导+总经理双签  \n6. **合同签订**：法务部审核条款，采购部签署合同  \n7. **验收确认**：需求部门与质检部联合验收  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门负责人 | 审核           |  \n| 预算审核         | 财务部         | 审核           |  \n| 采购部复核       | 采购部         | 复核           |  \n| 会签审批（≤10万）| 分管领导       | 确认           |  \n| 会签审批（＞10万）| 分管领导+总经理 | 确认（双签）   |  \n| 合同签订         | 法务部         | 审核（条款）   |  \n| 合同签订         | 采购部         | 确认（签署）   |  \n| 验收确认         | 需求部门+质检部| 协作（验收）   |  \n\n---  \n### **关键分析逻辑说明**  \n1. **关键词锚定**：  \n   - 发现条款“采购申请需经部门负责人**审核**后**提交**财务部**核准**预算” → 提取“审核”“提交”“核准”对应环节。  \n   - 发现“重大采购需**会签**分管领导与总经理” → 区分金额阈值，明确双签节点。  \n\n2. **隐性流程推断**：  \n   - 条款“合同签署前需法务部**核实**”未明确流程顺序，结合上下文补充为“合同签订”前置环节。  \n\n3. **部门职责交叉验证**：  \n   - 质检部仅在验收条款中出现，但未提及其审核权，故归类为“协作”而非“审核”。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为模板演示。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 948, 'prompt_tokens': 393, 'completion_tokens': 555, 'max_token': '8k'}}}
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\llm_processor.py", line 42, in extract_process_with_llm
    raw_response = self._call_sinopec(text)
                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 320, in _call_sinopec
    raise Exception(f"中石化AI平台API返回中未找到有效内容字段: {result}")
Exception: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': '698149b563204806a0699cd84364c0d5', 'step_id': '', 'created': 1750241785606, 'data': {'usage': {'completion_tokens': 555, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 948}, '文本呈现': '### 示例分析（假设制度文本为《XX公司采购管理制度》）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算审核**：财务部核对预算额度  \n4. **采购部复核**：采购部评估供应商及价格合规性  \n5. **会签审批**：  \n   - 金额≤10万：分管领导审批  \n   - 金额＞10万：分管领导+总经理双签  \n6. **合同签订**：法务部审核条款，采购部签署合同  \n7. **验收确认**：需求部门与质检部联合验收  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门负责人 | 审核           |  \n| 预算审核         | 财务部         | 审核           |  \n| 采购部复核       | 采购部         | 复核           |  \n| 会签审批（≤10万）| 分管领导       | 确认           |  \n| 会签审批（＞10万）| 分管领导+总经理 | 确认（双签）   |  \n| 合同签订         | 法务部         | 审核（条款）   |  \n| 合同签订         | 采购部         | 确认（签署）   |  \n| 验收确认         | 需求部门+质检部| 协作（验收）   |  \n\n---  \n### **关键分析逻辑说明**  \n1. **关键词锚定**：  \n   - 发现条款“采购申请需经部门负责人**审核**后**提交**财务部**核准**预算” → 提取“审核”“提交”“核准”对应环节。  \n   - 发现“重大采购需**会签**分管领导与总经理” → 区分金额阈值，明确双签节点。  \n\n2. **隐性流程推断**：  \n   - 条款“合同签署前需法务部**核实**”未明确流程顺序，结合上下文补充为“合同签订”前置环节。  \n\n3. **部门职责交叉验证**：  \n   - 质检部仅在验收条款中出现，但未提及其审核权，故归类为“协作”而非“审核”。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为模板演示。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 948, 'prompt_tokens': 393, 'completion_tokens': 555, 'max_token': '8k'}}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 140, in upload_file
    result = process_uploaded_file(file_path, use_llm, llm_provider, output_format)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\web_app.py", line 565, in process_uploaded_file
    process_data = extract_with_llm(text_content, llm_provider)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 435, in extract_with_llm
    return processor.extract_process_with_llm(text, provider)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Python_project\制度智能审查\llm_processor.py", line 99, in extract_process_with_llm
    raise Exception(f"API调用失败: {e}") from e
Exception: API调用失败: 中石化AI平台API返回中未找到有效内容字段: {'code': 200, 'msg': '成功', 'data': {'event': 'workflow_finished', 'session_id': '698149b563204806a0699cd84364c0d5', 'step_id': '', 'created': 1750241785606, 'data': {'usage': {'completion_tokens': 555, 'max_token': '8k', 'prompt_tokens': 393, 'total_tokens': 948}, '文本呈现': '### 示例分析（假设制度文本为《XX公司采购管理制度》）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算审核**：财务部核对预算额度  \n4. **采购部复核**：采购部评估供应商及价格合规性  \n5. **会签审批**：  \n   - 金额≤10万：分管领导审批  \n   - 金额＞10万：分管领导+总经理双签  \n6. **合同签订**：法务部审核条款，采购部签署合同  \n7. **验收确认**：需求部门与质检部联合验收  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门负责人 | 审核           |  \n| 预算审核         | 财务部         | 审核           |  \n| 采购部复核       | 采购部         | 复核           |  \n| 会签审批（≤10万）| 分管领导       | 确认           |  \n| 会签审批（＞10万）| 分管领导+总经理 | 确认（双签）   |  \n| 合同签订         | 法务部         | 审核（条款）   |  \n| 合同签订         | 采购部         | 确认（签署）   |  \n| 验收确认         | 需求部门+质检部| 协作（验收）   |  \n\n---  \n### **关键分析逻辑说明**  \n1. **关键词锚定**：  \n   - 发现条款“采购申请需经部门负责人**审核**后**提交**财务部**核准**预算” → 提取“审核”“提交”“核准”对应环节。  \n   - 发现“重大采购需**会签**分管领导与总经理” → 区分金额阈值，明确双签节点。  \n\n2. **隐性流程推断**：  \n   - 条款“合同签署前需法务部**核实**”未明确流程顺序，结合上下文补充为“合同签订”前置环节。  \n\n3. **部门职责交叉验证**：  \n   - 质检部仅在验收条款中出现，但未提及其审核权，故归类为“协作”而非“审核”。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为模板演示。'}, 'url_list': [], 'search_results': [], 'usage': {'total_tokens': 948, 'prompt_tokens': 393, 'completion_tokens': 555, 'max_token': '8k'}}}
2025-06-18 18:16:59 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:17:42 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:18:39 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:20:48 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 18:20:48 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 18:20:48 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 18:20:48 - web - INFO - web_app.py:563 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 18:21:00 - web - INFO - web_app.py:984 - 开始生成Excel报告
2025-06-18 18:21:00 - web - INFO - web_app.py:991 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_182100.xlsx
2025-06-18 18:21:00 - web - INFO - web_app.py:606 - 文件处理完成，生成了1个下载文件
2025-06-18 18:21:14 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 18:21:14 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 18:21:14 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 18:21:14 - web - INFO - web_app.py:563 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 18:21:28 - web - INFO - web_app.py:984 - 开始生成Excel报告
2025-06-18 18:21:28 - web - INFO - web_app.py:991 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_182128.xlsx
2025-06-18 18:21:28 - web - INFO - web_app.py:606 - 文件处理完成，生成了1个下载文件
2025-06-18 18:22:20 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:23:09 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:23:45 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 18:23:45 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 18:23:45 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 18:23:45 - web - INFO - web_app.py:563 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 18:23:59 - web - INFO - web_app.py:984 - 开始生成Excel报告
2025-06-18 18:23:59 - web - INFO - web_app.py:991 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_182359.xlsx
2025-06-18 18:23:59 - web - INFO - web_app.py:606 - 文件处理完成，生成了1个下载文件
2025-06-18 18:25:55 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:26:45 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:30:16 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:30:30 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:30:56 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:30:58 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:33:03 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 18:33:03 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 18:33:03 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 18:33:03 - web - INFO - web_app.py:563 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 18:33:15 - web - INFO - web_app.py:984 - 开始生成Excel报告
2025-06-18 18:33:16 - web - INFO - web_app.py:991 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_183315.xlsx
2025-06-18 18:33:16 - web - INFO - web_app.py:606 - 文件处理完成，生成了1个下载文件
2025-06-18 18:34:06 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:34:44 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:36:33 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 18:36:33 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 18:36:33 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 18:36:33 - web - INFO - web_app.py:563 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 18:36:45 - web - INFO - web_app.py:984 - 开始生成Excel报告
2025-06-18 18:36:45 - web - INFO - web_app.py:991 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_183645.xlsx
2025-06-18 18:36:45 - web - INFO - web_app.py:606 - 文件处理完成，生成了1个下载文件
2025-06-18 18:37:52 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:39:26 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:40:32 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:42:42 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:43:40 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:48:02 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:48:26 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:49:26 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-18 18:49:27 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-18 18:49:27 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-18 18:49:27 - web - INFO - web_app.py:563 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-18 18:49:39 - web - INFO - web_app.py:984 - 开始生成Excel报告
2025-06-18 18:49:39 - web - INFO - web_app.py:991 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250618_184939.xlsx
2025-06-18 18:49:39 - web - INFO - web_app.py:606 - 文件处理完成，生成了1个下载文件
2025-06-18 18:51:41 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:52:51 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:56:34 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 18:59:25 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 19:00:22 - web - INFO - web_app.py:32 - Web应用启动
2025-06-18 19:01:50 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:03:20 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:05:49 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:06:42 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:07:10 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:07:55 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:09:14 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:10:31 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:10:59 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:17:20 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:18:24 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 08:18:24 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-19 08:18:24 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-19 08:18:24 - web - INFO - web_app.py:563 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 08:18:25 - web - INFO - web_app.py:984 - 开始生成Excel报告
2025-06-19 08:18:26 - web - INFO - web_app.py:991 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_081825.xlsx
2025-06-19 08:18:26 - web - INFO - web_app.py:606 - 文件处理完成，生成了1个下载文件
2025-06-19 08:19:05 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:19:06 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:21:47 - web - INFO - web_app.py:910 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 08:21:47 - web - INFO - web_app.py:917 - 文件大小: 66833 字节
2025-06-19 08:21:47 - web - INFO - web_app.py:972 - 文本提取成功，内容长度: 3916 字符
2025-06-19 08:21:47 - web - INFO - web_app.py:563 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 08:21:49 - web - INFO - web_app.py:984 - 开始生成Excel报告
2025-06-19 08:21:49 - web - INFO - web_app.py:991 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_082149.xlsx
2025-06-19 08:21:49 - web - INFO - web_app.py:606 - 文件处理完成，生成了1个下载文件
2025-06-19 08:24:45 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:25:50 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:26:12 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:29:04 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:30:53 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:31:26 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:31:36 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:31:45 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:32:18 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:32:30 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:32:39 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:35:01 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:39:06 - web - INFO - web_app.py:892 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 08:39:06 - web - INFO - web_app.py:899 - 文件大小: 66833 字节
2025-06-19 08:39:06 - web - INFO - web_app.py:954 - 文本提取成功，内容长度: 3916 字符
2025-06-19 08:39:06 - web - INFO - web_app.py:545 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 08:39:08 - web - INFO - web_app.py:966 - 开始生成Excel报告
2025-06-19 08:39:08 - web - INFO - web_app.py:973 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_083908.xlsx
2025-06-19 08:39:08 - web - INFO - web_app.py:588 - 文件处理完成，生成了1个下载文件
2025-06-19 08:39:13 - web - INFO - web_app.py:659 - 访问日志管理页面
2025-06-19 08:41:43 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:42:22 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:43:23 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:43:49 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:46:28 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:48:07 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:48:52 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:49:33 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:50:06 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:51:37 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:51:38 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:51:47 - web - INFO - web_app.py:892 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 08:51:47 - web - INFO - web_app.py:899 - 文件大小: 66833 字节
2025-06-19 08:51:48 - web - INFO - web_app.py:954 - 文本提取成功，内容长度: 3916 字符
2025-06-19 08:51:48 - web - INFO - web_app.py:545 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 08:52:04 - web - INFO - web_app.py:966 - 开始生成Excel报告
2025-06-19 08:52:04 - web - INFO - web_app.py:973 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_085204.xlsx
2025-06-19 08:52:04 - web - INFO - web_app.py:588 - 文件处理完成，生成了1个下载文件
2025-06-19 08:59:27 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 08:59:40 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:00:00 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:00:39 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:02:52 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:03:02 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:03:06 - web - INFO - web_app.py:892 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 09:03:06 - web - INFO - web_app.py:899 - 文件大小: 66833 字节
2025-06-19 09:03:06 - web - INFO - web_app.py:954 - 文本提取成功，内容长度: 3916 字符
2025-06-19 09:03:06 - web - INFO - web_app.py:545 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 09:03:18 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:03:19 - web - INFO - web_app.py:892 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 09:03:19 - web - INFO - web_app.py:899 - 文件大小: 66833 字节
2025-06-19 09:03:19 - web - INFO - web_app.py:954 - 文本提取成功，内容长度: 3916 字符
2025-06-19 09:03:19 - web - INFO - web_app.py:545 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 09:03:39 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:05:27 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:07:20 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:07:32 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:07:47 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:07:59 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:10:16 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:15:43 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:15:50 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:16:05 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:16:16 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:16:32 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:16:44 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:17:32 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:19:40 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:22:14 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:23:33 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:23:39 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:24:44 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:26:09 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:27:10 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:28:26 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:28:36 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:29:19 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:29:38 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:30:04 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:31:19 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:34:57 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:37:14 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:39:04 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:39:10 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:40:33 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:40:59 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:41:08 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:41:36 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:42:53 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:42:54 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:42:59 - web - INFO - web_app.py:892 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 09:42:59 - web - INFO - web_app.py:899 - 文件大小: 66833 字节
2025-06-19 09:42:59 - web - INFO - web_app.py:954 - 文本提取成功，内容长度: 3916 字符
2025-06-19 09:42:59 - web - INFO - web_app.py:545 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 09:43:09 - web - INFO - web_app.py:892 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 09:43:09 - web - INFO - web_app.py:899 - 文件大小: 66833 字节
2025-06-19 09:43:09 - web - INFO - web_app.py:954 - 文本提取成功，内容长度: 3916 字符
2025-06-19 09:43:09 - web - INFO - web_app.py:545 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 09:43:22 - web - INFO - web_app.py:966 - 开始生成Excel报告
2025-06-19 09:43:23 - web - INFO - web_app.py:973 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_094322.xlsx
2025-06-19 09:43:23 - web - INFO - web_app.py:588 - 文件处理完成，生成了1个下载文件
2025-06-19 09:43:34 - web - INFO - web_app.py:966 - 开始生成Excel报告
2025-06-19 09:43:34 - web - INFO - web_app.py:973 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_094334.xlsx
2025-06-19 09:43:34 - web - INFO - web_app.py:588 - 文件处理完成，生成了1个下载文件
2025-06-19 09:45:17 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:45:17 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:46:07 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:46:08 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:48:00 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:48:41 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:48:41 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:49:12 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:51:22 - web - INFO - web_app.py:892 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 09:51:22 - web - INFO - web_app.py:899 - 文件大小: 66833 字节
2025-06-19 09:51:22 - web - INFO - web_app.py:954 - 文本提取成功，内容长度: 3916 字符
2025-06-19 09:51:22 - web - INFO - web_app.py:545 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 09:51:45 - web - INFO - web_app.py:966 - 开始生成Excel报告
2025-06-19 09:51:45 - web - INFO - web_app.py:973 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_095145.xlsx
2025-06-19 09:51:45 - web - INFO - web_app.py:588 - 文件处理完成，生成了1个下载文件
2025-06-19 09:53:15 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:53:15 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:53:55 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:53:55 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:55:46 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:55:46 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:59:23 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 09:59:23 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:01:03 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:01:03 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:02:44 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:02:44 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:03:47 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:05:05 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:05:05 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:05:30 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:07:11 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:07:12 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:07:22 - web - INFO - web_app.py:892 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 10:07:22 - web - INFO - web_app.py:899 - 文件大小: 66833 字节
2025-06-19 10:07:22 - web - INFO - web_app.py:954 - 文本提取成功，内容长度: 3916 字符
2025-06-19 10:07:22 - web - INFO - web_app.py:545 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 10:07:57 - web - INFO - web_app.py:966 - 开始生成Excel报告
2025-06-19 10:07:58 - web - INFO - web_app.py:973 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_100757.xlsx
2025-06-19 10:07:58 - web - INFO - web_app.py:588 - 文件处理完成，生成了1个下载文件
2025-06-19 10:09:27 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:11:02 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:11:30 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:12:10 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:14:45 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:16:14 - web - INFO - web_app.py:892 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 10:16:14 - web - INFO - web_app.py:899 - 文件大小: 66833 字节
2025-06-19 10:16:14 - web - INFO - web_app.py:954 - 文本提取成功，内容长度: 3916 字符
2025-06-19 10:16:14 - web - INFO - web_app.py:545 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 10:16:16 - web - INFO - web_app.py:966 - 开始生成Excel报告
2025-06-19 10:16:16 - web - INFO - web_app.py:973 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_101616.xlsx
2025-06-19 10:16:16 - web - INFO - web_app.py:588 - 文件处理完成，生成了1个下载文件
2025-06-19 10:19:19 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:21:00 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:21:28 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:22:06 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:24:00 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:24:24 - web - INFO - web_app.py:982 - 开始生成Excel报告
2025-06-19 10:24:24 - web - INFO - web_app.py:989 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_102424.xlsx
2025-06-19 10:24:24 - web - ERROR - web_app.py:669 - 处理文本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 629, in process_text_content
    'url': url_for('download_file', filename=filename),
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\helpers.py", line 222, in url_for
    return current_app.url_for(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\local.py", line 311, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\local.py", line 508, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-19 10:25:10 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:33:22 - web - INFO - web_app.py:908 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 10:33:22 - web - INFO - web_app.py:915 - 文件大小: 66833 字节
2025-06-19 10:33:22 - web - INFO - web_app.py:970 - 文本提取成功，内容长度: 3916 字符
2025-06-19 10:33:22 - web - INFO - web_app.py:545 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 10:34:06 - web - INFO - web_app.py:982 - 开始生成Excel报告
2025-06-19 10:34:06 - web - INFO - web_app.py:989 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_103406.xlsx
2025-06-19 10:34:06 - web - INFO - web_app.py:596 - 文件处理完成，生成了1个下载文件
2025-06-19 10:36:03 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:36:59 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:39:26 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:39:31 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:40:12 - web - INFO - web_app.py:931 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 10:40:12 - web - INFO - web_app.py:938 - 文件大小: 66833 字节
2025-06-19 10:40:12 - web - INFO - web_app.py:993 - 文本提取成功，内容长度: 3916 字符
2025-06-19 10:40:12 - web - INFO - web_app.py:568 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 10:40:31 - web - INFO - web_app.py:1005 - 开始生成Excel报告
2025-06-19 10:40:32 - web - INFO - web_app.py:1012 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_104031.xlsx
2025-06-19 10:40:32 - web - INFO - web_app.py:619 - 文件处理完成，生成了1个下载文件
2025-06-19 10:42:12 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:43:01 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:43:12 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:43:33 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:43:43 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:43:53 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:43:55 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:44:31 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:46:10 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:46:21 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:48:02 - web - INFO - web_app.py:933 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 10:48:02 - web - INFO - web_app.py:940 - 文件大小: 66833 字节
2025-06-19 10:48:02 - web - INFO - web_app.py:995 - 文本提取成功，内容长度: 3916 字符
2025-06-19 10:48:02 - web - INFO - web_app.py:570 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 10:48:16 - web - INFO - web_app.py:1007 - 开始生成Excel报告
2025-06-19 10:48:16 - web - INFO - web_app.py:1014 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_104816.xlsx
2025-06-19 10:48:16 - web - INFO - web_app.py:621 - 文件处理完成，生成了1个下载文件
2025-06-19 10:50:25 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:50:28 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:50:37 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:51:06 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:52:56 - web - INFO - web_app.py:933 - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 10:52:56 - web - INFO - web_app.py:940 - 文件大小: 66833 字节
2025-06-19 10:52:56 - web - INFO - web_app.py:995 - 文本提取成功，内容长度: 3916 字符
2025-06-19 10:52:56 - web - INFO - web_app.py:570 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 10:53:11 - web - INFO - web_app.py:1007 - 开始生成Excel报告
2025-06-19 10:53:11 - web - INFO - web_app.py:1014 - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_105311.xlsx
2025-06-19 10:53:11 - web - INFO - web_app.py:621 - 文件处理完成，生成了1个下载文件
2025-06-19 10:55:36 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:56:02 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:57:58 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:58:21 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 10:58:26 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:08:40 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:27:17 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:27:18 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:28:32 - web - INFO - web_app.py:933 - 开始从文件提取文本: F:\PYTHON~1\制度智~1\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 11:28:32 - web - INFO - web_app.py:940 - 文件大小: 66833 字节
2025-06-19 11:28:32 - web - INFO - web_app.py:995 - 文本提取成功，内容长度: 3916 字符
2025-06-19 11:28:32 - web - INFO - web_app.py:570 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 11:28:48 - web - INFO - web_app.py:1007 - 开始生成Excel报告
2025-06-19 11:28:48 - web - INFO - web_app.py:1014 - Excel报告生成成功: F:\PYTHON~1\制度智~1\downloads\approval_process_report_20250619_112848.xlsx
2025-06-19 11:28:48 - web - INFO - web_app.py:621 - 文件处理完成，生成了1个下载文件
2025-06-19 11:32:08 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:32:09 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:32:34 - web - INFO - web_app.py:933 - 开始从文件提取文本: F:\PYTHON~1\制度智~1\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 11:32:34 - web - INFO - web_app.py:940 - 文件大小: 66833 字节
2025-06-19 11:32:34 - web - INFO - web_app.py:995 - 文本提取成功，内容长度: 3916 字符
2025-06-19 11:32:34 - web - INFO - web_app.py:570 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 11:32:54 - web - INFO - web_app.py:1007 - 开始生成Excel报告
2025-06-19 11:32:54 - web - INFO - web_app.py:1014 - Excel报告生成成功: F:\PYTHON~1\制度智~1\downloads\approval_process_report_20250619_113254.xlsx
2025-06-19 11:32:54 - web - INFO - web_app.py:621 - 文件处理完成，生成了1个下载文件
2025-06-19 11:33:11 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:33:12 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:33:26 - web - INFO - web_app.py:935 - 开始从文件提取文本: F:\PYTHON~1\制度智~1\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 11:33:26 - web - INFO - web_app.py:942 - 文件大小: 66833 字节
2025-06-19 11:33:26 - web - INFO - web_app.py:997 - 文本提取成功，内容长度: 3916 字符
2025-06-19 11:33:26 - web - INFO - web_app.py:571 - 提取的文本内容: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 11:33:26 - web - INFO - web_app.py:572 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 11:33:41 - web - INFO - web_app.py:1009 - 开始生成Excel报告
2025-06-19 11:33:42 - web - INFO - web_app.py:1016 - Excel报告生成成功: F:\PYTHON~1\制度智~1\downloads\approval_process_report_20250619_113341.xlsx
2025-06-19 11:33:42 - web - INFO - web_app.py:623 - 文件处理完成，生成了1个下载文件
2025-06-19 11:48:50 - web - INFO - web_app.py:935 - 开始从文件提取文本: F:\PYTHON~1\制度智~1\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 11:48:50 - web - INFO - web_app.py:942 - 文件大小: 66833 字节
2025-06-19 11:48:50 - web - INFO - web_app.py:997 - 文本提取成功，内容长度: 3916 字符
2025-06-19 11:48:50 - web - INFO - web_app.py:571 - 提取的文本内容: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 11:48:50 - web - INFO - web_app.py:572 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 11:49:07 - web - INFO - web_app.py:1009 - 开始生成Excel报告
2025-06-19 11:49:07 - web - INFO - web_app.py:1016 - Excel报告生成成功: F:\PYTHON~1\制度智~1\downloads\approval_process_report_20250619_114907.xlsx
2025-06-19 11:49:07 - web - INFO - web_app.py:623 - 文件处理完成，生成了1个下载文件
2025-06-19 11:51:20 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:52:02 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:52:05 - web - INFO - web_app.py:111 - upload_file: 开始处理
2025-06-19 11:52:05 - web - INFO - web_app.py:937 - 开始从文件提取文本: F:\PYTHON~1\制度智~1\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 11:52:05 - web - INFO - web_app.py:944 - 文件大小: 66833 字节
2025-06-19 11:52:05 - web - INFO - web_app.py:999 - 文本提取成功，内容长度: 3916 字符
2025-06-19 11:52:05 - web - INFO - web_app.py:574 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 11:52:20 - web - INFO - web_app.py:1011 - 开始生成Excel报告
2025-06-19 11:52:20 - web - INFO - web_app.py:1018 - Excel报告生成成功: F:\PYTHON~1\制度智~1\downloads\approval_process_report_20250619_115220.xlsx
2025-06-19 11:52:20 - web - INFO - web_app.py:625 - 文件处理完成，生成了1个下载文件
2025-06-19 11:52:20 - web - INFO - web_app.py:137 - 提取的文本内容 result: {'success': True, 'process_data': {'process_name': '制度审批流程分析', 'steps': [{'step_number': 1, 'step_name': '采购申请提交', 'description': '需求部门填写采购申请表', 'department': '部门', 'action': '处理'}, {'step_number': 2, 'step_name': '部门初审', 'description': '需求部门负责人审核申请合理性', 'department': '部门', 'action': '审核'}, {'step_number': 3, 'step_name': '预算审核', 'description': '财务部核查预算可用性', 'department': '财务部', 'action': '处理'}, {'step_number': 4, 'step_name': '采购方案会签', 'description': '技术部（技术参数审核）、法务部（合同条款审核）', 'department': '技术部', 'action': '审核'}, {'step_number': 5, 'step_name': '分管领导审批', 'description': '分管副总经理批准采购方案', 'department': '总经理', 'action': '处理'}, {'step_number': 6, 'step_name': '总经理签批', 'description': '重大采购（金额≥50万）需总经理签字', 'department': '总经理', 'action': '签字'}, {'step_number': 7, 'step_name': '采购执行备案', 'description': '采购部存档审批文件并执行', 'department': '采购部', 'action': '审批'}, {'step_number': 1, 'step_name': '关键词锚定', 'description': '- 原文出现“提交采购申请”→对应“发起”；“审核”“会签”“签批”等直接匹配职责类型。', 'department': '未指定', 'action': '审核'}, {'step_number': 2, 'step_name': '多部门协作', 'description': '- “技术部、法务部参与采购方案确认”通过“会签”关键词定位为协作端。', 'department': '技术部', 'action': '确认'}, {'step_number': 3, 'step_name': '流程分级', 'description': '- 根据“金额≥50万需总经理审批”划分条件分支流程（示例中简化为单一路径）。', 'department': '总经理', 'action': '审批'}], 'total_steps': 10, 'original_analysis': '### 示例分析（假设制度文本为某公司《采购管理制度》节选）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算审核**：财务部核查预算可用性  \n4. **采购方案会签**：技术部（技术参数审核）、法务部（合同条款审核）  \n5. **分管领导审批**：分管副总经理批准采购方案  \n6. **总经理签批**：重大采购（金额≥50万）需总经理签字  \n7. **采购执行备案**：采购部存档审批文件并执行  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 采购申请提交     | 需求部门       | 发起           |  \n| 部门初审         | 需求部门       | 审核           |  \n| 预算审核         | 财务部         | 复核           |  \n| 采购方案会签     | 技术部/法务部  | 协作（会签）   |  \n| 分管领导审批     | 副总经理       | 确认（核准）   |  \n| 总经理签批       | 总经理办公室   | 确认（终审）   |  \n| 采购执行备案     | 采购部         | 执行           |  \n\n---  \n### 关键分析逻辑说明：  \n1. **关键词锚定**：  \n   - 原文出现“提交采购申请”→对应“发起”；“审核”“会签”“签批”等直接匹配职责类型。  \n   - “预算可用性核查”隐含“复核”职责，需结合财务部职能推断。  \n\n2. **多部门协作**：  \n   - “技术部、法务部参与采购方案确认”通过“会签”关键词定位为协作端。  \n\n3. **流程分级**：  \n   - 根据“金额≥50万需总经理审批”划分条件分支流程（示例中简化为单一路径）。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为方法论演示。', 'analysis_source': '中石化AI平台', 'analysis_type': '标准化模板', 'template_indicator': True, 'note': '🏭 此结果为中石化AI平台提供的行业标准流程模板，供参考使用'}, 'file_info': {'file_path': 'F:\\PYTHON~1\\制度智~1\\uploads\\GHNLH-B1801-43-808-2025-2-XXZX022.docx', 'file_name': 'GHNLH-B1801-43-808-2025-2-XXZX022.docx', 'file_size': '65.27 KB', 'file_type': 'Word文档', 'raw_size': 66833, 'extension': '.docx', 'processed_at': '2025-06-19 11:52:20', 'processing_time': '15.59秒'}, 'download_links': [{'format': 'Excel报告', 'filename': 'approval_process_report_20250619_115220.xlsx', 'url': '/download/approval_process_report_20250619_115220.xlsx', 'file_type': 'excel'}], 'llm_info': {'provider': 'sinopec', 'processing_time': '15.23秒', 'response_length': 1047, 'content_length': 3916, 'content_type': '文档内容'}, 'llm_analysis': {'content': '企业制度-执行类 \n1  基本要求\n1.1  目的依据\n为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。\n1.2  规范内容界定\n1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。\n1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。\n1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。\n1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。\n1.3  业务管理原则\n坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。\n1.4  相关术语\n各单位是指公司属各部门、各基层单位、各公司。\n2  职责与分工\n2.1  综合管理部信息中心\n2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。\n2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。\n2.1.3  负责组织公司信息化应用架构设计、维护和发布。\n2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。\n\n2.2  域长单位\n2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。\n2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。\n2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。\n2.2.4  负责组织本业务域数据治理工作。\n2.2.5  负责组织本业务域信息系统的深化应用。\n2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。\n\n2.3  各\n\n2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。\n2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。\n2.4  党委组织部\n负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。\n\n\n\n\n\n3  管理内容与要求\n3.1  顶层设计\n3.1.1  数字化转型方案制定\n3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。\n3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。\n3.1.2  应用架构设计\n3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。\n*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。\n*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。\n3.1.3  全域APP设计\n3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。\n3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。\n3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。\n3.2  业务流程标准化\n3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。\n3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。\n3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。\n3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。\n3.3  数据治理\n3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具\n3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。\n3.4  深化应用\n域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。\n4  工作机制\n4.1  工作例会\n4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。\n\n4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。\n4.2  工作评价\n4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。\n4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。\n\n5  检查与监督\n综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元\n5.1  未落实域长单位责任领导和本业务域的专家团队的；\n5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；\n5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；\n5.4  未按要求组织本业务域数据治理工作的；\n5.5  未按要求组织本业务域信息系统的深化应用的；\n5.6  未按要求组织本业务域信息化建设与应用工作例会的；\n\n5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。\n6  附件\n6.1  业务职责分工表\n6.2  检查考核评价表\n6.3  域长负责制管理流程\n6.4  各业务域数据治理域长责任部门及人员表\n\n\n\n\n\n表格 1:\n | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则\n | 制度编号 |  |  | \n | 制度版本 |  | 主办部门 | 综合管理部\n所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部\n下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | \n监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | \n解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | \n制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型\n制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》\n适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司\n涉及的相关制度 | / | / | / | /\n废止说明 |  |  |  | \n\n', 'content_type': '用户上传的文档内容', 'sent_to_ai': '企业制度-执行类 \n1  基本要求\n1.1  目的依据\n为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。\n1.2  规范内容界定\n1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。\n1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。\n1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。\n1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。\n1.3  业务管理原则\n坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。\n1.4  相关术语\n各单位是指公司属各部门、各基层单位、各公司。\n2  职责与分工\n2.1  综合管理部信息中心\n2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。\n2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。\n2.1.3  负责组织公司信息化应用架构设计、维护和发布。\n2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。\n\n2.2  域长单位\n2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。\n2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。\n2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。\n2.2.4  负责组织本业务域数据治理工作。\n2.2.5  负责组织本业务域信息系统的深化应用。\n2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。\n\n2.3  各\n\n2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。\n2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。\n2.4  党委组织部\n负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。\n\n\n\n\n\n3  管理内容与要求\n3.1  顶层设计\n3.1.1  数字化转型方案制定\n3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。\n3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。\n3.1.2  应用架构设计\n3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。\n*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。\n*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。\n3.1.3  全域APP设计\n3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。\n3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。\n3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。\n3.2  业务流程标准化\n3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。\n3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。\n3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。\n3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。\n3.3  数据治理\n3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具\n3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。\n3.4  深化应用\n域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。\n4  工作机制\n4.1  工作例会\n4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。\n\n4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。\n4.2  工作评价\n4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。\n4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。\n\n5  检查与监督\n综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元\n5.1  未落实域长单位责任领导和本业务域的专家团队的；\n5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；\n5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；\n5.4  未按要求组织本业务域数据治理工作的；\n5.5  未按要求组织本业务域信息系统的深化应用的；\n5.6  未按要求组织本业务域信息化建设与应用工作例会的；\n\n5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。\n6  附件\n6.1  业务职责分工表\n6.2  检查考核评价表\n6.3  域长负责制管理流程\n6.4  各业务域数据治理域长责任部门及人员表\n\n\n\n\n\n表格 1:\n | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则\n | 制度编号 |  |  | \n | 制度版本 |  | 主办部门 | 综合管理部\n所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部\n下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | \n监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | \n解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | \n制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型\n制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》\n适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司\n涉及的相关制度 | / | / | / | /\n废止说明 |  |  |  | \n\n', 'raw_response': "{'output': '### 示例分析（假设制度文本为某公司《采购管理制度》节选）  \\n\\n---  \\n#### **1. 审批流程环节**  \\n1. **采购申请提交**：需求部门填写采购申请表  \\n2. **部门初审**：需求部门负责人审核申请合理性  \\n3. **预算审核**：财务部核查预算可用性  \\n4. **采购方案会签**：技术部（技术参数审核）、法务部（合同条款审核）  \\n5. **分管领导审批**：分管副总经理批准采购方案  \\n6. **总经理签批**：重大采购（金额≥50万）需总经理签字  \\n7. **采购执行备案**：采购部存档审批文件并执行  \\n\\n---  \\n#### **2. 部门职责节点表**  \\n| 流程环节         | 负责部门       | 职责类型       |  \\n|------------------|----------------|----------------|  \\n| 采购申请提交     | 需求部门       | 发起           |  \\n| 部门初审         | 需求部门       | 审核           |  \\n| 预算审核         | 财务部         | 复核           |  \\n| 采购方案会签     | 技术部/法务部  | 协作（会签）   |  \\n| 分管领导审批     | 副总经理       | 确认（核准）   |  \\n| 总经理签批       | 总经理办公室   | 确认（终审）   |  \\n| 采购执行备案     | 采购部         | 执行           |  \\n\\n---  \\n### 关键分析逻辑说明：  \\n1. **关键词锚定**：  \\n   - 原文出现“提交采购申请”→对应“发起”；“审核”“会签”“签批”等直接匹配职责类型。  \\n   - “预算可用性核查”隐含“复核”职责，需结合财务部职能推断。  \\n\\n2. **多部门协作**：  \\n   - “技术部、法务部参与采购方案确认”通过“会签”关键词定位为协作端。  \\n\\n3. **流程分级**：  \\n   - 根据“金额≥50万需总经理审批”划分条件分支流程（示例中简化为单一路径）。  \\n\\n---  \\n**注**：实际分析需根据具体制度文本调整，以上仅为方法论演示。'}", 'analysis_timestamp': '2025-06-19 11:52:20', 'note': '中石化AI平台分析结果'}}
2025-06-19 11:53:05 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:53:07 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:53:19 - web - INFO - web_app.py:111 - upload_file: 开始处理
2025-06-19 11:53:19 - web - INFO - web_app.py:937 - 开始从文件提取文本: F:\PYTHON~1\制度智~1\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 11:53:19 - web - INFO - web_app.py:944 - 文件大小: 66833 字节
2025-06-19 11:53:19 - web - INFO - web_app.py:999 - 文本提取成功，内容长度: 3916 字符
2025-06-19 11:53:19 - web - INFO - web_app.py:574 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 11:53:38 - web - INFO - web_app.py:1011 - 开始生成Excel报告
2025-06-19 11:53:38 - web - INFO - web_app.py:1018 - Excel报告生成成功: F:\PYTHON~1\制度智~1\downloads\approval_process_report_20250619_115338.xlsx
2025-06-19 11:53:38 - web - INFO - web_app.py:625 - 文件处理完成，生成了1个下载文件
2025-06-19 11:53:38 - web - INFO - web_app.py:137 - 提取的文本内容 result: {'success': True, 'process_data': {'process_name': '制度审批流程分析', 'steps': [{'step_number': 1, 'step_name': '采购申请提交', 'description': '需求部门填写采购申请表', 'department': '部门', 'action': '处理'}, {'step_number': 2, 'step_name': '部门初审', 'description': '需求部门负责人审核申请合理性', 'department': '部门', 'action': '审核'}, {'step_number': 3, 'step_name': '预算审核', 'description': '财务部核查预算可用性', 'department': '财务部', 'action': '处理'}, {'step_number': 4, 'step_name': '采购方案会签', 'description': '技术部（技术参数审核）、法务部（合同条款审核）', 'department': '技术部', 'action': '审核'}, {'step_number': 5, 'step_name': '分管领导审批', 'description': '分管副总经理批准采购方案', 'department': '总经理', 'action': '处理'}, {'step_number': 6, 'step_name': '总经理终审', 'description': '重大采购（金额≥50万）需总经理签字', 'department': '总经理', 'action': '签字'}, {'step_number': 7, 'step_name': '采购执行备案', 'description': '采购部存档审批文件并执行', 'department': '采购部', 'action': '审批'}, {'step_number': 1, 'step_name': '锚定段落示例', 'description': '- 原文：“生产部提交采购申请，经部门负责人审核后，送财务部进行预算复核。”', 'department': '财务部', 'action': '审核'}, {'step_number': 2, 'step_name': '隐性节点处理', 'description': '- “报分管领导审批”中的“报”对应“提交审核”动作，但实际职责为“确认端”。', 'department': '未指定', 'action': '审核'}, {'step_number': 3, 'step_name': '多流程分类', 'description': '- 若文本同时包含“日常采购”和“重大采购”流程，需分表列示差异环节（如是否需总经理终审）。', 'department': '总经理', 'action': '处理'}], 'total_steps': 10, 'original_analysis': '### 示例分析（假设制度文本为某公司《采购管理制度》节选）  \n\n---  \n#### **1. 审批流程环节**  \n1. **采购申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算审核**：财务部核查预算可用性  \n4. **采购方案会签**：技术部（技术参数审核）、法务部（合同条款审核）  \n5. **分管领导审批**：分管副总经理批准采购方案  \n6. **总经理终审**：重大采购（金额≥50万）需总经理签字  \n7. **采购执行备案**：采购部存档审批文件并执行  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节           | 负责部门         | 职责类型       |  \n|--------------------|------------------|----------------|  \n| 采购申请提交       | 需求部门（如生产部） | 发起端         |  \n| 部门初审           | 需求部门负责人   | 审核端         |  \n| 预算审核           | 财务部           | 审核端         |  \n| 采购方案会签       | 技术部、法务部   | 协作端（会签） |  \n| 分管领导审批       | 副总经理         | 确认端         |  \n| 总经理终审         | 总经理办公室     | 确认端         |  \n| 采购执行备案       | 采购部           | 协作端（备案） |  \n\n---  \n### 关键提取逻辑说明  \n1. **锚定段落示例**：  \n   - 原文：“生产部提交采购申请，经部门负责人审核后，送财务部进行预算复核。”  \n     → 提取环节：部门初审（生产部负责人）、预算审核（财务部）  \n   - 原文：“技术参数由技术部会签，合同条款需法务部确认后方可报分管领导审批。”  \n     → 提取环节：会签（技术部、法务部）、分管领导审批  \n\n2. **隐性节点处理**：  \n   - “报分管领导审批”中的“报”对应“提交审核”动作，但实际职责为“确认端”。  \n   - “存档”对应“备案”职责，归类为协作端。  \n\n3. **多流程分类**：  \n   - 若文本同时包含“日常采购”和“重大采购”流程，需分表列示差异环节（如是否需总经理终审）。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为方法论示例。', 'analysis_source': '中石化AI平台', 'analysis_type': '标准化模板', 'template_indicator': True, 'note': '🏭 此结果为中石化AI平台提供的行业标准流程模板，供参考使用'}, 'file_info': {'file_path': 'F:\\PYTHON~1\\制度智~1\\uploads\\GHNLH-B1801-43-808-2025-2-XXZX022.docx', 'file_name': 'GHNLH-B1801-43-808-2025-2-XXZX022.docx', 'file_size': '65.27 KB', 'file_type': 'Word文档', 'raw_size': 66833, 'extension': '.docx', 'processed_at': '2025-06-19 11:53:38', 'processing_time': '19.15秒'}, 'download_links': [{'format': 'Excel报告', 'filename': 'approval_process_report_20250619_115338.xlsx', 'url': '/download/approval_process_report_20250619_115338.xlsx', 'file_type': 'excel'}], 'llm_info': {'provider': 'sinopec', 'processing_time': '18.84秒', 'response_length': 1190, 'content_length': 3916, 'content_type': '文档内容'}, 'llm_analysis': {'content': '企业制度-执行类 \n1  基本要求\n1.1  目的依据\n为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。\n1.2  规范内容界定\n1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。\n1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。\n1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。\n1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。\n1.3  业务管理原则\n坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。\n1.4  相关术语\n各单位是指公司属各部门、各基层单位、各公司。\n2  职责与分工\n2.1  综合管理部信息中心\n2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。\n2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。\n2.1.3  负责组织公司信息化应用架构设计、维护和发布。\n2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。\n\n2.2  域长单位\n2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。\n2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。\n2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。\n2.2.4  负责组织本业务域数据治理工作。\n2.2.5  负责组织本业务域信息系统的深化应用。\n2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。\n\n2.3  各\n\n2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。\n2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。\n2.4  党委组织部\n负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。\n\n\n\n\n\n3  管理内容与要求\n3.1  顶层设计\n3.1.1  数字化转型方案制定\n3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。\n3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。\n3.1.2  应用架构设计\n3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。\n*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。\n*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。\n3.1.3  全域APP设计\n3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。\n3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。\n3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。\n3.2  业务流程标准化\n3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。\n3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。\n3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。\n3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。\n3.3  数据治理\n3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具\n3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。\n3.4  深化应用\n域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。\n4  工作机制\n4.1  工作例会\n4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。\n\n4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。\n4.2  工作评价\n4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。\n4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。\n\n5  检查与监督\n综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元\n5.1  未落实域长单位责任领导和本业务域的专家团队的；\n5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；\n5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；\n5.4  未按要求组织本业务域数据治理工作的；\n5.5  未按要求组织本业务域信息系统的深化应用的；\n5.6  未按要求组织本业务域信息化建设与应用工作例会的；\n\n5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。\n6  附件\n6.1  业务职责分工表\n6.2  检查考核评价表\n6.3  域长负责制管理流程\n6.4  各业务域数据治理域长责任部门及人员表\n\n\n\n\n\n表格 1:\n | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则\n | 制度编号 |  |  | \n | 制度版本 |  | 主办部门 | 综合管理部\n所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部\n下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | \n监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | \n解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | \n制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型\n制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》\n适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司\n涉及的相关制度 | / | / | / | /\n废止说明 |  |  |  | \n\n', 'content_type': '用户上传的文档内容', 'sent_to_ai': '企业制度-执行类 \n1  基本要求\n1.1  目的依据\n为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。\n1.2  规范内容界定\n1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。\n1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。\n1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。\n1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。\n1.3  业务管理原则\n坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。\n1.4  相关术语\n各单位是指公司属各部门、各基层单位、各公司。\n2  职责与分工\n2.1  综合管理部信息中心\n2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。\n2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。\n2.1.3  负责组织公司信息化应用架构设计、维护和发布。\n2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。\n\n2.2  域长单位\n2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。\n2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。\n2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。\n2.2.4  负责组织本业务域数据治理工作。\n2.2.5  负责组织本业务域信息系统的深化应用。\n2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。\n\n2.3  各\n\n2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。\n2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。\n2.4  党委组织部\n负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。\n\n\n\n\n\n3  管理内容与要求\n3.1  顶层设计\n3.1.1  数字化转型方案制定\n3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。\n3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。\n3.1.2  应用架构设计\n3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。\n*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。\n*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。\n3.1.3  全域APP设计\n3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。\n3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。\n3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。\n3.2  业务流程标准化\n3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。\n3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。\n3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。\n3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。\n3.3  数据治理\n3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具\n3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。\n3.4  深化应用\n域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。\n4  工作机制\n4.1  工作例会\n4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。\n\n4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。\n4.2  工作评价\n4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。\n4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。\n\n5  检查与监督\n综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元\n5.1  未落实域长单位责任领导和本业务域的专家团队的；\n5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；\n5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；\n5.4  未按要求组织本业务域数据治理工作的；\n5.5  未按要求组织本业务域信息系统的深化应用的；\n5.6  未按要求组织本业务域信息化建设与应用工作例会的；\n\n5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。\n6  附件\n6.1  业务职责分工表\n6.2  检查考核评价表\n6.3  域长负责制管理流程\n6.4  各业务域数据治理域长责任部门及人员表\n\n\n\n\n\n表格 1:\n | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则\n | 制度编号 |  |  | \n | 制度版本 |  | 主办部门 | 综合管理部\n所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部\n下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | \n监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | \n解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | \n制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型\n制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》\n适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司\n涉及的相关制度 | / | / | / | /\n废止说明 |  |  |  | \n\n', 'raw_response': "{'output': '### 示例分析（假设制度文本为某公司《采购管理制度》节选）  \\n\\n---  \\n#### **1. 审批流程环节**  \\n1. **采购申请提交**：需求部门填写采购申请表  \\n2. **部门初审**：需求部门负责人审核申请合理性  \\n3. **预算审核**：财务部核查预算可用性  \\n4. **采购方案会签**：技术部（技术参数审核）、法务部（合同条款审核）  \\n5. **分管领导审批**：分管副总经理批准采购方案  \\n6. **总经理终审**：重大采购（金额≥50万）需总经理签字  \\n7. **采购执行备案**：采购部存档审批文件并执行  \\n\\n---  \\n#### **2. 部门职责节点表**  \\n| 流程环节           | 负责部门         | 职责类型       |  \\n|--------------------|------------------|----------------|  \\n| 采购申请提交       | 需求部门（如生产部） | 发起端         |  \\n| 部门初审           | 需求部门负责人   | 审核端         |  \\n| 预算审核           | 财务部           | 审核端         |  \\n| 采购方案会签       | 技术部、法务部   | 协作端（会签） |  \\n| 分管领导审批       | 副总经理         | 确认端         |  \\n| 总经理终审         | 总经理办公室     | 确认端         |  \\n| 采购执行备案       | 采购部           | 协作端（备案） |  \\n\\n---  \\n### 关键提取逻辑说明  \\n1. **锚定段落示例**：  \\n   - 原文：“生产部提交采购申请，经部门负责人审核后，送财务部进行预算复核。”  \\n     → 提取环节：部门初审（生产部负责人）、预算审核（财务部）  \\n   - 原文：“技术参数由技术部会签，合同条款需法务部确认后方可报分管领导审批。”  \\n     → 提取环节：会签（技术部、法务部）、分管领导审批  \\n\\n2. **隐性节点处理**：  \\n   - “报分管领导审批”中的“报”对应“提交审核”动作，但实际职责为“确认端”。  \\n   - “存档”对应“备案”职责，归类为协作端。  \\n\\n3. **多流程分类**：  \\n   - 若文本同时包含“日常采购”和“重大采购”流程，需分表列示差异环节（如是否需总经理终审）。  \\n\\n---  \\n**注**：实际分析需根据具体制度文本调整，以上仅为方法论示例。'}", 'analysis_timestamp': '2025-06-19 11:53:38', 'note': '中石化AI平台分析结果'}}
2025-06-19 11:55:21 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:55:29 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:55:33 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:55:42 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:55:44 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 11:55:51 - web - INFO - web_app.py:111 - upload_file: 开始处理
2025-06-19 11:55:51 - web - INFO - web_app.py:939 - 开始从文件提取文本: F:\PYTHON~1\制度智~1\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 11:55:51 - web - INFO - web_app.py:946 - 文件大小: 66833 字节
2025-06-19 11:55:51 - web - INFO - web_app.py:1001 - 文本提取成功，内容长度: 3916 字符
2025-06-19 11:55:51 - web - INFO - web_app.py:572 - 提取的文本内容 text_content: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 11:55:51 - web - INFO - web_app.py:574 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 11:56:11 - web - INFO - web_app.py:578 - 提取的文本内容 llm_result: {'success': True, 'data': {'process_name': '制度审批流程分析', 'steps': [{'step_number': 1, 'step_name': '申请提交', 'description': '需求部门填写采购申请表', 'department': '部门', 'action': '处理'}, {'step_number': 2, 'step_name': '部门初审', 'description': '需求部门负责人审核申请合理性', 'department': '部门', 'action': '审核'}, {'step_number': 3, 'step_name': '预算复核', 'description': '财务部核对预算额度', 'department': '财务部', 'action': '处理'}, {'step_number': 4, 'step_name': '采购审核', 'description': '采购部评估供应商及价格', 'department': '采购部', 'action': '处理'}, {'step_number': 5, 'step_name': '会签审批', 'description': '- 金额≤10万：分管领导审批', 'department': '未指定', 'action': '审批'}, {'step_number': 6, 'step_name': '合同签订', 'description': '法务部审核条款后，采购部签署合同', 'department': '采购部', 'action': '审核'}, {'step_number': 7, 'step_name': '验收备案', 'description': '使用部门验收，行政部归档文件', 'department': '部门', 'action': '处理'}, {'step_number': 1, 'step_name': '锚定段落示例', 'description': '- 原文：“采购申请需经部门负责人**审核**后，**提交**财务部**复核**预算”（关键词：审核、提交、复核）→ 对应环节2、3', 'department': '财务部', 'action': '审核'}, {'step_number': 2, 'step_name': '隐性节点处理', 'description': '- 未直接出现“发起”但“需求部门填写申请表”隐含发起动作；', 'department': '部门', 'action': '发起'}, {'step_number': 3, 'step_name': '多条件分支', 'description': '- 根据金额区分审批路径（环节5），需在流程中明确分叉逻辑。', 'department': '未指定', 'action': '审批'}], 'total_steps': 10, 'original_analysis': '### 示例分析（假设制度文本为《XX公司采购管理制度》节选）  \n\n---  \n#### **1. 审批流程环节**  \n1. **申请提交**：需求部门填写采购申请表  \n2. **部门初审**：需求部门负责人审核申请合理性  \n3. **预算复核**：财务部核对预算额度  \n4. **采购审核**：采购部评估供应商及价格  \n5. **会签审批**：  \n   - 金额≤10万：分管领导审批  \n   - 金额＞10万：分管领导+总经理双签  \n6. **合同签订**：法务部审核条款后，采购部签署合同  \n7. **验收备案**：使用部门验收，行政部归档文件  \n\n---  \n#### **2. 部门职责节点表**  \n| 流程环节       | 负责部门       | 职责类型       |  \n|----------------|----------------|----------------|  \n| 申请提交       | 需求部门       | 发起           |  \n| 部门初审       | 需求部门       | 审核（初审）   |  \n| 预算复核       | 财务部         | 审核（合规性） |  \n| 采购审核       | 采购部         | 审核（专业性） |  \n| 会签审批       | 分管领导/总经理| 确认（决策）   |  \n| 合同签订       | 法务部+采购部  | 协作+确认      |  \n| 验收备案       | 使用部门+行政部| 协作+确认      |  \n\n---  \n### 关键提取逻辑说明  \n1. **锚定段落示例**：  \n   - 原文：“采购申请需经部门负责人**审核**后，**提交**财务部**复核**预算”（关键词：审核、提交、复核）→ 对应环节2、3  \n   - 原文：“合同签署前须由法务部**会签**，确认条款合法性”（关键词：会签）→ 对应环节6  \n\n2. **隐性节点处理**：  \n   - 未直接出现“发起”但“需求部门填写申请表”隐含发起动作；  \n   - “验收”未明确写审批，但实际为流程终点，故归为确认端。  \n\n3. **多条件分支**：  \n   - 根据金额区分审批路径（环节5），需在流程中明确分叉逻辑。  \n\n---  \n**注**：实际分析需根据具体制度文本调整，以上仅为方法论演示。', 'analysis_source': '中石化AI平台', 'analysis_type': '标准化模板', 'template_indicator': True, 'note': '🏭 此结果为中石化AI平台提供的行业标准流程模板，供参考使用'}, 'message': '成功解析JSON响应', 'llm_info': {'provider': 'sinopec', 'processing_time': '19.98秒', 'response_length': 1100, 'content_length': 3916, 'content_type': '文档内容'}, 'llm_analysis': {'content': '企业制度-执行类 \n1  基本要求\n1.1  目的依据\n为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。\n1.2  规范内容界定\n1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。\n1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。\n1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。\n1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。\n1.3  业务管理原则\n坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。\n1.4  相关术语\n各单位是指公司属各部门、各基层单位、各公司。\n2  职责与分工\n2.1  综合管理部信息中心\n2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。\n2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。\n2.1.3  负责组织公司信息化应用架构设计、维护和发布。\n2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。\n\n2.2  域长单位\n2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。\n2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。\n2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。\n2.2.4  负责组织本业务域数据治理工作。\n2.2.5  负责组织本业务域信息系统的深化应用。\n2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。\n\n2.3  各\n\n2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。\n2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。\n2.4  党委组织部\n负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。\n\n\n\n\n\n3  管理内容与要求\n3.1  顶层设计\n3.1.1  数字化转型方案制定\n3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。\n3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。\n3.1.2  应用架构设计\n3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。\n*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。\n*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。\n3.1.3  全域APP设计\n3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。\n3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。\n3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。\n3.2  业务流程标准化\n3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。\n3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。\n3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。\n3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。\n3.3  数据治理\n3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具\n3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。\n3.4  深化应用\n域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。\n4  工作机制\n4.1  工作例会\n4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。\n\n4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。\n4.2  工作评价\n4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。\n4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。\n\n5  检查与监督\n综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元\n5.1  未落实域长单位责任领导和本业务域的专家团队的；\n5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；\n5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；\n5.4  未按要求组织本业务域数据治理工作的；\n5.5  未按要求组织本业务域信息系统的深化应用的；\n5.6  未按要求组织本业务域信息化建设与应用工作例会的；\n\n5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。\n6  附件\n6.1  业务职责分工表\n6.2  检查考核评价表\n6.3  域长负责制管理流程\n6.4  各业务域数据治理域长责任部门及人员表\n\n\n\n\n\n表格 1:\n | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则\n | 制度编号 |  |  | \n | 制度版本 |  | 主办部门 | 综合管理部\n所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部\n下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | \n监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | \n解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | \n制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型\n制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》\n适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司\n涉及的相关制度 | / | / | / | /\n废止说明 |  |  |  | \n\n', 'content_type': '用户上传的文档内容', 'sent_to_ai': '企业制度-执行类 \n1  基本要求\n1.1  目的依据\n为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。\n1.2  规范内容界定\n1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。\n1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。\n1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。\n1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。\n1.3  业务管理原则\n坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。\n1.4  相关术语\n各单位是指公司属各部门、各基层单位、各公司。\n2  职责与分工\n2.1  综合管理部信息中心\n2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。\n2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。\n2.1.3  负责组织公司信息化应用架构设计、维护和发布。\n2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。\n\n2.2  域长单位\n2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。\n2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。\n2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。\n2.2.4  负责组织本业务域数据治理工作。\n2.2.5  负责组织本业务域信息系统的深化应用。\n2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。\n\n2.3  各\n\n2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。\n2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。\n2.4  党委组织部\n负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。\n\n\n\n\n\n3  管理内容与要求\n3.1  顶层设计\n3.1.1  数字化转型方案制定\n3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。\n3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。\n3.1.2  应用架构设计\n3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。\n*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。\n*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。\n3.1.3  全域APP设计\n3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。\n3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。\n3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。\n3.2  业务流程标准化\n3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。\n3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。\n3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。\n3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。\n3.3  数据治理\n3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具\n3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。\n3.4  深化应用\n域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。\n4  工作机制\n4.1  工作例会\n4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。\n\n4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。\n4.2  工作评价\n4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。\n4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。\n\n5  检查与监督\n综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元\n5.1  未落实域长单位责任领导和本业务域的专家团队的；\n5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；\n5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；\n5.4  未按要求组织本业务域数据治理工作的；\n5.5  未按要求组织本业务域信息系统的深化应用的；\n5.6  未按要求组织本业务域信息化建设与应用工作例会的；\n\n5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。\n6  附件\n6.1  业务职责分工表\n6.2  检查考核评价表\n6.3  域长负责制管理流程\n6.4  各业务域数据治理域长责任部门及人员表\n\n\n\n\n\n表格 1:\n | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则\n | 制度编号 |  |  | \n | 制度版本 |  | 主办部门 | 综合管理部\n所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部\n下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | \n监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | \n解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | \n制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型\n制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》\n适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司\n涉及的相关制度 | / | / | / | /\n废止说明 |  |  |  | \n\n', 'raw_response': "{'output': '### 示例分析（假设制度文本为《XX公司采购管理制度》节选）  \\n\\n---  \\n#### **1. 审批流程环节**  \\n1. **申请提交**：需求部门填写采购申请表  \\n2. **部门初审**：需求部门负责人审核申请合理性  \\n3. **预算复核**：财务部核对预算额度  \\n4. **采购审核**：采购部评估供应商及价格  \\n5. **会签审批**：  \\n   - 金额≤10万：分管领导审批  \\n   - 金额＞10万：分管领导+总经理双签  \\n6. **合同签订**：法务部审核条款后，采购部签署合同  \\n7. **验收备案**：使用部门验收，行政部归档文件  \\n\\n---  \\n#### **2. 部门职责节点表**  \\n| 流程环节       | 负责部门       | 职责类型       |  \\n|----------------|----------------|----------------|  \\n| 申请提交       | 需求部门       | 发起           |  \\n| 部门初审       | 需求部门       | 审核（初审）   |  \\n| 预算复核       | 财务部         | 审核（合规性） |  \\n| 采购审核       | 采购部         | 审核（专业性） |  \\n| 会签审批       | 分管领导/总经理| 确认（决策）   |  \\n| 合同签订       | 法务部+采购部  | 协作+确认      |  \\n| 验收备案       | 使用部门+行政部| 协作+确认      |  \\n\\n---  \\n### 关键提取逻辑说明  \\n1. **锚定段落示例**：  \\n   - 原文：“采购申请需经部门负责人**审核**后，**提交**财务部**复核**预算”（关键词：审核、提交、复核）→ 对应环节2、3  \\n   - 原文：“合同签署前须由法务部**会签**，确认条款合法性”（关键词：会签）→ 对应环节6  \\n\\n2. **隐性节点处理**：  \\n   - 未直接出现“发起”但“需求部门填写申请表”隐含发起动作；  \\n   - “验收”未明确写审批，但实际为流程终点，故归为确认端。  \\n\\n3. **多条件分支**：  \\n   - 根据金额区分审批路径（环节5），需在流程中明确分叉逻辑。  \\n\\n---  \\n**注**：实际分析需根据具体制度文本调整，以上仅为方法论演示。'}", 'analysis_timestamp': '2025-06-19 11:56:11', 'note': '中石化AI平台分析结果'}}
2025-06-19 11:56:11 - web - INFO - web_app.py:1013 - 开始生成Excel报告
2025-06-19 11:56:11 - web - INFO - web_app.py:1020 - Excel报告生成成功: F:\PYTHON~1\制度智~1\downloads\approval_process_report_20250619_115611.xlsx
2025-06-19 11:56:11 - web - INFO - web_app.py:627 - 文件处理完成，生成了1个下载文件
2025-06-19 12:11:56 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:12:38 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:13:16 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:13:55 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:14:32 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:15:33 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:17:39 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:19:09 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:20:33 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:23:53 - web - INFO - web_app.py:111 - upload_file: 开始处理
2025-06-19 12:23:54 - web - INFO - web_app.py:939 - 开始从文件提取文本: F:\PYTHON~1\制度智~1\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 12:23:54 - web - INFO - web_app.py:946 - 文件大小: 66833 字节
2025-06-19 12:23:54 - web - INFO - web_app.py:1001 - 文本提取成功，内容长度: 3916 字符
2025-06-19 12:23:54 - web - INFO - web_app.py:572 - 提取的文本内容 text_content: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 12:23:54 - web - INFO - web_app.py:574 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 12:24:34 - web - INFO - web_app.py:578 - 提取的文本内容 llm_result: {'success': True, 'data': {'process_name': '制度审批流程分析', 'steps': [{'step_number': 1, 'step_name': '步骤1', 'description': '**信息化项目储备审核**', 'department': '域长单位', 'action': '审核'}, {'step_number': 2, 'step_name': '步骤2', 'description': '**可研报告审核**', 'department': '域长单位', 'action': '审核'}, {'step_number': 3, 'step_name': '步骤3', 'description': '**跨业务域协调审批**', 'department': '综合管理部信息中心', 'action': '审核/统筹'}, {'step_number': 4, 'step_name': '步骤4', 'description': '**应用架构设计评审**', 'department': '域长单位（业务域级）', 'action': '发起/审核'}, {'step_number': 5, 'step_name': '步骤5', 'description': '综合管理部信息中心（公司级）', 'department': '确认/发布', 'action': '*******（统一发布）'}, {'step_number': 6, 'step_name': '步骤6', 'description': '**业务流程标准化评审**', 'department': '域长单位（业务域级）', 'action': '发起/审核'}, {'step_number': 7, 'step_name': '步骤7', 'description': '党委组织部（公司级）', 'department': '确认', 'action': '3.2.4（组织评审）'}, {'step_number': 8, 'step_name': '步骤8', 'description': '**数据治理计划审批**', 'department': '综合管理部信息中心', 'action': '审核/统筹'}, {'step_number': 9, 'step_name': '步骤9', 'description': '域长单位', 'department': '发起/执行', 'action': '3.3.2（编制计划）'}, {'step_number': 10, 'step_name': '步骤10', 'description': '**信息化需求立项论证**', 'department': '综合管理部信息中心', 'action': '审核/决策'}, {'step_number': 11, 'step_name': '步骤11', 'description': '**域长负责制工作评价**', 'department': '综合管理部信息中心', 'action': '发起/确认'}, {'step_number': 12, 'step_name': '步骤12', 'description': '**检查与监督整改审批**', 'department': '综合管理部信息中心', 'action': '审核/考核'}], 'total_steps': 12, 'original_analysis': '### 审批流程环节及部门职责节点分析\n\n#### 1. **审批流程环节**（按顺序列出）  \n1. **信息化项目储备审核**  \n2. **可研报告审核**  \n3. **跨业务域协调审批**  \n4. **应用架构设计与评审**  \n   - 业务域应用架构设计评审  \n   - 公司级应用架构设计评审  \n5. **业务流程标准化评审**  \n   - 业务域内跨组织/系统流程评审  \n   - 公司级跨业务域流程评审  \n6. **数据治理计划审批**  \n7. **信息化需求立项论证**  \n8. **域长负责制工作评价与发布**  \n9. **检查与监督问题整改审批**  \n\n---\n\n#### 2. **部门职责节点表**  \n| 流程环节                  | 负责部门/单位               | 职责类型       | 依据条款（简化）                  |  \n|---------------------------|-----------------------------|----------------|-----------------------------------|  \n| **信息化项目储备审核**     | 域长单位                    | 审核           | 2.2.1（参与审核）                |  \n| **可研报告审核**           | 域长单位                    | 审核           | 2.2.1（参与审核）                |  \n| **跨业务域协调审批**       | 综合管理部信息中心          | 审核/统筹      | 2.1.4（资源调配）                |  \n| **应用架构设计评审**       | 域长单位（业务域级）        | 发起/审核      | *******（设计+评审）             |  \n|                           | 综合管理部信息中心（公司级）| 确认/发布      | *******（统一发布）              |  \n| **业务流程标准化评审**     | 域长单位（业务域级）        | 发起/审核      | 3.2.2-3.2.3（组织评审）          |  \n|                           | 党委组织部（公司级）        | 确认           | 3.2.4（组织评审）                |  \n| **数据治理计划审批**       | 综合管理部信息中心          | 审核/统筹      | 3.3.1（制定标准）                |  \n|                           | 域长单位                    | 发起/执行      | 3.3.2（编制计划）                |  \n| **信息化需求立项论证**     | 综合管理部信息中心          | 审核/决策      | 4.1.1（需求论证会议）            |  \n| **域长负责制工作评价**     | 综合管理部信息中心          | 发起/确认      | 4.2.2（评价+发布）               |  \n| **检查与监督整改审批**     | 综合管理部信息中心          | 审核/考核      | 第5章（问题通报+奖惩）           |  \n\n---\n\n### 关键说明：  \n1. **发起与审核分离**：  \n   - 域长单位多为业务域内流程的发起者和初步审核者（如应用架构设计、业务流程标准化），而综合管理部信息中心负责跨域协调和公司级确认。  \n   - 党委组织部仅在业务流程标准化中担任公司级评审角色。  \n\n2. **隐性流程**：  \n   - **会签部门**（如科技发展部、财务资产部等）未在正文中明确具体审批动作，但表格1显示其为“会签部门”，推测参与跨域项目或重大决策的联合审核（需结合附件补充）。  \n\n3. **动态管理**：  \n   - 应用架构、数据治理等环节的“动态更新”隐含非固定审批周期，由域长单位和信息中心协同维护。  \n\n4. **奖惩流程**：  \n   - 检查考核（第5章）由综合管理部信息中心直接执行，无需其他部门审批，但需通报结果。  \n\n**注**：若需更详细流程（如附件中的《域长负责制管理流程》），需进一步分析附件内容。', 'analysis_source': '中石化AI平台', 'analysis_type': '流程解析', 'template_indicator': False, 'note': '🏭 基于文档内容的流程分析结果'}, 'message': '成功解析JSON响应', 'llm_info': {'provider': 'sinopec', 'processing_time': '40.74秒', 'response_length': 2101, 'content_length': 3916, 'content_type': '文档内容'}, 'llm_analysis': {'content': '企业制度-执行类 \n1  基本要求\n1.1  目的依据\n为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。\n1.2  规范内容界定\n1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。\n1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。\n1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。\n1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。\n1.3  业务管理原则\n坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。\n1.4  相关术语\n各单位是指公司属各部门、各基层单位、各公司。\n2  职责与分工\n2.1  综合管理部信息中心\n2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。\n2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。\n2.1.3  负责组织公司信息化应用架构设计、维护和发布。\n2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。\n\n2.2  域长单位\n2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。\n2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。\n2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。\n2.2.4  负责组织本业务域数据治理工作。\n2.2.5  负责组织本业务域信息系统的深化应用。\n2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。\n\n2.3  各\n\n2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。\n2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。\n2.4  党委组织部\n负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。\n\n\n\n\n\n3  管理内容与要求\n3.1  顶层设计\n3.1.1  数字化转型方案制定\n3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。\n3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。\n3.1.2  应用架构设计\n3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。\n*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。\n*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。\n3.1.3  全域APP设计\n3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。\n3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。\n3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。\n3.2  业务流程标准化\n3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。\n3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。\n3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。\n3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。\n3.3  数据治理\n3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具\n3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。\n3.4  深化应用\n域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。\n4  工作机制\n4.1  工作例会\n4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。\n\n4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。\n4.2  工作评价\n4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。\n4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。\n\n5  检查与监督\n综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元\n5.1  未落实域长单位责任领导和本业务域的专家团队的；\n5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；\n5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；\n5.4  未按要求组织本业务域数据治理工作的；\n5.5  未按要求组织本业务域信息系统的深化应用的；\n5.6  未按要求组织本业务域信息化建设与应用工作例会的；\n\n5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。\n6  附件\n6.1  业务职责分工表\n6.2  检查考核评价表\n6.3  域长负责制管理流程\n6.4  各业务域数据治理域长责任部门及人员表\n\n\n\n\n\n表格 1:\n | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则\n | 制度编号 |  |  | \n | 制度版本 |  | 主办部门 | 综合管理部\n所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部\n下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | \n监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | \n解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | \n制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型\n制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》\n适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司\n涉及的相关制度 | / | / | / | /\n废止说明 |  |  |  | \n\n', 'content_type': '用户上传的文档内容', 'sent_to_ai': '企业制度-执行类 \n1  基本要求\n1.1  目的依据\n为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。\n1.2  规范内容界定\n1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。\n1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。\n1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。\n1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。\n1.3  业务管理原则\n坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。\n1.4  相关术语\n各单位是指公司属各部门、各基层单位、各公司。\n2  职责与分工\n2.1  综合管理部信息中心\n2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。\n2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。\n2.1.3  负责组织公司信息化应用架构设计、维护和发布。\n2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。\n\n2.2  域长单位\n2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。\n2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。\n2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。\n2.2.4  负责组织本业务域数据治理工作。\n2.2.5  负责组织本业务域信息系统的深化应用。\n2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。\n\n2.3  各\n\n2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。\n2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。\n2.4  党委组织部\n负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。\n\n\n\n\n\n3  管理内容与要求\n3.1  顶层设计\n3.1.1  数字化转型方案制定\n3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。\n3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。\n3.1.2  应用架构设计\n3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。\n*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。\n*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。\n3.1.3  全域APP设计\n3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。\n3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。\n3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。\n3.2  业务流程标准化\n3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。\n3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。\n3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。\n3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。\n3.3  数据治理\n3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具\n3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。\n3.4  深化应用\n域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。\n4  工作机制\n4.1  工作例会\n4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。\n\n4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。\n4.2  工作评价\n4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。\n4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。\n\n5  检查与监督\n综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元\n5.1  未落实域长单位责任领导和本业务域的专家团队的；\n5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；\n5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；\n5.4  未按要求组织本业务域数据治理工作的；\n5.5  未按要求组织本业务域信息系统的深化应用的；\n5.6  未按要求组织本业务域信息化建设与应用工作例会的；\n\n5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。\n6  附件\n6.1  业务职责分工表\n6.2  检查考核评价表\n6.3  域长负责制管理流程\n6.4  各业务域数据治理域长责任部门及人员表\n\n\n\n\n\n表格 1:\n | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则\n | 制度编号 |  |  | \n | 制度版本 |  | 主办部门 | 综合管理部\n所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部\n下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | \n监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | \n解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | \n制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型\n制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》\n适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司\n涉及的相关制度 | / | / | / | /\n废止说明 |  |  |  | \n\n', 'raw_response': "{'output': '### 审批流程环节及部门职责节点分析\\n\\n#### 1. **审批流程环节**（按顺序列出）  \\n1. **信息化项目储备审核**  \\n2. **可研报告审核**  \\n3. **跨业务域协调审批**  \\n4. **应用架构设计与评审**  \\n   - 业务域应用架构设计评审  \\n   - 公司级应用架构设计评审  \\n5. **业务流程标准化评审**  \\n   - 业务域内跨组织/系统流程评审  \\n   - 公司级跨业务域流程评审  \\n6. **数据治理计划审批**  \\n7. **信息化需求立项论证**  \\n8. **域长负责制工作评价与发布**  \\n9. **检查与监督问题整改审批**  \\n\\n---\\n\\n#### 2. **部门职责节点表**  \\n| 流程环节                  | 负责部门/单位               | 职责类型       | 依据条款（简化）                  |  \\n|---------------------------|-----------------------------|----------------|-----------------------------------|  \\n| **信息化项目储备审核**     | 域长单位                    | 审核           | 2.2.1（参与审核）                |  \\n| **可研报告审核**           | 域长单位                    | 审核           | 2.2.1（参与审核）                |  \\n| **跨业务域协调审批**       | 综合管理部信息中心          | 审核/统筹      | 2.1.4（资源调配）                |  \\n| **应用架构设计评审**       | 域长单位（业务域级）        | 发起/审核      | *******（设计+评审）             |  \\n|                           | 综合管理部信息中心（公司级）| 确认/发布      | *******（统一发布）              |  \\n| **业务流程标准化评审**     | 域长单位（业务域级）        | 发起/审核      | 3.2.2-3.2.3（组织评审）          |  \\n|                           | 党委组织部（公司级）        | 确认           | 3.2.4（组织评审）                |  \\n| **数据治理计划审批**       | 综合管理部信息中心          | 审核/统筹      | 3.3.1（制定标准）                |  \\n|                           | 域长单位                    | 发起/执行      | 3.3.2（编制计划）                |  \\n| **信息化需求立项论证**     | 综合管理部信息中心          | 审核/决策      | 4.1.1（需求论证会议）            |  \\n| **域长负责制工作评价**     | 综合管理部信息中心          | 发起/确认      | 4.2.2（评价+发布）               |  \\n| **检查与监督整改审批**     | 综合管理部信息中心          | 审核/考核      | 第5章（问题通报+奖惩）           |  \\n\\n---\\n\\n### 关键说明：  \\n1. **发起与审核分离**：  \\n   - 域长单位多为业务域内流程的发起者和初步审核者（如应用架构设计、业务流程标准化），而综合管理部信息中心负责跨域协调和公司级确认。  \\n   - 党委组织部仅在业务流程标准化中担任公司级评审角色。  \\n\\n2. **隐性流程**：  \\n   - **会签部门**（如科技发展部、财务资产部等）未在正文中明确具体审批动作，但表格1显示其为“会签部门”，推测参与跨域项目或重大决策的联合审核（需结合附件补充）。  \\n\\n3. **动态管理**：  \\n   - 应用架构、数据治理等环节的“动态更新”隐含非固定审批周期，由域长单位和信息中心协同维护。  \\n\\n4. **奖惩流程**：  \\n   - 检查考核（第5章）由综合管理部信息中心直接执行，无需其他部门审批，但需通报结果。  \\n\\n**注**：若需更详细流程（如附件中的《域长负责制管理流程》），需进一步分析附件内容。'}", 'analysis_timestamp': '2025-06-19 12:24:34', 'note': '中石化AI平台分析结果'}}
2025-06-19 12:24:34 - web - INFO - web_app.py:1013 - 开始生成Excel报告
2025-06-19 12:24:35 - web - INFO - web_app.py:1020 - Excel报告生成成功: F:\PYTHON~1\制度智~1\downloads\approval_process_report_20250619_122434.xlsx
2025-06-19 12:24:35 - web - INFO - web_app.py:627 - 文件处理完成，生成了1个下载文件
2025-06-19 12:35:15 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:37:06 - web - INFO - web_app.py:111 - upload_file: 开始处理
2025-06-19 12:37:07 - web - INFO - web_app.py:939 - 开始从文件提取文本: F:\PYTHON~1\制度智~1\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 12:37:07 - web - INFO - web_app.py:946 - 文件大小: 66833 字节
2025-06-19 12:37:07 - web - INFO - web_app.py:1001 - 文本提取成功，内容长度: 3916 字符
2025-06-19 12:37:07 - web - INFO - web_app.py:572 - 提取的文本内容 text_content: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 12:37:07 - web - INFO - web_app.py:574 - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 12:37:44 - web - INFO - web_app.py:578 - 提取的文本内容 llm_result: {'success': True, 'data': {'process_name': '制度审批流程分析', 'steps': [{'step_number': 1, 'step_name': '步骤1', 'description': '信息化项目储备审核', 'department': '域长单位', 'action': '审核'}, {'step_number': 2, 'step_name': '步骤2', 'description': '可研报告审核', 'department': '域长单位', 'action': '审核'}, {'step_number': 3, 'step_name': '步骤3', 'description': '应用架构设计', 'department': '域长单位', 'action': '发起/执行'}, {'step_number': 4, 'step_name': '步骤4', 'description': '应用架构评审', 'department': '域长单位、综合管理部信息中心', 'action': '审核/确认'}, {'step_number': 5, 'step_name': '步骤5', 'description': '业务流程标准化评审', 'department': '域长单位、党委组织部', 'action': '审核/确认'}, {'step_number': 6, 'step_name': '步骤6', 'description': '全域APP设计统筹', 'department': '综合管理部信息中心', 'action': '确认/管理'}, {'step_number': 7, 'step_name': '步骤7', 'description': '数据治理计划实施', 'department': '域长单位', 'action': '发起/执行'}, {'step_number': 8, 'step_name': '步骤8', 'description': '数据治理标准制定', 'department': '综合管理部信息中心', 'action': '审核/确认'}, {'step_number': 9, 'step_name': '步骤9', 'description': '工作评价与考核', 'department': '综合管理部信息中心', 'action': '确认/监督'}], 'total_steps': 9, 'original_analysis': '### 审批流程环节及部门职责节点分析  \n\n#### 1. **审批流程环节**（按顺序梳理）  \n**（1）信息化项目储备审核**  \n- 原文依据：  \n  - 2.2.1 域长单位职责："参与信息化项目储备审核、可研报告审核、跨业务域协调等工作"  \n  - 4.1.1 工作例会："论证信息化需求是否具备立项条件"  \n\n**（2）可研报告审核**  \n- 原文依据：  \n  - 2.2.1 域长单位职责："参与信息化项目储备审核、可研报告审核、跨业务域协调等工作"  \n\n**（3）应用架构设计与评审**  \n- 原文依据：  \n  - ******* 域长单位："组织开展本业务域应用架构设计和专家评审"  \n  - ******* 综合管理部信息中心："组织开展公司应用架构设计和专家评审，并统一发布和管理"  \n\n**（4）业务流程标准化评审**  \n- 原文依据：  \n  - 3.2.3 域长单位："组织本业务域专家进行评审"  \n  - 3.2.4 党委组织部："组织对各业务域的跨组织、跨系统的端到端业务流程进行评审"  \n\n**（5）全域APP设计统筹管理**  \n- 原文依据：  \n  - 3.1.3.3 综合管理部信息中心："对各业务域设计的全域APP进行统筹管理"  \n\n**（6）数据治理计划审核与实施**  \n- 原文依据：  \n  - 3.3.1 综合管理部信息中心："组织建立数据治理组织，制定数据标准、方法"  \n  - 3.3.2 域长单位："编制数据治理计划，开展数据资源盘点"  \n\n**（7）工作评价与考核**  \n- 原文依据：  \n  - 4.2.2 综合管理部信息中心："每年组织开展域长负责制工作评价，形成评价结果并发布"  \n  - 5 检查与监督："对问题较多的单位给予通报并限期整改"  \n\n---\n\n#### 2. **部门职责节点表**  \n| 流程环节               | 负责部门               | 职责类型       | 原文依据（关键动作） |  \n|------------------------|------------------------|----------------|----------------------|  \n| 信息化项目储备审核     | 域长单位               | 审核           | 2.2.1 "参与审核"     |  \n| 可研报告审核           | 域长单位               | 审核           | 2.2.1 "参与审核"     |  \n| 应用架构设计           | 域长单位               | 发起/执行      | ******* "组织开展设计" |  \n| 应用架构评审           | 域长单位、综合管理部信息中心 | 审核/确认      | *******-******* "专家评审、统一发布" |  \n| 业务流程标准化评审     | 域长单位、党委组织部   | 审核/确认      | 3.2.3-3.2.4 "组织评审" |  \n| 全域APP设计统筹        | 综合管理部信息中心     | 确认/管理      | 3.1.3.3 "统筹管理"   |  \n| 数据治理计划实施       | 域长单位               | 发起/执行      | 3.3.2 "编制计划、开展盘点" |  \n| 数据治理标准制定       | 综合管理部信息中心     | 审核/确认      | 3.3.1 "制定标准"     |  \n| 工作评价与考核         | 综合管理部信息中心     | 确认/监督      | 4.2.2 "评价并发布"、5 "检查考核" |  \n\n---\n\n### 关键发现  \n1. **多层级审核机制**：  \n   - 域长单位负责业务域内初步审核（如项目储备、可研报告），综合管理部信息中心负责跨域统筹和最终确认（如应用架构、全域APP）。  \n   - 党委组织部在业务流程标准化中承担终审角色（3.2.4）。  \n\n2. **协作型节点**：  \n   - **会签部门**（表格1中列出的科技发展部、财务资产部等）虽未在细则正文中明确审批动作，但作为制度会签方，可能参与跨域协同流程（如2.1.4提到的"跨域资源调配"）。  \n\n3. **隐性流程**：  \n   - **专家评审**是重要环节（*******、3.2.3），但专家团队组建由域长单位主导（2.2.1），未明确具体审批步骤。  \n\n4. **监督闭环**：  \n   - 综合管理部信息中心全程监督，从需求论证（4.1.1）到结果考核（5），形成完整闭环。  \n\n注：若需进一步细化，可结合附件《域长负责制管理流程》补充具体流程图。', 'analysis_source': '中石化AI平台', 'analysis_type': '流程解析', 'template_indicator': False, 'note': '🏭 基于文档内容的流程分析结果'}, 'message': '成功解析JSON响应', 'llm_info': {'provider': 'sinopec', 'processing_time': '36.97秒', 'response_length': 2161, 'content_length': 3916, 'content_type': '文档内容'}, 'llm_analysis': {'content': '企业制度-执行类 \n1  基本要求\n1.1  目的依据\n为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。\n1.2  规范内容界定\n1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。\n1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。\n1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。\n1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。\n1.3  业务管理原则\n坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。\n1.4  相关术语\n各单位是指公司属各部门、各基层单位、各公司。\n2  职责与分工\n2.1  综合管理部信息中心\n2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。\n2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。\n2.1.3  负责组织公司信息化应用架构设计、维护和发布。\n2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。\n\n2.2  域长单位\n2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。\n2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。\n2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。\n2.2.4  负责组织本业务域数据治理工作。\n2.2.5  负责组织本业务域信息系统的深化应用。\n2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。\n\n2.3  各\n\n2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。\n2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。\n2.4  党委组织部\n负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。\n\n\n\n\n\n3  管理内容与要求\n3.1  顶层设计\n3.1.1  数字化转型方案制定\n3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。\n3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。\n3.1.2  应用架构设计\n3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。\n*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。\n*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。\n3.1.3  全域APP设计\n3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。\n3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。\n3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。\n3.2  业务流程标准化\n3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。\n3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。\n3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。\n3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。\n3.3  数据治理\n3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具\n3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。\n3.4  深化应用\n域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。\n4  工作机制\n4.1  工作例会\n4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。\n\n4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。\n4.2  工作评价\n4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。\n4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。\n\n5  检查与监督\n综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元\n5.1  未落实域长单位责任领导和本业务域的专家团队的；\n5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；\n5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；\n5.4  未按要求组织本业务域数据治理工作的；\n5.5  未按要求组织本业务域信息系统的深化应用的；\n5.6  未按要求组织本业务域信息化建设与应用工作例会的；\n\n5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。\n6  附件\n6.1  业务职责分工表\n6.2  检查考核评价表\n6.3  域长负责制管理流程\n6.4  各业务域数据治理域长责任部门及人员表\n\n\n\n\n\n表格 1:\n | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则\n | 制度编号 |  |  | \n | 制度版本 |  | 主办部门 | 综合管理部\n所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部\n下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | \n监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | \n解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | \n制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型\n制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》\n适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司\n涉及的相关制度 | / | / | / | /\n废止说明 |  |  |  | \n\n', 'content_type': '用户上传的文档内容', 'sent_to_ai': '企业制度-执行类 \n1  基本要求\n1.1  目的依据\n为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。\n1.2  规范内容界定\n1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。\n1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。\n1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。\n1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。\n1.3  业务管理原则\n坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。\n1.4  相关术语\n各单位是指公司属各部门、各基层单位、各公司。\n2  职责与分工\n2.1  综合管理部信息中心\n2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。\n2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。\n2.1.3  负责组织公司信息化应用架构设计、维护和发布。\n2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。\n\n2.2  域长单位\n2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。\n2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。\n2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。\n2.2.4  负责组织本业务域数据治理工作。\n2.2.5  负责组织本业务域信息系统的深化应用。\n2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。\n\n2.3  各\n\n2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。\n2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。\n2.4  党委组织部\n负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。\n\n\n\n\n\n3  管理内容与要求\n3.1  顶层设计\n3.1.1  数字化转型方案制定\n3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。\n3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。\n3.1.2  应用架构设计\n3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。\n*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。\n*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。\n3.1.3  全域APP设计\n3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。\n3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。\n3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。\n3.2  业务流程标准化\n3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。\n3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。\n3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。\n3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。\n3.3  数据治理\n3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具\n3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。\n3.4  深化应用\n域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。\n4  工作机制\n4.1  工作例会\n4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。\n\n4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。\n4.2  工作评价\n4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。\n4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。\n\n5  检查与监督\n综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元\n5.1  未落实域长单位责任领导和本业务域的专家团队的；\n5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；\n5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；\n5.4  未按要求组织本业务域数据治理工作的；\n5.5  未按要求组织本业务域信息系统的深化应用的；\n5.6  未按要求组织本业务域信息化建设与应用工作例会的；\n\n5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。\n6  附件\n6.1  业务职责分工表\n6.2  检查考核评价表\n6.3  域长负责制管理流程\n6.4  各业务域数据治理域长责任部门及人员表\n\n\n\n\n\n表格 1:\n | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则\n | 制度编号 |  |  | \n | 制度版本 |  | 主办部门 | 综合管理部\n所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部\n下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | \n监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | \n解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | \n制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型\n制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》\n适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司\n涉及的相关制度 | / | / | / | /\n废止说明 |  |  |  | \n\n', 'raw_response': '{\'output\': \'### 审批流程环节及部门职责节点分析  \\n\\n#### 1. **审批流程环节**（按顺序梳理）  \\n**（1）信息化项目储备审核**  \\n- 原文依据：  \\n  - 2.2.1 域长单位职责："参与信息化项目储备审核、可研报告审核、跨业务域协调等工作"  \\n  - 4.1.1 工作例会："论证信息化需求是否具备立项条件"  \\n\\n**（2）可研报告审核**  \\n- 原文依据：  \\n  - 2.2.1 域长单位职责："参与信息化项目储备审核、可研报告审核、跨业务域协调等工作"  \\n\\n**（3）应用架构设计与评审**  \\n- 原文依据：  \\n  - ******* 域长单位："组织开展本业务域应用架构设计和专家评审"  \\n  - ******* 综合管理部信息中心："组织开展公司应用架构设计和专家评审，并统一发布和管理"  \\n\\n**（4）业务流程标准化评审**  \\n- 原文依据：  \\n  - 3.2.3 域长单位："组织本业务域专家进行评审"  \\n  - 3.2.4 党委组织部："组织对各业务域的跨组织、跨系统的端到端业务流程进行评审"  \\n\\n**（5）全域APP设计统筹管理**  \\n- 原文依据：  \\n  - 3.1.3.3 综合管理部信息中心："对各业务域设计的全域APP进行统筹管理"  \\n\\n**（6）数据治理计划审核与实施**  \\n- 原文依据：  \\n  - 3.3.1 综合管理部信息中心："组织建立数据治理组织，制定数据标准、方法"  \\n  - 3.3.2 域长单位："编制数据治理计划，开展数据资源盘点"  \\n\\n**（7）工作评价与考核**  \\n- 原文依据：  \\n  - 4.2.2 综合管理部信息中心："每年组织开展域长负责制工作评价，形成评价结果并发布"  \\n  - 5 检查与监督："对问题较多的单位给予通报并限期整改"  \\n\\n---\\n\\n#### 2. **部门职责节点表**  \\n| 流程环节               | 负责部门               | 职责类型       | 原文依据（关键动作） |  \\n|------------------------|------------------------|----------------|----------------------|  \\n| 信息化项目储备审核     | 域长单位               | 审核           | 2.2.1 "参与审核"     |  \\n| 可研报告审核           | 域长单位               | 审核           | 2.2.1 "参与审核"     |  \\n| 应用架构设计           | 域长单位               | 发起/执行      | ******* "组织开展设计" |  \\n| 应用架构评审           | 域长单位、综合管理部信息中心 | 审核/确认      | *******-******* "专家评审、统一发布" |  \\n| 业务流程标准化评审     | 域长单位、党委组织部   | 审核/确认      | 3.2.3-3.2.4 "组织评审" |  \\n| 全域APP设计统筹        | 综合管理部信息中心     | 确认/管理      | 3.1.3.3 "统筹管理"   |  \\n| 数据治理计划实施       | 域长单位               | 发起/执行      | 3.3.2 "编制计划、开展盘点" |  \\n| 数据治理标准制定       | 综合管理部信息中心     | 审核/确认      | 3.3.1 "制定标准"     |  \\n| 工作评价与考核         | 综合管理部信息中心     | 确认/监督      | 4.2.2 "评价并发布"、5 "检查考核" |  \\n\\n---\\n\\n### 关键发现  \\n1. **多层级审核机制**：  \\n   - 域长单位负责业务域内初步审核（如项目储备、可研报告），综合管理部信息中心负责跨域统筹和最终确认（如应用架构、全域APP）。  \\n   - 党委组织部在业务流程标准化中承担终审角色（3.2.4）。  \\n\\n2. **协作型节点**：  \\n   - **会签部门**（表格1中列出的科技发展部、财务资产部等）虽未在细则正文中明确审批动作，但作为制度会签方，可能参与跨域协同流程（如2.1.4提到的"跨域资源调配"）。  \\n\\n3. **隐性流程**：  \\n   - **专家评审**是重要环节（*******、3.2.3），但专家团队组建由域长单位主导（2.2.1），未明确具体审批步骤。  \\n\\n4. **监督闭环**：  \\n   - 综合管理部信息中心全程监督，从需求论证（4.1.1）到结果考核（5），形成完整闭环。  \\n\\n注：若需进一步细化，可结合附件《域长负责制管理流程》补充具体流程图。\'}', 'analysis_timestamp': '2025-06-19 12:37:44', 'note': '中石化AI平台分析结果'}}
2025-06-19 12:37:44 - web - INFO - web_app.py:1013 - 开始生成Excel报告
2025-06-19 12:37:44 - web - INFO - web_app.py:1020 - Excel报告生成成功: F:\PYTHON~1\制度智~1\downloads\approval_process_report_20250619_123744.xlsx
2025-06-19 12:37:44 - web - INFO - web_app.py:627 - 文件处理完成，生成了1个下载文件
2025-06-19 12:45:53 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:47:20 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:51:11 - web - INFO - web_app.py:32 - Web应用启动
2025-06-19 12:57:43 - web - ERROR - web_app.py:98 - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\PYTHON~1\制度智~1\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\PYTHON~1\制度智~1\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\PYTHON~1\制度智~1\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_debug') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 13:00:12,002 - web - INFO - Web应用启动
2025-06-19 13:00:14 - web - ERROR - web_app.py:98 - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\PYTHON~1\制度智~1\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\PYTHON~1\制度智~1\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\PYTHON~1\制度智~1\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_debug') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 13:00:16 - web - ERROR - web_app.py:98 - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\PYTHON~1\制度智~1\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\PYTHON~1\制度智~1\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\PYTHON~1\制度智~1\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_debug') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 13:02:01,131 - web - INFO - Web应用启动
2025-06-19 13:02:01 - web - ERROR - web_app.py:98 - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\PYTHON~1\制度智~1\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\PYTHON~1\制度智~1\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\PYTHON~1\制度智~1\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_debug') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 14:11:33 - web - ERROR - web_app.py:98 - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\PYTHON~1\制度智~1\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\PYTHON~1\制度智~1\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\PYTHON~1\制度智~1\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_debug') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 14:11:53 - web - ERROR - web_app.py:98 - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\PYTHON~1\制度智~1\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\PYTHON~1\制度智~1\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\PYTHON~1\制度智~1\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_debug') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 14:12:04 - web - ERROR - web_app.py:98 - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\PYTHON~1\制度智~1\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\PYTHON~1\制度智~1\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\PYTHON~1\制度智~1\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_debug') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 14:15:55,203 - web - INFO - Web应用启动
2025-06-19 14:15:55,951 - web - ERROR - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\Python_project\制度智能审查\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\Python_project\制度智能审查\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_debug') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 14:19:57,428 - web - INFO - Web应用启动
2025-06-19 14:19:58,178 - web - ERROR - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\Python_project\制度智能审查\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\Python_project\制度智能审查\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_viewer') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 14:19:59,484 - web - ERROR - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\Python_project\制度智能审查\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\Python_project\制度智能审查\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_viewer') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 14:20:13,390 - web - ERROR - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\Python_project\制度智能审查\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\Python_project\制度智能审查\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_viewer') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 14:22:33,095 - web - ERROR - 首页渲染失败: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
Traceback (most recent call last):
  File "F:\Python_project\制度智能审查\web_app.py", line 96, in index
    return render_template('index_new.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 152, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\templating.py", line 133, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python312\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "F:\Python_project\制度智能审查\templates\index_new.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "F:\Python_project\制度智能审查\templates\base.html", line 330, in top-level template code
    <a class="nav-link" href="{{ url_for('api_viewer') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1071, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\flask\app.py", line 1060, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\werkzeug\routing\map.py", line 919, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'api_debug'. Did you mean 'api_providers' instead?
2025-06-19 14:23:02,621 - web - INFO - Web应用启动
2025-06-19 14:23:10,952 - web - INFO - upload_file: 开始处理
2025-06-19 14:23:10,955 - web - INFO - 文件名=GHNLH-B1801-43-808-2025-2-XXZX022海南炼化公司域长负责制运行管理细则.docx, 扩展名=docx, 允许的扩展名={'doc', 'rtf', 'odt', 'txt', 'pdf', 'xls', 'ppt', 'xlsx', 'pptx', 'docx'}
2025-06-19 14:23:11,181 - web - INFO - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 14:23:11,184 - web - INFO - 文件大小: 66833 字节
2025-06-19 14:23:11,184 - web - INFO - 文件前1KB原始内容: b'PK\x03\x04\n\x00\x00\x00\x00\x00\x87N\xe2@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\t\x00\x00\x00docProps/PK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\xc2\xd1\xd6\xdc\x96\x01\x00\x00\x9b\x02\x00\x00\x10\x00\x00\x00docProps/app.xml\x9dR\xbbn\xdb0\x14\xdd\x0b\xf4\x1f\x04\xed2)\xc9\x8a\xe2\xe0\x9a\x81\xab4S\xd1\x1a\xb0R\x8f\x01A][B%\x92 \x99 \xfe\x96\x0e]\x8af\xca\xd2.\xc9\x90\xbf\x89\xdb\xdf(U\xa7\xb2\xb3f\xbb\xe7\x1c\xf0\xf0\xdc\x07\x9c\xdetmp\x8d\xc66JN\xc3xD\xc3\x00\xa5PU#\xd7\xd3\xf0\xa2<\x8f\x8e\xc3\xc0:.+\xde*\x89\xd3p\x836<eo\xdf\xc0\xdc(\x8d\xc65h\x03o!\xed4\xac\x9d\xd3\'\x84XQc\xc7\xed\xc8\xcb\xd2++e:\xee<4k\xa2V\xabF\xe0\x99\x12W\x1dJG\x12J\x8f\x08\xde8\x94\x15V\x91\x1e\x0c\xc3\x9d\xe3\xc9\xb5{\xadi\xa5D\x9f\xcf~.7\xda\x07fPb\xa7[\xee\x90}\xec\xe3\xb4@\x06\x02\n\xd5i.7\xec\xe9\xfen\xfb\xf5\xf1\xcf\xb7_\xbf\x7f>l\xbf\xdfn\xef~\x00\xf9\xaf\xc1\x9c\xaf\xd1\xb2\t\x90]\x01Ke*\xcb\xd2q\x9e\x02\xd9\xd5P\xd4\xdcp\xe1\xfc(Y\x9ag\xb9\x7f\xbc\'\xe0C#\xfd\xfbt\x0cdWyC\xc3\xd7\x86\xeb\xfa\xd9u@P*\xc7\xdb\xb2\xe9\x90Q\x1fs\x00\xb0\x10\xbc\xc5\xc2w\xc5V\xbc\xb5\x08dO\xf4\xf6_\xec\x85.\xd5Y\xdf\xe3\xb3\xfe\x92<\xc8\xb7l\\\xbd\xd0\\\xf4\x89&\xb1\xffe\x9f\xf4@\x82\x99\xd6m#\xb8\xf3\x97\xc1\x96\xf3E\xf0\xe9\xdf\xf6.\xe3d\xe4\xcfd\x14O\xf2\x9c^\x9e\xc7\xef\xd3$\x7fWD\xc9\xd1\xa4\x88\xc6iVE\xb38K"\x9a\x15\xd9\x98\x1eS\x9a\x143 \x87N\xe0\xd7\xbf@qe\x1a\xb7\xe9;<\x84~\xbc\xc3\x11\xb0\xbfPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@<\xcf\x83u\x9d\x01\x00\x00\xb2\x02\x00\x00\x11\x00\x00\x00docProps/core.xml}\x92OK\x1bA\x1c\x86\xef\x82\xdfa\x99\xfbff7\xfe\t\xcbf\x05\x15O\n\x82\x91\x96\xde\x86\x99_\xe2\xd0\xdd\xd9ef4\xe6\xe8E\x94\x16io\xa5 \x92K\x0b=\x14BA\xc4\xf8qL6\xfd\x16\xeeN\x925\xa2x\x1c\xde\xf7\xf7\xcc3\x7f\xc2\x8d\xd3$vN@i\x91\xca&\xf2j\x049 Y\xca\x85\xec4\xd1ak\xc7m G\x1b*9\x8dS\tM\xd4\x03\x8d6\xa2\xe5\xa5\x90e\x01K\x15\xec\xab4\x03e\x04h\xa7 I\x1d\xb0\xac\x89\x8e\x8c\xc9\x02\x8c5;\x82\x84\xeaZ\xd1\x90E\xd8NUBM\xb1T\x1d\x9cQ\xf6\x99v\x00\xfb\x84\xac\xe1\x04\x0c\xe5\xd4P\\\x02\xdd\xac"\xa2\x19\x92\xb3\n\x99\x1d\xab\xd8\x028\xc3\x10C\x02\xd2h\xec\xd5<\xfc\xdc5\xa0\x12\xfd\xe6\x80M\x16\x9a\x890\xbd\xac8\xd3Lw\x91\xcd\xd94\xac\xda\xa7ZT\xc5n\xb7[\xeb\xd6\xadF\xe1\xef\xe1\x8f{\xbb\x07\xf6\xa8\xae\x90\xe5]1@Q\xc8\x99\xdd.`\n\xa8\x01\xee\x14\x80`\xba\xdd<\xf9P\xdf\xdan\xed\xa0\xc8\'~\xdd%k.Yo\x91F\xb0\xe2\x05\x84|\n\xf1\xbc5\x9b/\x81SV\xaa\xa2\xfc\xdf0\x1f\xde\xe4\x7f\xfb\xf9\xb7\xf3\xd1\xf7\x1fe\xb9\xca\xca\x87\x89\xa96{\xc5\x1b\xb6\x05\xf0\xcd^4\xbe\xbe\x9a|\x19\xfc\xffs\x16\xe2\xd7a%\x9a\xcc\x06\xde7]u\x89\xe7\xfa~i\xba\xda\x08\xea\xfe\x82\xe9\x1c`U\x8d01D\x8f\x0fg\x8fw?G\x17\xb7\xa3\xfb_\xee\xf8\xf2\xf7\xa4\xff5\x1f\x0c\x1d+<m\x94\xba\nND\xf9\xff"\xcf\xb7\x86\xd5\xda\xae^\xfe\xb2\xe8\tPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\x89\xbe\xae\n\x91\x01\x00\x00\xeb\x02\x00\x00\x13\x00\x00\x00docProps/custom.xml\xb5\x92Ao\x9b0\x18\x86\xef\x93\xf6\x1f\x90\xef\x04c @\x04\xa9\x02$\x15\xd9'
2025-06-19 14:23:11,230 - web - INFO - 文本提取成功，内容长度: 3916 字符
2025-06-19 14:23:11,232 - web - INFO - 提取的内容前100个字符: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用
2025-06-19 14:23:11,232 - web - INFO - 提取的文本内容 text_content: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 14:23:11,233 - web - INFO - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 14:23:11,234 - web - INFO - 提取的文本内容 llm_result: {'success': False, 'message': "中石化LLM处理失败: 调用中石化API失败: 'LLMProcessor' object has no attribute '_is_sinopec_api_configured'", 'data': {}}
2025-06-19 14:23:11,234 - web - INFO - 开始生成Excel报告
2025-06-19 14:23:11,619 - web - INFO - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_142311.xlsx
2025-06-19 14:23:11,620 - web - INFO - 文件处理完成，生成了1个下载文件
2025-06-19 14:30:06,367 - web - INFO - upload_file: 开始处理
2025-06-19 14:30:06,372 - web - INFO - 文件名=GHNLH-B1801-43-808-2025-2-XXZX022海南炼化公司域长负责制运行管理细则.docx, 扩展名=docx, 允许的扩展名={'doc', 'rtf', 'odt', 'txt', 'pdf', 'xls', 'ppt', 'xlsx', 'pptx', 'docx'}
2025-06-19 14:30:06,603 - web - INFO - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 14:30:06,604 - web - INFO - 文件大小: 66833 字节
2025-06-19 14:30:06,604 - web - INFO - 文件前1KB原始内容: b'PK\x03\x04\n\x00\x00\x00\x00\x00\x87N\xe2@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\t\x00\x00\x00docProps/PK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\xc2\xd1\xd6\xdc\x96\x01\x00\x00\x9b\x02\x00\x00\x10\x00\x00\x00docProps/app.xml\x9dR\xbbn\xdb0\x14\xdd\x0b\xf4\x1f\x04\xed2)\xc9\x8a\xe2\xe0\x9a\x81\xab4S\xd1\x1a\xb0R\x8f\x01A][B%\x92 \x99 \xfe\x96\x0e]\x8af\xca\xd2.\xc9\x90\xbf\x89\xdb\xdf(U\xa7\xb2\xb3f\xbb\xe7\x1c\xf0\xf0\xdc\x07\x9c\xdetmp\x8d\xc66JN\xc3xD\xc3\x00\xa5PU#\xd7\xd3\xf0\xa2<\x8f\x8e\xc3\xc0:.+\xde*\x89\xd3p\x836<eo\xdf\xc0\xdc(\x8d\xc65h\x03o!\xed4\xac\x9d\xd3\'\x84XQc\xc7\xed\xc8\xcb\xd2++e:\xee<4k\xa2V\xabF\xe0\x99\x12W\x1dJG\x12J\x8f\x08\xde8\x94\x15V\x91\x1e\x0c\xc3\x9d\xe3\xc9\xb5{\xadi\xa5D\x9f\xcf~.7\xda\x07fPb\xa7[\xee\x90}\xec\xe3\xb4@\x06\x02\n\xd5i.7\xec\xe9\xfen\xfb\xf5\xf1\xcf\xb7_\xbf\x7f>l\xbf\xdfn\xef~\x00\xf9\xaf\xc1\x9c\xaf\xd1\xb2\t\x90]\x01Ke*\xcb\xd2q\x9e\x02\xd9\xd5P\xd4\xdcp\xe1\xfc(Y\x9ag\xb9\x7f\xbc\'\xe0C#\xfd\xfbt\x0cdWyC\xc3\xd7\x86\xeb\xfa\xd9u@P*\xc7\xdb\xb2\xe9\x90Q\x1fs\x00\xb0\x10\xbc\xc5\xc2w\xc5V\xbc\xb5\x08dO\xf4\xf6_\xec\x85.\xd5Y\xdf\xe3\xb3\xfe\x92<\xc8\xb7l\\\xbd\xd0\\\xf4\x89&\xb1\xffe\x9f\xf4@\x82\x99\xd6m#\xb8\xf3\x97\xc1\x96\xf3E\xf0\xe9\xdf\xf6.\xe3d\xe4\xcfd\x14O\xf2\x9c^\x9e\xc7\xef\xd3$\x7fWD\xc9\xd1\xa4\x88\xc6iVE\xb38K"\x9a\x15\xd9\x98\x1eS\x9a\x143 \x87N\xe0\xd7\xbf@qe\x1a\xb7\xe9;<\x84~\xbc\xc3\x11\xb0\xbfPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@<\xcf\x83u\x9d\x01\x00\x00\xb2\x02\x00\x00\x11\x00\x00\x00docProps/core.xml}\x92OK\x1bA\x1c\x86\xef\x82\xdfa\x99\xfbff7\xfe\t\xcbf\x05\x15O\n\x82\x91\x96\xde\x86\x99_\xe2\xd0\xdd\xd9ef4\xe6\xe8E\x94\x16io\xa5 \x92K\x0b=\x14BA\xc4\xf8qL6\xfd\x16\xeeN\x925\xa2x\x1c\xde\xf7\xf7\xcc3\x7f\xc2\x8d\xd3$vN@i\x91\xca&\xf2j\x049 Y\xca\x85\xec4\xd1ak\xc7m G\x1b*9\x8dS\tM\xd4\x03\x8d6\xa2\xe5\xa5\x90e\x01K\x15\xec\xab4\x03e\x04h\xa7 I\x1d\xb0\xac\x89\x8e\x8c\xc9\x02\x8c5;\x82\x84\xeaZ\xd1\x90E\xd8NUBM\xb1T\x1d\x9cQ\xf6\x99v\x00\xfb\x84\xac\xe1\x04\x0c\xe5\xd4P\\\x02\xdd\xac"\xa2\x19\x92\xb3\n\x99\x1d\xab\xd8\x028\xc3\x10C\x02\xd2h\xec\xd5<\xfc\xdc5\xa0\x12\xfd\xe6\x80M\x16\x9a\x890\xbd\xac8\xd3Lw\x91\xcd\xd94\xac\xda\xa7ZT\xc5n\xb7[\xeb\xd6\xadF\xe1\xef\xe1\x8f{\xbb\x07\xf6\xa8\xae\x90\xe5]1@Q\xc8\x99\xdd.`\n\xa8\x01\xee\x14\x80`\xba\xdd<\xf9P\xdf\xdan\xed\xa0\xc8\'~\xdd%k.Yo\x91F\xb0\xe2\x05\x84|\n\xf1\xbc5\x9b/\x81SV\xaa\xa2\xfc\xdf0\x1f\xde\xe4\x7f\xfb\xf9\xb7\xf3\xd1\xf7\x1fe\xb9\xca\xca\x87\x89\xa96{\xc5\x1b\xb6\x05\xf0\xcd^4\xbe\xbe\x9a|\x19\xfc\xffs\x16\xe2\xd7a%\x9a\xcc\x06\xde7]u\x89\xe7\xfa~i\xba\xda\x08\xea\xfe\x82\xe9\x1c`U\x8d01D\x8f\x0fg\x8fw?G\x17\xb7\xa3\xfb_\xee\xf8\xf2\xf7\xa4\xff5\x1f\x0c\x1d+<m\x94\xba\nND\xf9\xff"\xcf\xb7\x86\xd5\xda\xae^\xfe\xb2\xe8\tPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\x89\xbe\xae\n\x91\x01\x00\x00\xeb\x02\x00\x00\x13\x00\x00\x00docProps/custom.xml\xb5\x92Ao\x9b0\x18\x86\xef\x93\xf6\x1f\x90\xef\x04c @\x04\xa9\x02$\x15\xd9'
2025-06-19 14:30:06,647 - web - INFO - 文本提取成功，内容长度: 3916 字符
2025-06-19 14:30:06,647 - web - INFO - 提取的内容前100个字符: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用
2025-06-19 14:30:06,647 - web - INFO - 提取的文本内容 text_content: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 14:30:06,648 - web - INFO - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 14:30:06,648 - web - INFO - 提取的文本内容 llm_result: {'success': False, 'message': "中石化LLM处理失败: 调用中石化API失败: 'LLMProcessor' object has no attribute '_is_sinopec_api_configured'", 'data': {}}
2025-06-19 14:30:06,649 - web - INFO - 开始生成Excel报告
2025-06-19 14:30:06,874 - web - INFO - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_143006.xlsx
2025-06-19 14:30:06,875 - web - INFO - 文件处理完成，生成了1个下载文件
2025-06-19 14:36:48,532 - web - INFO - Web应用启动
2025-06-19 14:37:04,800 - web - INFO - upload_file: 开始处理
2025-06-19 14:37:04,808 - web - INFO - 文件名=GHNLH-B1801-43-808-2025-2-XXZX022海南炼化公司域长负责制运行管理细则.docx, 扩展名=docx, 允许的扩展名={'doc', 'rtf', 'odt', 'txt', 'pdf', 'xls', 'ppt', 'xlsx', 'pptx', 'docx'}
2025-06-19 14:37:05,045 - web - INFO - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 14:37:05,048 - web - INFO - 文件大小: 66833 字节
2025-06-19 14:37:05,049 - web - INFO - 文件前1KB原始内容: b'PK\x03\x04\n\x00\x00\x00\x00\x00\x87N\xe2@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\t\x00\x00\x00docProps/PK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\xc2\xd1\xd6\xdc\x96\x01\x00\x00\x9b\x02\x00\x00\x10\x00\x00\x00docProps/app.xml\x9dR\xbbn\xdb0\x14\xdd\x0b\xf4\x1f\x04\xed2)\xc9\x8a\xe2\xe0\x9a\x81\xab4S\xd1\x1a\xb0R\x8f\x01A][B%\x92 \x99 \xfe\x96\x0e]\x8af\xca\xd2.\xc9\x90\xbf\x89\xdb\xdf(U\xa7\xb2\xb3f\xbb\xe7\x1c\xf0\xf0\xdc\x07\x9c\xdetmp\x8d\xc66JN\xc3xD\xc3\x00\xa5PU#\xd7\xd3\xf0\xa2<\x8f\x8e\xc3\xc0:.+\xde*\x89\xd3p\x836<eo\xdf\xc0\xdc(\x8d\xc65h\x03o!\xed4\xac\x9d\xd3\'\x84XQc\xc7\xed\xc8\xcb\xd2++e:\xee<4k\xa2V\xabF\xe0\x99\x12W\x1dJG\x12J\x8f\x08\xde8\x94\x15V\x91\x1e\x0c\xc3\x9d\xe3\xc9\xb5{\xadi\xa5D\x9f\xcf~.7\xda\x07fPb\xa7[\xee\x90}\xec\xe3\xb4@\x06\x02\n\xd5i.7\xec\xe9\xfen\xfb\xf5\xf1\xcf\xb7_\xbf\x7f>l\xbf\xdfn\xef~\x00\xf9\xaf\xc1\x9c\xaf\xd1\xb2\t\x90]\x01Ke*\xcb\xd2q\x9e\x02\xd9\xd5P\xd4\xdcp\xe1\xfc(Y\x9ag\xb9\x7f\xbc\'\xe0C#\xfd\xfbt\x0cdWyC\xc3\xd7\x86\xeb\xfa\xd9u@P*\xc7\xdb\xb2\xe9\x90Q\x1fs\x00\xb0\x10\xbc\xc5\xc2w\xc5V\xbc\xb5\x08dO\xf4\xf6_\xec\x85.\xd5Y\xdf\xe3\xb3\xfe\x92<\xc8\xb7l\\\xbd\xd0\\\xf4\x89&\xb1\xffe\x9f\xf4@\x82\x99\xd6m#\xb8\xf3\x97\xc1\x96\xf3E\xf0\xe9\xdf\xf6.\xe3d\xe4\xcfd\x14O\xf2\x9c^\x9e\xc7\xef\xd3$\x7fWD\xc9\xd1\xa4\x88\xc6iVE\xb38K"\x9a\x15\xd9\x98\x1eS\x9a\x143 \x87N\xe0\xd7\xbf@qe\x1a\xb7\xe9;<\x84~\xbc\xc3\x11\xb0\xbfPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@<\xcf\x83u\x9d\x01\x00\x00\xb2\x02\x00\x00\x11\x00\x00\x00docProps/core.xml}\x92OK\x1bA\x1c\x86\xef\x82\xdfa\x99\xfbff7\xfe\t\xcbf\x05\x15O\n\x82\x91\x96\xde\x86\x99_\xe2\xd0\xdd\xd9ef4\xe6\xe8E\x94\x16io\xa5 \x92K\x0b=\x14BA\xc4\xf8qL6\xfd\x16\xeeN\x925\xa2x\x1c\xde\xf7\xf7\xcc3\x7f\xc2\x8d\xd3$vN@i\x91\xca&\xf2j\x049 Y\xca\x85\xec4\xd1ak\xc7m G\x1b*9\x8dS\tM\xd4\x03\x8d6\xa2\xe5\xa5\x90e\x01K\x15\xec\xab4\x03e\x04h\xa7 I\x1d\xb0\xac\x89\x8e\x8c\xc9\x02\x8c5;\x82\x84\xeaZ\xd1\x90E\xd8NUBM\xb1T\x1d\x9cQ\xf6\x99v\x00\xfb\x84\xac\xe1\x04\x0c\xe5\xd4P\\\x02\xdd\xac"\xa2\x19\x92\xb3\n\x99\x1d\xab\xd8\x028\xc3\x10C\x02\xd2h\xec\xd5<\xfc\xdc5\xa0\x12\xfd\xe6\x80M\x16\x9a\x890\xbd\xac8\xd3Lw\x91\xcd\xd94\xac\xda\xa7ZT\xc5n\xb7[\xeb\xd6\xadF\xe1\xef\xe1\x8f{\xbb\x07\xf6\xa8\xae\x90\xe5]1@Q\xc8\x99\xdd.`\n\xa8\x01\xee\x14\x80`\xba\xdd<\xf9P\xdf\xdan\xed\xa0\xc8\'~\xdd%k.Yo\x91F\xb0\xe2\x05\x84|\n\xf1\xbc5\x9b/\x81SV\xaa\xa2\xfc\xdf0\x1f\xde\xe4\x7f\xfb\xf9\xb7\xf3\xd1\xf7\x1fe\xb9\xca\xca\x87\x89\xa96{\xc5\x1b\xb6\x05\xf0\xcd^4\xbe\xbe\x9a|\x19\xfc\xffs\x16\xe2\xd7a%\x9a\xcc\x06\xde7]u\x89\xe7\xfa~i\xba\xda\x08\xea\xfe\x82\xe9\x1c`U\x8d01D\x8f\x0fg\x8fw?G\x17\xb7\xa3\xfb_\xee\xf8\xf2\xf7\xa4\xff5\x1f\x0c\x1d+<m\x94\xba\nND\xf9\xff"\xcf\xb7\x86\xd5\xda\xae^\xfe\xb2\xe8\tPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\x89\xbe\xae\n\x91\x01\x00\x00\xeb\x02\x00\x00\x13\x00\x00\x00docProps/custom.xml\xb5\x92Ao\x9b0\x18\x86\xef\x93\xf6\x1f\x90\xef\x04c @\x04\xa9\x02$\x15\xd9'
2025-06-19 14:37:05,114 - web - INFO - 文本提取成功，内容长度: 3916 字符
2025-06-19 14:37:05,115 - web - INFO - 提取的内容前100个字符: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用
2025-06-19 14:37:05,115 - web - INFO - 提取的文本内容 text_content: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 14:37:05,116 - web - INFO - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 14:37:05,116 - web - INFO - 提取的文本内容 llm_result: {'success': False, 'message': "中石化LLM处理失败: 调用中石化API失败: 'LLMProcessor' object has no attribute '_is_sinopec_api_configured'", 'data': {}}
2025-06-19 14:37:05,116 - web - INFO - 开始生成Excel报告
2025-06-19 14:37:05,232 - web - INFO - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_143705.xlsx
2025-06-19 14:37:05,233 - web - INFO - 文件处理完成，生成了1个下载文件
2025-06-19 14:42:36,653 - web - INFO - upload_file: 开始处理
2025-06-19 14:42:36,656 - web - INFO - 文件名=GHNLH-B1801-43-808-2025-2-XXZX022海南炼化公司域长负责制运行管理细则.docx, 扩展名=docx, 允许的扩展名={'doc', 'rtf', 'odt', 'txt', 'pdf', 'xls', 'ppt', 'xlsx', 'pptx', 'docx'}
2025-06-19 14:42:36,906 - web - INFO - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 14:42:36,908 - web - INFO - 文件大小: 66833 字节
2025-06-19 14:42:36,908 - web - INFO - 文件前1KB原始内容: b'PK\x03\x04\n\x00\x00\x00\x00\x00\x87N\xe2@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\t\x00\x00\x00docProps/PK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\xc2\xd1\xd6\xdc\x96\x01\x00\x00\x9b\x02\x00\x00\x10\x00\x00\x00docProps/app.xml\x9dR\xbbn\xdb0\x14\xdd\x0b\xf4\x1f\x04\xed2)\xc9\x8a\xe2\xe0\x9a\x81\xab4S\xd1\x1a\xb0R\x8f\x01A][B%\x92 \x99 \xfe\x96\x0e]\x8af\xca\xd2.\xc9\x90\xbf\x89\xdb\xdf(U\xa7\xb2\xb3f\xbb\xe7\x1c\xf0\xf0\xdc\x07\x9c\xdetmp\x8d\xc66JN\xc3xD\xc3\x00\xa5PU#\xd7\xd3\xf0\xa2<\x8f\x8e\xc3\xc0:.+\xde*\x89\xd3p\x836<eo\xdf\xc0\xdc(\x8d\xc65h\x03o!\xed4\xac\x9d\xd3\'\x84XQc\xc7\xed\xc8\xcb\xd2++e:\xee<4k\xa2V\xabF\xe0\x99\x12W\x1dJG\x12J\x8f\x08\xde8\x94\x15V\x91\x1e\x0c\xc3\x9d\xe3\xc9\xb5{\xadi\xa5D\x9f\xcf~.7\xda\x07fPb\xa7[\xee\x90}\xec\xe3\xb4@\x06\x02\n\xd5i.7\xec\xe9\xfen\xfb\xf5\xf1\xcf\xb7_\xbf\x7f>l\xbf\xdfn\xef~\x00\xf9\xaf\xc1\x9c\xaf\xd1\xb2\t\x90]\x01Ke*\xcb\xd2q\x9e\x02\xd9\xd5P\xd4\xdcp\xe1\xfc(Y\x9ag\xb9\x7f\xbc\'\xe0C#\xfd\xfbt\x0cdWyC\xc3\xd7\x86\xeb\xfa\xd9u@P*\xc7\xdb\xb2\xe9\x90Q\x1fs\x00\xb0\x10\xbc\xc5\xc2w\xc5V\xbc\xb5\x08dO\xf4\xf6_\xec\x85.\xd5Y\xdf\xe3\xb3\xfe\x92<\xc8\xb7l\\\xbd\xd0\\\xf4\x89&\xb1\xffe\x9f\xf4@\x82\x99\xd6m#\xb8\xf3\x97\xc1\x96\xf3E\xf0\xe9\xdf\xf6.\xe3d\xe4\xcfd\x14O\xf2\x9c^\x9e\xc7\xef\xd3$\x7fWD\xc9\xd1\xa4\x88\xc6iVE\xb38K"\x9a\x15\xd9\x98\x1eS\x9a\x143 \x87N\xe0\xd7\xbf@qe\x1a\xb7\xe9;<\x84~\xbc\xc3\x11\xb0\xbfPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@<\xcf\x83u\x9d\x01\x00\x00\xb2\x02\x00\x00\x11\x00\x00\x00docProps/core.xml}\x92OK\x1bA\x1c\x86\xef\x82\xdfa\x99\xfbff7\xfe\t\xcbf\x05\x15O\n\x82\x91\x96\xde\x86\x99_\xe2\xd0\xdd\xd9ef4\xe6\xe8E\x94\x16io\xa5 \x92K\x0b=\x14BA\xc4\xf8qL6\xfd\x16\xeeN\x925\xa2x\x1c\xde\xf7\xf7\xcc3\x7f\xc2\x8d\xd3$vN@i\x91\xca&\xf2j\x049 Y\xca\x85\xec4\xd1ak\xc7m G\x1b*9\x8dS\tM\xd4\x03\x8d6\xa2\xe5\xa5\x90e\x01K\x15\xec\xab4\x03e\x04h\xa7 I\x1d\xb0\xac\x89\x8e\x8c\xc9\x02\x8c5;\x82\x84\xeaZ\xd1\x90E\xd8NUBM\xb1T\x1d\x9cQ\xf6\x99v\x00\xfb\x84\xac\xe1\x04\x0c\xe5\xd4P\\\x02\xdd\xac"\xa2\x19\x92\xb3\n\x99\x1d\xab\xd8\x028\xc3\x10C\x02\xd2h\xec\xd5<\xfc\xdc5\xa0\x12\xfd\xe6\x80M\x16\x9a\x890\xbd\xac8\xd3Lw\x91\xcd\xd94\xac\xda\xa7ZT\xc5n\xb7[\xeb\xd6\xadF\xe1\xef\xe1\x8f{\xbb\x07\xf6\xa8\xae\x90\xe5]1@Q\xc8\x99\xdd.`\n\xa8\x01\xee\x14\x80`\xba\xdd<\xf9P\xdf\xdan\xed\xa0\xc8\'~\xdd%k.Yo\x91F\xb0\xe2\x05\x84|\n\xf1\xbc5\x9b/\x81SV\xaa\xa2\xfc\xdf0\x1f\xde\xe4\x7f\xfb\xf9\xb7\xf3\xd1\xf7\x1fe\xb9\xca\xca\x87\x89\xa96{\xc5\x1b\xb6\x05\xf0\xcd^4\xbe\xbe\x9a|\x19\xfc\xffs\x16\xe2\xd7a%\x9a\xcc\x06\xde7]u\x89\xe7\xfa~i\xba\xda\x08\xea\xfe\x82\xe9\x1c`U\x8d01D\x8f\x0fg\x8fw?G\x17\xb7\xa3\xfb_\xee\xf8\xf2\xf7\xa4\xff5\x1f\x0c\x1d+<m\x94\xba\nND\xf9\xff"\xcf\xb7\x86\xd5\xda\xae^\xfe\xb2\xe8\tPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\x89\xbe\xae\n\x91\x01\x00\x00\xeb\x02\x00\x00\x13\x00\x00\x00docProps/custom.xml\xb5\x92Ao\x9b0\x18\x86\xef\x93\xf6\x1f\x90\xef\x04c @\x04\xa9\x02$\x15\xd9'
2025-06-19 14:42:36,971 - web - INFO - 文本提取成功，内容长度: 3916 字符
2025-06-19 14:42:36,972 - web - INFO - 提取的内容前100个字符: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用
2025-06-19 14:42:36,972 - web - INFO - 提取的文本内容 text_content: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 14:42:36,973 - web - INFO - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 14:42:36,973 - web - INFO - 提取的文本内容 llm_result: {'success': False, 'message': "中石化LLM处理失败: 调用中石化API失败: 'LLMProcessor' object has no attribute '_is_sinopec_api_configured'", 'data': {}}
2025-06-19 14:42:36,973 - web - INFO - 开始生成Excel报告
2025-06-19 14:42:37,197 - web - INFO - Excel报告生成成功: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_144236.xlsx
2025-06-19 14:42:37,199 - web - INFO - 文件处理完成，生成了1个下载文件
2025-06-19 14:48:07,990 - web - INFO - upload_file: 开始处理
2025-06-19 14:48:07,992 - web - INFO - 文件名=GHNLH-B1801-43-808-2025-2-XXZX022海南炼化公司域长负责制运行管理细则.docx, 扩展名=docx, 允许的扩展名={'txt', 'xls', 'odt', 'pptx', 'doc', 'rtf', 'pdf', 'docx', 'xlsx', 'ppt'}
2025-06-19 14:48:07,996 - web - INFO - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 14:48:07,996 - web - INFO - 文件大小: 66833 字节
2025-06-19 14:48:07,997 - web - INFO - 文件前1KB原始内容: b'PK\x03\x04\n\x00\x00\x00\x00\x00\x87N\xe2@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\t\x00\x00\x00docProps/PK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\xc2\xd1\xd6\xdc\x96\x01\x00\x00\x9b\x02\x00\x00\x10\x00\x00\x00docProps/app.xml\x9dR\xbbn\xdb0\x14\xdd\x0b\xf4\x1f\x04\xed2)\xc9\x8a\xe2\xe0\x9a\x81\xab4S\xd1\x1a\xb0R\x8f\x01A][B%\x92 \x99 \xfe\x96\x0e]\x8af\xca\xd2.\xc9\x90\xbf\x89\xdb\xdf(U\xa7\xb2\xb3f\xbb\xe7\x1c\xf0\xf0\xdc\x07\x9c\xdetmp\x8d\xc66JN\xc3xD\xc3\x00\xa5PU#\xd7\xd3\xf0\xa2<\x8f\x8e\xc3\xc0:.+\xde*\x89\xd3p\x836<eo\xdf\xc0\xdc(\x8d\xc65h\x03o!\xed4\xac\x9d\xd3\'\x84XQc\xc7\xed\xc8\xcb\xd2++e:\xee<4k\xa2V\xabF\xe0\x99\x12W\x1dJG\x12J\x8f\x08\xde8\x94\x15V\x91\x1e\x0c\xc3\x9d\xe3\xc9\xb5{\xadi\xa5D\x9f\xcf~.7\xda\x07fPb\xa7[\xee\x90}\xec\xe3\xb4@\x06\x02\n\xd5i.7\xec\xe9\xfen\xfb\xf5\xf1\xcf\xb7_\xbf\x7f>l\xbf\xdfn\xef~\x00\xf9\xaf\xc1\x9c\xaf\xd1\xb2\t\x90]\x01Ke*\xcb\xd2q\x9e\x02\xd9\xd5P\xd4\xdcp\xe1\xfc(Y\x9ag\xb9\x7f\xbc\'\xe0C#\xfd\xfbt\x0cdWyC\xc3\xd7\x86\xeb\xfa\xd9u@P*\xc7\xdb\xb2\xe9\x90Q\x1fs\x00\xb0\x10\xbc\xc5\xc2w\xc5V\xbc\xb5\x08dO\xf4\xf6_\xec\x85.\xd5Y\xdf\xe3\xb3\xfe\x92<\xc8\xb7l\\\xbd\xd0\\\xf4\x89&\xb1\xffe\x9f\xf4@\x82\x99\xd6m#\xb8\xf3\x97\xc1\x96\xf3E\xf0\xe9\xdf\xf6.\xe3d\xe4\xcfd\x14O\xf2\x9c^\x9e\xc7\xef\xd3$\x7fWD\xc9\xd1\xa4\x88\xc6iVE\xb38K"\x9a\x15\xd9\x98\x1eS\x9a\x143 \x87N\xe0\xd7\xbf@qe\x1a\xb7\xe9;<\x84~\xbc\xc3\x11\xb0\xbfPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@<\xcf\x83u\x9d\x01\x00\x00\xb2\x02\x00\x00\x11\x00\x00\x00docProps/core.xml}\x92OK\x1bA\x1c\x86\xef\x82\xdfa\x99\xfbff7\xfe\t\xcbf\x05\x15O\n\x82\x91\x96\xde\x86\x99_\xe2\xd0\xdd\xd9ef4\xe6\xe8E\x94\x16io\xa5 \x92K\x0b=\x14BA\xc4\xf8qL6\xfd\x16\xeeN\x925\xa2x\x1c\xde\xf7\xf7\xcc3\x7f\xc2\x8d\xd3$vN@i\x91\xca&\xf2j\x049 Y\xca\x85\xec4\xd1ak\xc7m G\x1b*9\x8dS\tM\xd4\x03\x8d6\xa2\xe5\xa5\x90e\x01K\x15\xec\xab4\x03e\x04h\xa7 I\x1d\xb0\xac\x89\x8e\x8c\xc9\x02\x8c5;\x82\x84\xeaZ\xd1\x90E\xd8NUBM\xb1T\x1d\x9cQ\xf6\x99v\x00\xfb\x84\xac\xe1\x04\x0c\xe5\xd4P\\\x02\xdd\xac"\xa2\x19\x92\xb3\n\x99\x1d\xab\xd8\x028\xc3\x10C\x02\xd2h\xec\xd5<\xfc\xdc5\xa0\x12\xfd\xe6\x80M\x16\x9a\x890\xbd\xac8\xd3Lw\x91\xcd\xd94\xac\xda\xa7ZT\xc5n\xb7[\xeb\xd6\xadF\xe1\xef\xe1\x8f{\xbb\x07\xf6\xa8\xae\x90\xe5]1@Q\xc8\x99\xdd.`\n\xa8\x01\xee\x14\x80`\xba\xdd<\xf9P\xdf\xdan\xed\xa0\xc8\'~\xdd%k.Yo\x91F\xb0\xe2\x05\x84|\n\xf1\xbc5\x9b/\x81SV\xaa\xa2\xfc\xdf0\x1f\xde\xe4\x7f\xfb\xf9\xb7\xf3\xd1\xf7\x1fe\xb9\xca\xca\x87\x89\xa96{\xc5\x1b\xb6\x05\xf0\xcd^4\xbe\xbe\x9a|\x19\xfc\xffs\x16\xe2\xd7a%\x9a\xcc\x06\xde7]u\x89\xe7\xfa~i\xba\xda\x08\xea\xfe\x82\xe9\x1c`U\x8d01D\x8f\x0fg\x8fw?G\x17\xb7\xa3\xfb_\xee\xf8\xf2\xf7\xa4\xff5\x1f\x0c\x1d+<m\x94\xba\nND\xf9\xff"\xcf\xb7\x86\xd5\xda\xae^\xfe\xb2\xe8\tPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\x89\xbe\xae\n\x91\x01\x00\x00\xeb\x02\x00\x00\x13\x00\x00\x00docProps/custom.xml\xb5\x92Ao\x9b0\x18\x86\xef\x93\xf6\x1f\x90\xef\x04c @\x04\xa9\x02$\x15\xd9'
2025-06-19 14:48:08,079 - web - INFO - 文本提取成功，内容长度: 3916 字符
2025-06-19 14:48:08,079 - web - INFO - 提取的内容前100个字符: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用
2025-06-19 14:48:08,080 - web - INFO - 提取的文本内容 text_content: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 14:48:08,082 - web - INFO - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 14:48:08,083 - web - INFO - 提取的文本内容 llm_result: {'success': False, 'message': "中石化LLM处理失败: 调用中石化API失败: 'LLMProcessor' object has no attribute '_is_sinopec_api_configured'", 'data': {}}
2025-06-19 14:48:08,306 - web - INFO - Excel报告已生成: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_144808.xlsx
2025-06-19 14:48:08,307 - web - INFO - 文件处理完成，生成了1个下载文件
2025-06-19 14:50:31,371 - web - INFO - upload_file: 开始处理
2025-06-19 14:50:31,372 - web - INFO - 文件名=GHNLH-B1801-43-808-2025-2-XXZX022海南炼化公司域长负责制运行管理细则.docx, 扩展名=docx, 允许的扩展名={'txt', 'xls', 'odt', 'pptx', 'doc', 'rtf', 'pdf', 'docx', 'xlsx', 'ppt'}
2025-06-19 14:50:31,377 - web - INFO - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 14:50:31,377 - web - INFO - 文件大小: 66833 字节
2025-06-19 14:50:31,379 - web - INFO - 文件前1KB原始内容: b'PK\x03\x04\n\x00\x00\x00\x00\x00\x87N\xe2@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\t\x00\x00\x00docProps/PK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\xc2\xd1\xd6\xdc\x96\x01\x00\x00\x9b\x02\x00\x00\x10\x00\x00\x00docProps/app.xml\x9dR\xbbn\xdb0\x14\xdd\x0b\xf4\x1f\x04\xed2)\xc9\x8a\xe2\xe0\x9a\x81\xab4S\xd1\x1a\xb0R\x8f\x01A][B%\x92 \x99 \xfe\x96\x0e]\x8af\xca\xd2.\xc9\x90\xbf\x89\xdb\xdf(U\xa7\xb2\xb3f\xbb\xe7\x1c\xf0\xf0\xdc\x07\x9c\xdetmp\x8d\xc66JN\xc3xD\xc3\x00\xa5PU#\xd7\xd3\xf0\xa2<\x8f\x8e\xc3\xc0:.+\xde*\x89\xd3p\x836<eo\xdf\xc0\xdc(\x8d\xc65h\x03o!\xed4\xac\x9d\xd3\'\x84XQc\xc7\xed\xc8\xcb\xd2++e:\xee<4k\xa2V\xabF\xe0\x99\x12W\x1dJG\x12J\x8f\x08\xde8\x94\x15V\x91\x1e\x0c\xc3\x9d\xe3\xc9\xb5{\xadi\xa5D\x9f\xcf~.7\xda\x07fPb\xa7[\xee\x90}\xec\xe3\xb4@\x06\x02\n\xd5i.7\xec\xe9\xfen\xfb\xf5\xf1\xcf\xb7_\xbf\x7f>l\xbf\xdfn\xef~\x00\xf9\xaf\xc1\x9c\xaf\xd1\xb2\t\x90]\x01Ke*\xcb\xd2q\x9e\x02\xd9\xd5P\xd4\xdcp\xe1\xfc(Y\x9ag\xb9\x7f\xbc\'\xe0C#\xfd\xfbt\x0cdWyC\xc3\xd7\x86\xeb\xfa\xd9u@P*\xc7\xdb\xb2\xe9\x90Q\x1fs\x00\xb0\x10\xbc\xc5\xc2w\xc5V\xbc\xb5\x08dO\xf4\xf6_\xec\x85.\xd5Y\xdf\xe3\xb3\xfe\x92<\xc8\xb7l\\\xbd\xd0\\\xf4\x89&\xb1\xffe\x9f\xf4@\x82\x99\xd6m#\xb8\xf3\x97\xc1\x96\xf3E\xf0\xe9\xdf\xf6.\xe3d\xe4\xcfd\x14O\xf2\x9c^\x9e\xc7\xef\xd3$\x7fWD\xc9\xd1\xa4\x88\xc6iVE\xb38K"\x9a\x15\xd9\x98\x1eS\x9a\x143 \x87N\xe0\xd7\xbf@qe\x1a\xb7\xe9;<\x84~\xbc\xc3\x11\xb0\xbfPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@<\xcf\x83u\x9d\x01\x00\x00\xb2\x02\x00\x00\x11\x00\x00\x00docProps/core.xml}\x92OK\x1bA\x1c\x86\xef\x82\xdfa\x99\xfbff7\xfe\t\xcbf\x05\x15O\n\x82\x91\x96\xde\x86\x99_\xe2\xd0\xdd\xd9ef4\xe6\xe8E\x94\x16io\xa5 \x92K\x0b=\x14BA\xc4\xf8qL6\xfd\x16\xeeN\x925\xa2x\x1c\xde\xf7\xf7\xcc3\x7f\xc2\x8d\xd3$vN@i\x91\xca&\xf2j\x049 Y\xca\x85\xec4\xd1ak\xc7m G\x1b*9\x8dS\tM\xd4\x03\x8d6\xa2\xe5\xa5\x90e\x01K\x15\xec\xab4\x03e\x04h\xa7 I\x1d\xb0\xac\x89\x8e\x8c\xc9\x02\x8c5;\x82\x84\xeaZ\xd1\x90E\xd8NUBM\xb1T\x1d\x9cQ\xf6\x99v\x00\xfb\x84\xac\xe1\x04\x0c\xe5\xd4P\\\x02\xdd\xac"\xa2\x19\x92\xb3\n\x99\x1d\xab\xd8\x028\xc3\x10C\x02\xd2h\xec\xd5<\xfc\xdc5\xa0\x12\xfd\xe6\x80M\x16\x9a\x890\xbd\xac8\xd3Lw\x91\xcd\xd94\xac\xda\xa7ZT\xc5n\xb7[\xeb\xd6\xadF\xe1\xef\xe1\x8f{\xbb\x07\xf6\xa8\xae\x90\xe5]1@Q\xc8\x99\xdd.`\n\xa8\x01\xee\x14\x80`\xba\xdd<\xf9P\xdf\xdan\xed\xa0\xc8\'~\xdd%k.Yo\x91F\xb0\xe2\x05\x84|\n\xf1\xbc5\x9b/\x81SV\xaa\xa2\xfc\xdf0\x1f\xde\xe4\x7f\xfb\xf9\xb7\xf3\xd1\xf7\x1fe\xb9\xca\xca\x87\x89\xa96{\xc5\x1b\xb6\x05\xf0\xcd^4\xbe\xbe\x9a|\x19\xfc\xffs\x16\xe2\xd7a%\x9a\xcc\x06\xde7]u\x89\xe7\xfa~i\xba\xda\x08\xea\xfe\x82\xe9\x1c`U\x8d01D\x8f\x0fg\x8fw?G\x17\xb7\xa3\xfb_\xee\xf8\xf2\xf7\xa4\xff5\x1f\x0c\x1d+<m\x94\xba\nND\xf9\xff"\xcf\xb7\x86\xd5\xda\xae^\xfe\xb2\xe8\tPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\x89\xbe\xae\n\x91\x01\x00\x00\xeb\x02\x00\x00\x13\x00\x00\x00docProps/custom.xml\xb5\x92Ao\x9b0\x18\x86\xef\x93\xf6\x1f\x90\xef\x04c @\x04\xa9\x02$\x15\xd9'
2025-06-19 14:50:31,426 - web - INFO - 文本提取成功，内容长度: 3916 字符
2025-06-19 14:50:31,426 - web - INFO - 提取的内容前100个字符: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用
2025-06-19 14:50:31,426 - web - INFO - 提取的文本内容 text_content: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 14:50:31,429 - web - INFO - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 14:50:31,429 - web - INFO - 提取的文本内容 llm_result: {'success': False, 'message': "中石化LLM处理失败: 调用中石化API失败: 'LLMProcessor' object has no attribute '_is_sinopec_api_configured'", 'data': {}}
2025-06-19 14:50:31,458 - web - INFO - Excel报告已生成: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_145031.xlsx
2025-06-19 14:50:31,458 - web - INFO - 文件处理完成，生成了1个下载文件
2025-06-19 14:50:38,062 - web - INFO - upload_file: 开始处理
2025-06-19 14:50:38,064 - web - INFO - 文件名=GHNLH-B1801-43-808-2025-2-XXZX022海南炼化公司域长负责制运行管理细则.docx, 扩展名=docx, 允许的扩展名={'txt', 'xls', 'odt', 'pptx', 'doc', 'rtf', 'pdf', 'docx', 'xlsx', 'ppt'}
2025-06-19 14:50:38,068 - web - INFO - 开始从文件提取文本: F:\Python_project\制度智能审查\uploads\GHNLH-B1801-43-808-2025-2-XXZX022.docx
2025-06-19 14:50:38,069 - web - INFO - 文件大小: 66833 字节
2025-06-19 14:50:38,069 - web - INFO - 文件前1KB原始内容: b'PK\x03\x04\n\x00\x00\x00\x00\x00\x87N\xe2@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\t\x00\x00\x00docProps/PK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\xc2\xd1\xd6\xdc\x96\x01\x00\x00\x9b\x02\x00\x00\x10\x00\x00\x00docProps/app.xml\x9dR\xbbn\xdb0\x14\xdd\x0b\xf4\x1f\x04\xed2)\xc9\x8a\xe2\xe0\x9a\x81\xab4S\xd1\x1a\xb0R\x8f\x01A][B%\x92 \x99 \xfe\x96\x0e]\x8af\xca\xd2.\xc9\x90\xbf\x89\xdb\xdf(U\xa7\xb2\xb3f\xbb\xe7\x1c\xf0\xf0\xdc\x07\x9c\xdetmp\x8d\xc66JN\xc3xD\xc3\x00\xa5PU#\xd7\xd3\xf0\xa2<\x8f\x8e\xc3\xc0:.+\xde*\x89\xd3p\x836<eo\xdf\xc0\xdc(\x8d\xc65h\x03o!\xed4\xac\x9d\xd3\'\x84XQc\xc7\xed\xc8\xcb\xd2++e:\xee<4k\xa2V\xabF\xe0\x99\x12W\x1dJG\x12J\x8f\x08\xde8\x94\x15V\x91\x1e\x0c\xc3\x9d\xe3\xc9\xb5{\xadi\xa5D\x9f\xcf~.7\xda\x07fPb\xa7[\xee\x90}\xec\xe3\xb4@\x06\x02\n\xd5i.7\xec\xe9\xfen\xfb\xf5\xf1\xcf\xb7_\xbf\x7f>l\xbf\xdfn\xef~\x00\xf9\xaf\xc1\x9c\xaf\xd1\xb2\t\x90]\x01Ke*\xcb\xd2q\x9e\x02\xd9\xd5P\xd4\xdcp\xe1\xfc(Y\x9ag\xb9\x7f\xbc\'\xe0C#\xfd\xfbt\x0cdWyC\xc3\xd7\x86\xeb\xfa\xd9u@P*\xc7\xdb\xb2\xe9\x90Q\x1fs\x00\xb0\x10\xbc\xc5\xc2w\xc5V\xbc\xb5\x08dO\xf4\xf6_\xec\x85.\xd5Y\xdf\xe3\xb3\xfe\x92<\xc8\xb7l\\\xbd\xd0\\\xf4\x89&\xb1\xffe\x9f\xf4@\x82\x99\xd6m#\xb8\xf3\x97\xc1\x96\xf3E\xf0\xe9\xdf\xf6.\xe3d\xe4\xcfd\x14O\xf2\x9c^\x9e\xc7\xef\xd3$\x7fWD\xc9\xd1\xa4\x88\xc6iVE\xb38K"\x9a\x15\xd9\x98\x1eS\x9a\x143 \x87N\xe0\xd7\xbf@qe\x1a\xb7\xe9;<\x84~\xbc\xc3\x11\xb0\xbfPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@<\xcf\x83u\x9d\x01\x00\x00\xb2\x02\x00\x00\x11\x00\x00\x00docProps/core.xml}\x92OK\x1bA\x1c\x86\xef\x82\xdfa\x99\xfbff7\xfe\t\xcbf\x05\x15O\n\x82\x91\x96\xde\x86\x99_\xe2\xd0\xdd\xd9ef4\xe6\xe8E\x94\x16io\xa5 \x92K\x0b=\x14BA\xc4\xf8qL6\xfd\x16\xeeN\x925\xa2x\x1c\xde\xf7\xf7\xcc3\x7f\xc2\x8d\xd3$vN@i\x91\xca&\xf2j\x049 Y\xca\x85\xec4\xd1ak\xc7m G\x1b*9\x8dS\tM\xd4\x03\x8d6\xa2\xe5\xa5\x90e\x01K\x15\xec\xab4\x03e\x04h\xa7 I\x1d\xb0\xac\x89\x8e\x8c\xc9\x02\x8c5;\x82\x84\xeaZ\xd1\x90E\xd8NUBM\xb1T\x1d\x9cQ\xf6\x99v\x00\xfb\x84\xac\xe1\x04\x0c\xe5\xd4P\\\x02\xdd\xac"\xa2\x19\x92\xb3\n\x99\x1d\xab\xd8\x028\xc3\x10C\x02\xd2h\xec\xd5<\xfc\xdc5\xa0\x12\xfd\xe6\x80M\x16\x9a\x890\xbd\xac8\xd3Lw\x91\xcd\xd94\xac\xda\xa7ZT\xc5n\xb7[\xeb\xd6\xadF\xe1\xef\xe1\x8f{\xbb\x07\xf6\xa8\xae\x90\xe5]1@Q\xc8\x99\xdd.`\n\xa8\x01\xee\x14\x80`\xba\xdd<\xf9P\xdf\xdan\xed\xa0\xc8\'~\xdd%k.Yo\x91F\xb0\xe2\x05\x84|\n\xf1\xbc5\x9b/\x81SV\xaa\xa2\xfc\xdf0\x1f\xde\xe4\x7f\xfb\xf9\xb7\xf3\xd1\xf7\x1fe\xb9\xca\xca\x87\x89\xa96{\xc5\x1b\xb6\x05\xf0\xcd^4\xbe\xbe\x9a|\x19\xfc\xffs\x16\xe2\xd7a%\x9a\xcc\x06\xde7]u\x89\xe7\xfa~i\xba\xda\x08\xea\xfe\x82\xe9\x1c`U\x8d01D\x8f\x0fg\x8fw?G\x17\xb7\xa3\xfb_\xee\xf8\xf2\xf7\xa4\xff5\x1f\x0c\x1d+<m\x94\xba\nND\xf9\xff"\xcf\xb7\x86\xd5\xda\xae^\xfe\xb2\xe8\tPK\x03\x04\x14\x00\x00\x00\x08\x00\x87N\xe2@\x89\xbe\xae\n\x91\x01\x00\x00\xeb\x02\x00\x00\x13\x00\x00\x00docProps/custom.xml\xb5\x92Ao\x9b0\x18\x86\xef\x93\xf6\x1f\x90\xef\x04c @\x04\xa9\x02$\x15\xd9'
2025-06-19 14:50:38,179 - web - INFO - 文本提取成功，内容长度: 3916 字符
2025-06-19 14:50:38,180 - web - INFO - 提取的内容前100个字符: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用
2025-06-19 14:50:38,180 - web - INFO - 提取的文本内容 text_content: 企业制度-执行类 
1  基本要求
1.1  目的依据
为了全面推进域长负责制的落地执行，提升各业务域工作成效，促进公司数字化转型，依据《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》和《海南炼化公司信息和数字化管理细则》，特制定本细则。
1.2  规范内容界定
1.2.1  本细则明确了域长负责制运行管理的各项工作，主要包括组织管理与职责、工作内容、工作机制等内容。
1.2.2   本细则所称业务域，是指为了推进海南炼化公司数字化转型，发挥业务部门信息化建设的主体责任，按照资源共享、协同推进的原则，划分的信息化建设与应用的业务领域。
1.2.3  本细则所称域长单位，是指本业务域内，负责全面落实域长负责制的工作机制和工作任务的主责单位。
1.2.4  本细则所称应用架构，是指以海南炼化公司业务架构为基础，统筹考虑信息和数字化发展建设需要，规划和指导各业务域应用系统定位和功能设计的框架结构。
1.3  业务管理原则
坚持“业务驱动、技术统筹、协力推进、监督评价”原则，充分发挥域长单位在信息化应用顶层设计、业务流程标准化、数据治理和深化应用等方面的主导作用，综合管理部信息中心进行技术统筹，提供信息技术支撑，共同构建“业务驱动、部门协同、全域覆盖”的长效机制。
1.4  相关术语
各单位是指公司属各部门、各基层单位、各公司。
2  职责与分工
2.1  综合管理部信息中心
2.1.1  负责落实集团公司及公司域长负责制配套机制和运行体系建设，建立业务主导、按域推进的机制。开展日常运行管理、工作推进和评价考核工作。
2.1.2  负责为业务域推进顶层设计、数据治理等相关工作提供标准、方法和工具支撑。
2.1.3  负责组织公司信息化应用架构设计、维护和发布。
2.1.4  负责统筹跨域的业务协同和数据共享，负责跨域信息化项目重复功能审查、资源调配。

2.2  域长单位
2.2.1  负责组织落实本业务域的工作机制，推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作，组建和管理本业务域的专家团队。
2.2.2  负责组织本业务域信息化应用顶层设计，进行动态更新维护。
2.2.3  负责组织本业务域业务流程标准化工作，持续优化业务流程。
2.2.4  负责组织本业务域数据治理工作。
2.2.5  负责组织本业务域信息系统的深化应用。
2.2.6  负责本业务域各信息化项目重复功能审查、资源调配。

2.3  各

2.3.  负责本单位的业务流程标准化、数据治理和深化应用等相关工作。
2.3.  参与本业务域信息化应用顶层设计、专家团队建立等工作。
2.4  党委组织部
负责组织开展各业务域业务流程标准化工作，统一各业务域编制业务流程使用的模板和工具。





3  管理内容与要求
3.1  顶层设计
3.1.1  数字化转型方案制定
3.1.1.1  综合管理部信息中心根据集团公司数字化转型战略部署，组织编制公司数字化转型工作方案。
3.1.1.2  域长单位根据集团公司数字化转型战略部署、公司整体实施方案，结合公司业务发展需要，组织落实制定本业务域数字化转型工作方案。
3.1.2  应用架构设计
3.1.2.1  应用架构原则上应按照业务域、业务子域、关键业务、子业务分层分级进行设计，包括架构标准/原则、边界和定义等内容。
*******  域长单位按照公司统一的应用架构梳理设计方法、分类原则和标准，组织开展本业务域应用架构设计和专家评审，并进行动态管理与更新。
*******  综合管理部信息中心在各业务域应用架构基础上，组织开展公司应用架构设计和专家评审，并统一发布和管理。
3.1.3  全域APP设计
3.1.3.1  全域APP设计原则上应涵盖应用架构的所有关键业务，且避免重复交叉。
3.1.3.2  域长单位应统筹考虑本业务域信息化发展和数字化转型需要，面向业务场景和解决具体问题开展全域APP设计，并在全域APP设计的基础上，制定在用信息系统整合方案，并推进本域在用信息系统整合。
3.1.3.3  综合管理部信息中心对各业务域设计的全域APP进行统筹管理。
3.2  业务流程标准化
3.2.1  业务流程标准化应对关键业务的业务逻辑和子业务的全过程进行标准化的描述，为信息系统建设和数据治理提供支撑，包括业务逻辑图、流程组、单元流程、流程手册等内容。
3.2.2  域长单位根据党委组织部统一制定的业务流程标准化的方法及标准，开展业务流程的标准化工作。
3.2.3  域长单位在本业务域内建立跨组织、跨系统的端到端业务流程，并组织本业务域专家进行评审。
3.2.4  党委组织部组织对各业务域的跨组织、跨系统的端到端业务流程进行评审。
3.3  数据治理
3.3.1  综合管理部信息中心根据集团公司数据治理的整体工作部署，结合公司实际，组织建立数据治理组织，建立公司数据治理体系，制定数据标准、方法，组织开发数据治理智能工具
3.3.2  域长单位应根据公司数据治理的整体工作部署，建立本业务域内数据治理组织，开展现状分析，编制数据治理计划，开展数据资源盘点，推动数据入湖，管控数据质量，开展数据分析应用和共享。
3.4  深化应用
域长单位应结合本业务域业务管理的要求，规范使用信息系统，开展应用培训、经验交流、应用效果的评估等活动，发挥信息系统的应用成效。
4  工作机制
4.1  工作例会
4.1.1  综合管理部信息中心负责组织域长负责制季度例会，主要包括安排部署重点工作，协调解决共性问题，检查通报工作进展、经验分享交流，论证信息化需求是否具备立项条件等，当已提报完成的信息化需求大于等于3个时，召开域长负责制临时会议开展需求论证。

4.1.2  域长单位按需组织本业务域信息化建设与应用工作例会（可与分管专业月度例会统筹管理）。推进本业务信息化应用顶层设计、业务流程标准化、数据治理和深化应用等工作。
4.2  工作评价
4.2.1  域长负责制工作评价的内容主要包括域长负责制组织体系建设及运行、信息化应用顶层设计、业务流程标准化、数据治理、信息系统建设应用等，从效益、效果、效率三个维度形成评价结果。
4.2.2  综合管理部信息中心每年组织开展域长负责制工作评价，形成评价结果并发布。

5  检查与监督
综合管理部信息中心对本细则的执行情况进行监督检查，对问题较多的单位，给予通报并限期整改。对检查发现的问题考核500元，做得好的奖励500元
5.1  未落实域长单位责任领导和本业务域的专家团队的；
5.2  未按要求推进本业务域的整体工作，参与信息化项目储备审核、可研报告审核、跨业务域协调等工作的；
5.3  未按要求组织本业务域业务流程标准化工作，持续优化业务流程的；
5.4  未按要求组织本业务域数据治理工作的；
5.5  未按要求组织本业务域信息系统的深化应用的；
5.6  未按要求组织本业务域信息化建设与应用工作例会的；

5.  本业务信息化建设工作成效好，深化应用案例获得总部通报表扬的。
6  附件
6.1  业务职责分工表
6.2  检查考核评价表
6.3  域长负责制管理流程
6.4  各业务域数据治理域长责任部门及人员表





表格 1:
 | 制度名称 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则 | 海南炼化公司域长负责制运行管理细则
 | 制度编号 |  |  | 
 | 制度版本 |  | 主办部门 | 综合管理部
所属业务类别 | 信息和数字化管理/综合管理 | 信息和数字化管理/综合管理 | 会签部门 | 科技发展部、党委组织部、财务资产部、物资中心、计划经营部、生产技术部、安全环保部、设备工程部、纪检审计部、党群工作部、质量管理部
下位制度制定 | 合资公司、代管公司结合实际制定执行类制度 | 合资公司、代管公司结合实际制定执行类制度 | 审核部门 | 
监督检查者 | 综合管理部 | 综合管理部 | 签发日期 | 
解释权归属 | 综合管理部 | 综合管理部 | 生效日期 | 
制定目的 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型 | 全面推进域长负责制的落地执行，保障各业务域工作推进的成效，加快推进公司数字化转型
制定依据 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》 | 《中国石化域长负责制运行管理规定》《集团公司业务域信息化建设应用域长负责制工作方案》《海南炼化信息和数字化管理细则》
适用范围 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司 | 公司各部门、各基层单位、各公司
涉及的相关制度 | / | / | / | /
废止说明 |  |  |  | 


2025-06-19 14:50:38,183 - web - INFO - 开始处理文本，使用LLM: True, 提供商: sinopec
2025-06-19 14:50:38,184 - web - INFO - 提取的文本内容 llm_result: {'success': False, 'message': "中石化LLM处理失败: 调用中石化API失败: 'LLMProcessor' object has no attribute '_is_sinopec_api_configured'", 'data': {}}
2025-06-19 14:50:38,206 - web - INFO - Excel报告已生成: F:\Python_project\制度智能审查\downloads\approval_process_report_20250619_145038.xlsx
2025-06-19 14:50:38,207 - web - INFO - 文件处理完成，生成了1个下载文件
2025-06-19 14:58:31,703 - web - INFO - Web应用启动
2025-06-19 15:04:13,222 - web - INFO - Web应用启动
2025-06-19 15:38:10,768 - web - INFO - Web应用启动
