<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志管理 - 制度智能审查系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .log-container {
            background-color: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        .log-line {
            margin-bottom: 2px;
            word-wrap: break-word;
        }
        .log-error {
            color: #f48771;
        }
        .log-warning {
            color: #dcdcaa;
        }
        .log-info {
            color: #9cdcfe;
        }
        .log-debug {
            color: #608b4e;
        }
        .error-badge {
            font-size: 0.8em;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-gear-fill"></i> 制度智能审查系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="bi bi-house"></i> 首页
                </a>
                <a class="nav-link" href="{{ url_for('upload_file') }}">
                    <i class="bi bi-upload"></i> 文件上传
                </a>
                <a class="nav-link" href="{{ url_for('environment_page') }}">
                    <i class="bi bi-tools"></i> 环境管理
                </a>
                <a class="nav-link active" href="{{ url_for('logs_page') }}">
                    <i class="bi bi-journal-text"></i> 日志管理
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="bi bi-journal-text"></i> 日志管理</h2>
                <p class="text-muted">查看系统运行日志，分析问题和性能</p>
            </div>
        </div>

        <!-- 日志摘要 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-info-circle"></i> 日志摘要</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ log_summary.日志文件|length }}</h4>
                                    <small class="text-muted">日志文件数</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info">{{ "%.2f"|format(log_summary.总大小 / 1024) }} KB</h4>
                                    <small class="text-muted">总大小</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="text-center">
                                    <h6 class="text-success">{{ log_summary.最新更新 or '无' }}</h6>
                                    <small class="text-muted">最新更新时间</small>
                                </div>
                            </div>
                        </div>
                        
                        {% if log_summary.日志文件 %}
                        <div class="mt-3">
                            <h6>日志文件列表：</h6>
                            <div class="row">
                                {% for file in log_summary.日志文件 %}
                                <div class="col-md-4 mb-2">
                                    <div class="card card-body p-2">
                                        <small>
                                            <strong>{{ file.文件名 }}</strong><br>
                                            大小: {{ "%.1f"|format(file.大小 / 1024) }} KB<br>
                                            修改: {{ file.修改时间 }}
                                        </small>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 错误分析 -->
        {% if error_analysis %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-exclamation-triangle"></i> 错误分析</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for error_type, count in error_analysis.items() %}
                            <div class="col-md-3 mb-2">
                                <span class="badge bg-danger error-badge">{{ error_type }}</span>
                                <span class="ms-2">{{ count }} 次</span>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 日志查看器 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="bi bi-terminal"></i> 实时日志</h5>
                            <div>
                                <select id="loggerSelect" class="form-select form-select-sm d-inline-block w-auto">
                                    <option value="web">Web应用日志</option>
                                    <option value="main">主程序日志</option>
                                    <option value="error">错误日志</option>
                                    <option value="llm">LLM处理日志</option>
                                    <option value="file">文件处理日志</option>
                                </select>
                                <button id="refreshBtn" class="btn btn-sm btn-outline-primary ms-2">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="logContainer" class="log-container">
                            <div id="logContent">
                                <!-- 日志内容将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 悬浮刷新按钮 -->
    <button id="floatingRefresh" class="btn btn-primary refresh-btn rounded-circle">
        <i class="bi bi-arrow-clockwise"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 日志管理JavaScript
        let currentLogger = 'web';
        let autoRefresh = false;
        let refreshInterval;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadInitialLogs();
            setupEventListeners();
        });

        function setupEventListeners() {
            // 日志选择器
            document.getElementById('loggerSelect').addEventListener('change', function() {
                currentLogger = this.value;
                loadLogs();
            });

            // 刷新按钮
            document.getElementById('refreshBtn').addEventListener('click', loadLogs);
            document.getElementById('floatingRefresh').addEventListener('click', loadLogs);

            // 双击启用自动刷新
            document.getElementById('logContainer').addEventListener('dblclick', function() {
                toggleAutoRefresh();
            });
        }

        function loadInitialLogs() {
            // 加载初始日志数据
            {% for logger_name, logs in recent_logs.items() %}
            if ('{{ logger_name }}' === currentLogger) {
                displayLogs({{ logs|tojson }});
            }
            {% endfor %}
        }

        function loadLogs() {
            const loadingHtml = '<div class="text-center"><i class="bi bi-arrow-repeat"></i> 加载中...</div>';
            document.getElementById('logContent').innerHTML = loadingHtml;

            fetch(`/api/logs/${currentLogger}?lines=100`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayLogs(data.logs);
                    } else {
                        document.getElementById('logContent').innerHTML = 
                            `<div class="text-danger">加载失败: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('logContent').innerHTML = 
                        `<div class="text-danger">网络错误: ${error.message}</div>`;
                });
        }

        function displayLogs(logs) {
            const container = document.getElementById('logContent');
            container.innerHTML = '';

            if (!logs || logs.length === 0) {
                container.innerHTML = '<div class="text-muted">暂无日志</div>';
                return;
            }

            logs.forEach(log => {
                const logLine = document.createElement('div');
                logLine.className = 'log-line';
                
                // 根据日志级别设置样式
                if (log.includes('ERROR')) {
                    logLine.className += ' log-error';
                } else if (log.includes('WARNING')) {
                    logLine.className += ' log-warning';
                } else if (log.includes('INFO')) {
                    logLine.className += ' log-info';
                } else if (log.includes('DEBUG')) {
                    logLine.className += ' log-debug';
                }
                
                logLine.textContent = log;
                container.appendChild(logLine);
            });

            // 滚动到底部
            const logContainer = document.getElementById('logContainer');
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            
            if (autoRefresh) {
                refreshInterval = setInterval(loadLogs, 5000); // 每5秒刷新
                document.getElementById('floatingRefresh').innerHTML = '<i class="bi bi-pause"></i>';
                document.getElementById('floatingRefresh').className = 'btn btn-warning refresh-btn rounded-circle';
            } else {
                clearInterval(refreshInterval);
                document.getElementById('floatingRefresh').innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
                document.getElementById('floatingRefresh').className = 'btn btn-primary refresh-btn rounded-circle';
            }
        }

        // 页面可见性变化时停止/恢复自动刷新
        document.addEventListener('visibilitychange', function() {
            if (document.hidden && autoRefresh) {
                clearInterval(refreshInterval);
            } else if (!document.hidden && autoRefresh) {
                refreshInterval = setInterval(loadLogs, 5000);
            }
        });
    </script>
</body>
</html> 