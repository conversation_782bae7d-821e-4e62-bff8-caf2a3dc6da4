{% extends "base.html" %}

{% block title %}API配置{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-cog me-2"></i>API配置管理
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        配置各种AI服务的API密钥和参数。至少启用一个AI服务以使用智能功能。
                    </div>

                    <!-- 配置表单 -->
                    <form id="apiConfigForm">
                        <!-- OpenAI配置 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fab fa-openai me-2"></i>OpenAI
                                    </h5>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="openai_enabled" name="openai_enabled">
                                        <label class="form-check-label" for="openai_enabled">启用</label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="openai_api_key" class="form-label">API密钥</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="openai_api_key" name="openai_api_key" placeholder="sk-...">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('openai_api_key')">
                                                    <i class="fas fa-eye" id="openai_api_key_icon"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="openai_base_url" class="form-label">Base URL</label>
                                            <input type="url" class="form-control" id="openai_base_url" name="openai_base_url" placeholder="https://api.openai.com/v1">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="openai_model" class="form-label">模型</label>
                                            <select class="form-select" id="openai_model" name="openai_model">
                                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                                <option value="gpt-4">GPT-4</option>
                                                <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="openai_temperature" class="form-label">温度 (0-1)</label>
                                            <input type="number" class="form-control" id="openai_temperature" name="openai_temperature" min="0" max="1" step="0.1" value="0.3">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 智谱AI配置 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-brain me-2"></i>智谱AI
                                    </h5>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="zhipu_enabled" name="zhipu_enabled">
                                        <label class="form-check-label" for="zhipu_enabled">启用</label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="zhipu_api_key" class="form-label">API密钥</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="zhipu_api_key" name="zhipu_api_key" placeholder="输入智谱AI API密钥">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('zhipu_api_key')">
                                                    <i class="fas fa-eye" id="zhipu_api_key_icon"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="zhipu_base_url" class="form-label">Base URL</label>
                                            <input type="url" class="form-control" id="zhipu_base_url" name="zhipu_base_url" placeholder="https://open.bigmodel.cn/api/paas/v4">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="zhipu_model" class="form-label">模型</label>
                                            <select class="form-select" id="zhipu_model" name="zhipu_model">
                                                <option value="glm-4">GLM-4</option>
                                                <option value="glm-4v">GLM-4V</option>
                                                <option value="glm-3-turbo">GLM-3 Turbo</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="zhipu_temperature" class="form-label">温度 (0-1)</label>
                                            <input type="number" class="form-control" id="zhipu_temperature" name="zhipu_temperature" min="0" max="1" step="0.1" value="0.3">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 百度文心一言配置 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-robot me-2"></i>百度文心一言
                                    </h5>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="baidu_enabled" name="baidu_enabled">
                                        <label class="form-check-label" for="baidu_enabled">启用</label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="baidu_api_key" class="form-label">API Key</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="baidu_api_key" name="baidu_api_key" placeholder="输入百度API Key">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('baidu_api_key')">
                                                    <i class="fas fa-eye" id="baidu_api_key_icon"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="baidu_secret_key" class="form-label">Secret Key</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="baidu_secret_key" name="baidu_secret_key" placeholder="输入百度Secret Key">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('baidu_secret_key')">
                                                    <i class="fas fa-eye" id="baidu_secret_key_icon"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="baidu_model" class="form-label">模型</label>
                                            <select class="form-select" id="baidu_model" name="baidu_model">
                                                <option value="ERNIE-Bot-turbo">ERNIE-Bot-turbo</option>
                                                <option value="ERNIE-Bot">ERNIE-Bot</option>
                                                <option value="ERNIE-Bot-4">ERNIE-Bot-4</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="baidu_temperature" class="form-label">温度 (0-1)</label>
                                            <input type="number" class="form-control" id="baidu_temperature" name="baidu_temperature" min="0" max="1" step="0.1" value="0.3">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 阿里通义千问配置 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-cloud me-2"></i>阿里通义千问
                                    </h5>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="qwen_enabled" name="qwen_enabled">
                                        <label class="form-check-label" for="qwen_enabled">启用</label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="qwen_api_key" class="form-label">API密钥</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="qwen_api_key" name="qwen_api_key" placeholder="sk-...">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('qwen_api_key')">
                                                    <i class="fas fa-eye" id="qwen_api_key_icon"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="qwen_base_url" class="form-label">Base URL</label>
                                            <input type="url" class="form-control" id="qwen_base_url" name="qwen_base_url" placeholder="https://dashscope.aliyuncs.com/api/v1">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="qwen_model" class="form-label">模型</label>
                                            <select class="form-select" id="qwen_model" name="qwen_model">
                                                <option value="qwen-turbo">Qwen-Turbo</option>
                                                <option value="qwen-plus">Qwen-Plus</option>
                                                <option value="qwen-max">Qwen-Max</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="qwen_temperature" class="form-label">温度 (0-1)</label>
                                            <input type="number" class="form-control" id="qwen_temperature" name="qwen_temperature" min="0" max="1" step="0.1" value="0.3">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 字节跳动豆包配置 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-fire me-2"></i>字节跳动豆包
                                    </h5>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="doubao_enabled" name="doubao_enabled">
                                        <label class="form-check-label" for="doubao_enabled">启用</label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="doubao_api_key" class="form-label">API密钥</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="doubao_api_key" name="doubao_api_key" placeholder="输入豆包API密钥">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('doubao_api_key')">
                                                    <i class="fas fa-eye" id="doubao_api_key_icon"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="doubao_base_url" class="form-label">Base URL</label>
                                            <input type="url" class="form-control" id="doubao_base_url" name="doubao_base_url" placeholder="https://ark.cn-beijing.volces.com/api/v3">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="doubao_model" class="form-label">模型</label>
                                            <select class="form-select" id="doubao_model" name="doubao_model">
                                                <option value="doubao-pro-4k">豆包Pro-4K</option>
                                                <option value="doubao-pro-32k">豆包Pro-32K</option>
                                                <option value="doubao-lite-4k">豆包Lite-4K</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="doubao_temperature" class="form-label">温度 (0-1)</label>
                                            <input type="number" class="form-control" id="doubao_temperature" name="doubao_temperature" min="0" max="1" step="0.1" value="0.3">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="button" class="btn btn-success" onclick="saveConfig()">
                                    <i class="fas fa-save me-2"></i>保存配置
                                </button>
                                <button type="button" class="btn btn-info" onclick="testConfig()">
                                    <i class="fas fa-vial me-2"></i>测试连接
                                </button>
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="loadConfig()">
                                <i class="fas fa-sync me-2"></i>重新加载
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 状态提示模态框 -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalTitle">操作状态</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="statusModalBody">
                <!-- 状态内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载时获取配置
document.addEventListener('DOMContentLoaded', function() {
    loadConfig();
});

// 切换密码显示/隐藏
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

// 加载配置
function loadConfig() {
    fetch('/api/config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const configs = data.configs;
                
                // 填充各个提供商的配置
                Object.keys(configs).forEach(provider => {
                    const config = configs[provider];
                    
                    // 启用状态
                    const enabledCheckbox = document.getElementById(provider + '_enabled');
                    if (enabledCheckbox) {
                        enabledCheckbox.checked = config.enabled || false;
                    }
                    
                    // 填充其他字段
                    Object.keys(config).forEach(key => {
                        const fieldId = provider + '_' + key;
                        const field = document.getElementById(fieldId);
                        if (field && key !== 'enabled') {
                            field.value = config[key] || '';
                        }
                    });
                });
                
                showStatus('配置加载成功', 'success');
            } else {
                showStatus('配置加载失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showStatus('网络错误: ' + error.message, 'error');
        });
}

// 保存配置
function saveConfig() {
    const formData = new FormData(document.getElementById('apiConfigForm'));
    const config = {};
    
    // 解析表单数据
    const providers = ['openai', 'zhipu', 'baidu', 'qwen', 'doubao'];
    
    providers.forEach(provider => {
        config[provider] = {};
        
        // 获取启用状态
        const enabledField = formData.get(provider + '_enabled');
        config[provider].enabled = enabledField === 'on';
        
        // 获取其他配置项
        for (let [key, value] of formData.entries()) {
            if (key.startsWith(provider + '_') && !key.endsWith('_enabled')) {
                const configKey = key.replace(provider + '_', '');
                config[provider][configKey] = value;
            }
        }
    });
    
    // 发送保存请求
    fetch('/api/config/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatus('配置保存成功', 'success');
        } else {
            showStatus('配置保存失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showStatus('网络错误: ' + error.message, 'error');
    });
}

// 测试配置
function testConfig() {
    const enabledProviders = [];
    const providers = ['openai', 'zhipu', 'baidu', 'qwen', 'doubao'];
    
    providers.forEach(provider => {
        const checkbox = document.getElementById(provider + '_enabled');
        if (checkbox && checkbox.checked) {
            enabledProviders.push(provider);
        }
    });
    
    if (enabledProviders.length === 0) {
        showStatus('请至少启用一个AI服务提供商', 'warning');
        return;
    }
    
    showStatus('正在测试连接...', 'info');
    
    fetch('/api/config/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({providers: enabledProviders})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let message = '测试结果:\n';
            Object.keys(data.results).forEach(provider => {
                const result = data.results[provider];
                message += `${provider}: ${result.success ? '✅ 成功' : '❌ 失败 - ' + result.error}\n`;
            });
            showStatus(message, 'info');
        } else {
            showStatus('测试失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showStatus('网络错误: ' + error.message, 'error');
    });
}

// 显示状态信息
function showStatus(message, type) {
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    const title = document.getElementById('statusModalTitle');
    const body = document.getElementById('statusModalBody');
    
    // 设置标题和样式
    switch(type) {
        case 'success':
            title.textContent = '操作成功';
            title.className = 'modal-title text-success';
            break;
        case 'error':
            title.textContent = '操作失败';
            title.className = 'modal-title text-danger';
            break;
        case 'warning':
            title.textContent = '注意';
            title.className = 'modal-title text-warning';
            break;
        case 'info':
            title.textContent = '信息';
            title.className = 'modal-title text-info';
            break;
        default:
            title.textContent = '状态';
            title.className = 'modal-title';
    }
    
    // 设置内容（保持换行格式）
    body.innerHTML = message.replace(/\n/g, '<br>');
    
    modal.show();
}
</script>

<style>
.input-group .btn {
    border-left: 0;
}

.input-group .form-control:focus + .btn {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.card-header h5 {
    color: #495057;
}

.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.btn-outline-secondary {
    border-color: #ced4da;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

.alert {
    border-left: 4px solid;
}

.alert-info {
    border-left-color: #0dcaf0;
}
</style>
{% endblock %} 