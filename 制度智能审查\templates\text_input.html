{% extends "base.html" %}

{% block title %}文本输入 - 制度智能审查系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header text-center">
                <h3 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    文本输入
                </h3>
                <p class="mb-0 mt-2">直接输入制度文本内容，自动解析生成流程图</p>
            </div>
            <div class="card-body">
                <form method="POST" id="textForm">
                    <!-- 文本输入区域 -->
                    <div class="mb-4">
                        <label for="text_content" class="form-label">
                            <i class="fas fa-file-text me-2"></i>制度文本内容
                        </label>
                        <textarea class="form-control" id="text_content" name="text_content" 
                                  rows="12" placeholder="请输入制度文本内容，系统将进行智能审查分析..." required></textarea>
                        <div class="form-text">
                            建议包含完整的审批步骤、审批人员、条件分支和时间限制等信息
                        </div>
                    </div>

                    <!-- 处理选项 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-brain me-2"></i>AI解析选项
                                    </h6>
                                    
                                    <!-- 隐藏字段，默认使用LLM -->
                                    <input type="hidden" name="use_llm" value="on">
                                    
                                    <div class="mt-2">
                                        <label for="llm_provider" class="form-label">选择AI提供商：</label>
                                        <select class="form-select" id="llm_provider" name="llm_provider" required>
                                            {% for provider in api_providers %}
                                                {% if provider.id != 'local' %}
                                                    <option value="{{ provider.id }}" 
                                                            {% if not provider.enabled or not provider.valid %}disabled{% endif %}>
                                                        {{ provider.name }}
                                                        {% if not provider.enabled %}(未启用){% endif %}
                                                        {% if not provider.valid %}(配置不完整){% endif %}
                                                    </option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                        <div class="form-text">
                                            <small>
                                                <i class="fas fa-info-circle me-1"></i>
                                                请确保已在API设置中配置了相应的API密钥
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-download me-2"></i>输出格式
                                    </h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="output_format" id="format_excel" value="excel" checked>
                                        <label class="form-check-label" for="format_excel">
                                            <i class="fas fa-file-excel me-1"></i>Excel报告 (.xlsx)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="output_format" id="format_pptx" value="pptx">
                                        <label class="form-check-label" for="format_pptx">
                                            <i class="fas fa-file-powerpoint me-1"></i>PowerPoint (.pptx)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="output_format" id="format_all" value="all">
                                        <label class="form-check-label" for="format_all">
                                            <i class="fas fa-layer-group me-1"></i>全部格式
                                        </label>
                                    </div>
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="checkbox" 
                                               id="show_raw_response" name="show_raw_response">
                                        <label class="form-check-label" for="show_raw_response">
                                            <i class="fas fa-code me-1"></i>显示API原始响应
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>返回首页
                        </a>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>开始解析
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 示例文本 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    示例文本
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-3">以下是一个制度文本示例，您可以参考格式：</p>
                <div class="bg-light p-3 rounded">
                    <pre class="mb-0" style="white-space: pre-wrap; font-size: 0.9em;">报销审批制度

1. 申请人填写报销申请单，金额在1000元以下的，直接提交给部门主管审批。

2. 部门主管收到申请后，需要在3个工作日内完成审批。审批通过后，提交给财务部门。

3. 如果金额超过1000元但不超过5000元，需要部门主管审批后，再提交给分管领导审批。

4. 分管领导需要在5个工作日内完成审批。审批通过后，提交给财务部门。

5. 如果金额超过5000元，需要经过部门主管、分管领导、总经理三级审批。

6. 总经理需要在7个工作日内完成最终审批。

7. 财务部门收到审批通过的申请后，在2个工作日内完成报销。</pre>
                </div>
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="useExample()">
                        <i class="fas fa-copy me-1"></i>使用此示例
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const textForm = document.getElementById('textForm');
    const submitBtn = document.getElementById('submitBtn');
    const processing = document.getElementById('processing');
    
    // 表单提交处理
    textForm.addEventListener('submit', function(e) {
        const textContent = document.getElementById('text_content').value.trim();
        if (!textContent) {
            e.preventDefault();
            alert('请输入制度文本内容');
            return;
        }
        
        if (textContent.length < 50) {
            if (!confirm('文本内容较短，可能影响解析效果。是否继续？')) {
                e.preventDefault();
                return;
            }
        }
        
        // 显示处理状态
        submitBtn.style.display = 'none';
        processing.style.display = 'block';
    });
    
    // 字符计数
    const textArea = document.getElementById('text_content');
    textArea.addEventListener('input', function() {
        const length = this.value.length;
        console.log(`当前文本长度: ${length} 字符`);
    });
});

function useExample() {
    const exampleText = `报销审批制度

1. 申请人填写报销申请单，金额在1000元以下的，直接提交给部门主管审批。

2. 部门主管收到申请后，需要在3个工作日内完成审批。审批通过后，提交给财务部门。

3. 如果金额超过1000元但不超过5000元，需要部门主管审批后，再提交给分管领导审批。

4. 分管领导需要在5个工作日内完成审批。审批通过后，提交给财务部门。

5. 如果金额超过5000元，需要经过部门主管、分管领导、总经理三级审批。

6. 总经理需要在7个工作日内完成最终审批。

7. 财务部门收到审批通过的申请后，在2个工作日内完成报销。`;
    
    document.getElementById('text_content').value = exampleText;
}
</script>
{% endblock %} 