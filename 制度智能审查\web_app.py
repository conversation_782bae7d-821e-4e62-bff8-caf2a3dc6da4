# web_app.py
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for, flash, session
import os
import json
import sys
import platform
import subprocess
import tempfile
import uuid
from werkzeug.utils import secure_filename
from datetime import datetime
import time
import requests

# 导入我们的模块
from file_readers import FileReaderFactory, get_file_extension_smart
from llm_processor import LLMProcessor, extract_with_llm, get_api_config
# from text_parser import TextParser  # 本地解析已禁用
from flowchart_generator import FlowchartGenerator
from excel_generator import ExcelGenerator, create_excel_report
from api_config import api_config, list_providers, set_api_config
from logger_config import get_logger, log_info, log_error, log_warning, log_analyzer

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
app.config['DOWNLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'downloads')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB最大上传限制
app.config['SECRET_KEY'] = 'intelligent_system_review_secret_key'  # 用于会话加密的密钥

# 初始化日志
web_logger = get_logger("web")
web_logger.info("Web应用启动")

# 配置
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'rtf', 'odt'}

# 确保目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['DOWNLOAD_FOLDER'], exist_ok=True)

def get_api_providers():
    """获取API提供商列表 - 仅中石化API"""
    providers_dict = list_providers()
    providers = []
    
    # 只返回中石化API
    if 'sinopec' in providers_dict:
        providers.append({
            'id': 'sinopec',
            'name': '中石化AI平台',
            'enabled': providers_dict['sinopec'].get('enabled', False),
            'valid': providers_dict['sinopec'].get('valid', False)
        })
    
    return providers

def get_provider_name(provider_id):
    """获取提供商的显示名称 - 仅中石化"""
    if provider_id == 'sinopec':
        return '中石化AI平台'
    return provider_id

def allowed_file(filename):
    """检查文件是否允许上传"""
    if not filename:
        web_logger.info(f"文件名为空")
        return False
    
    # 检查是否有扩展名
    if '.' not in filename:
        web_logger.info(f"文件名没有扩展名: {filename}")
        return False
    
    ext = filename.rsplit('.', 1)[1].lower()
    web_logger.info(f"文件名={filename}, 扩展名={ext}, 允许的扩展名={ALLOWED_EXTENSIONS}")
    return ext in ALLOWED_EXTENSIONS

def allowed_file_smart(file_path):
    """智能检查文件是否为允许的格式（包括内容检测）"""
    try:
        file_ext = get_file_extension_smart(file_path)
        if not file_ext:
            return False
        
        # 移除点号进行比较
        ext_without_dot = file_ext.lstrip('.')
        return ext_without_dot in ALLOWED_EXTENSIONS
    except Exception as e:
        web_logger.error(f"智能文件检测失败: {e}")
        return False

@app.route('/')
def index():
    """首页"""
    try:
        return render_template('index_new.html')
    except Exception as e:
        web_logger.error(f"首页渲染失败: {str(e)}", exc_info=True)
        return "系统错误，请联系管理员"

@app.route('/upload', methods=['GET', 'POST'])
def upload_file():
    """文件上传页面"""
    if request.method == 'POST':
        try:
            start_time = time.time()
            log_webapp_request('开始处理上传请求')
            
            # 获取上传的文件
            file = request.files.get('file')
            text_content = request.form.get('text_content')
            use_llm = request.form.get('use_llm', 'false').lower() == 'true'
            provider = request.form.get('provider', 'sinopec')
            
            # 如果既没有上传文件也没有输入文本，返回错误
            if not file and not text_content:
                log_webapp_error('未上传文件或输入文本')
                return jsonify({
                    'success': False,
                    'error': '请上传文件或输入文本',
                    'processing_time': f"{time.time() - start_time:.2f}秒"
                })
            
            # 文件和文本同时存在时，优先处理文件
            if file:
                # 安全获取文件名
                filename = secure_filename(file.filename)
                
                # 如果是上传的文件，确保上传目录存在
                upload_dir = os.path.join(app.config['UPLOAD_FOLDER'])
                os.makedirs(upload_dir, exist_ok=True)
                
                # 添加时间戳，避免文件名冲突
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                file_path = os.path.join(upload_dir, f"{timestamp}_{filename}")
                
                # 保存文件
                file.save(file_path)
                log_webapp_info(f'文件已保存: {file_path}')
                
                # 提取文本内容
                text_content = extract_text_from_file(file_path)
                if not text_content:
                    log_webapp_error(f'无法从文件中提取文本: {file_path}')
                    return jsonify({
                        'success': False,
                        'error': '无法从文件中提取文本，请检查文件格式或直接输入文本。',
                        'processing_time': f"{time.time() - start_time:.2f}秒"
                    })
            
            # 使用LLM处理文本
            llm_result = {'success': False, 'message': '未使用LLM处理', 'data': {}}
            
            if use_llm:
                log_webapp_info(f'开始处理文本，使用LLM: {use_llm}, 提供商: {provider}')
                try:
                    # 添加详细的调试信息
                    log_webapp_info("开始创建LLMProcessor实例...")
                    
                    # 这里强制每次都创建新的LLMProcessor实例，避免缓存问题
                    llm = LLMProcessor()
                    log_webapp_info("✓ LLMProcessor实例创建成功")
                    
                    # 检查实例是否有必要的方法
                    if hasattr(llm, '_is_sinopec_api_configured'):
                        log_webapp_info("✓ _is_sinopec_api_configured方法存在")
                        
                        # 测试方法调用
                        config_status = llm._is_sinopec_api_configured()
                        log_webapp_info(f"✓ API配置状态: {config_status}")
                    else:
                        log_webapp_error("✗ _is_sinopec_api_configured方法不存在")
                        raise AttributeError("LLMProcessor缺少_is_sinopec_api_configured方法")
                    
                    # 调用主要方法
                    log_webapp_info("开始调用extract_process_with_llm方法...")
                    llm_result = llm.extract_process_with_llm(text_content, provider=provider)
                    log_webapp_info("✓ extract_process_with_llm方法调用成功")
                    
                except Exception as e:
                    log_webapp_error(f'LLM处理失败: {str(e)}')
                    import traceback
                    log_webapp_error(f'详细错误信息: {traceback.format_exc()}')
                    llm_result = {'success': False, 'message': f'LLM处理失败: {str(e)}', 'data': {}}
            
            log_webapp_info(f'提取的文本内容 llm_result: {llm_result}')
            
            # 生成Excel格式报告
            downloads_dir = os.path.join(app.config['DOWNLOAD_FOLDER'])
            os.makedirs(downloads_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            excel_filename = f"approval_process_report_{timestamp}.xlsx"
            excel_path = os.path.join(downloads_dir, excel_filename)
            
            # 创建Excel报告
            excel_generator = ExcelGenerator()
            excel_path = excel_generator.create_excel_report(llm_result, excel_path)
            log_webapp_info(f'Excel报告已生成: {excel_path}')
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 准备下载文件列表
            download_files = []
            if os.path.exists(excel_path):
                download_files.append({
                    'name': os.path.basename(excel_path),
                    'path': excel_path,
                    'type': 'excel'
                })
            
            log_webapp_info(f'文件处理完成，生成了{len(download_files)}个下载文件')
            
            # 返回结果
            return jsonify({
                'success': True,
                'download_files': download_files,
                'processing_time': f"{processing_time:.2f}秒",
                'llm_success': llm_result.get('success', False)
            })
            
        except Exception as e:
            log_webapp_error(f'处理上传文件时出错: {str(e)}')
            return jsonify({
                'success': False,
                'error': f'处理上传文件时出错: {str(e)}',
                'processing_time': f"{time.time() - start_time:.2f}秒"
            })
    
    # GET请求，显示上传表单
    api_providers = get_api_providers()
    return render_template('upload.html', providers=api_providers)

@app.route('/text_input', methods=['GET', 'POST'])
def text_input():
    """文本输入页面"""
    if request.method == 'POST':
        try:
            text_content = request.form.get('text_content', '')
            # 始终使用LLM，不再需要检查use_llm
            use_llm = True
            llm_provider = request.form.get('llm_provider', 'sinopec')
            output_format = request.form.get('output_format', 'excel')
            show_raw_response = request.form.get('show_raw_response') == 'on'
            
            if not text_content:
                flash('请输入文本内容')
                return redirect(url_for('text_input'))
            
            result = process_text_content(text_content, use_llm, llm_provider, output_format)
            
            # 如果用户选择显示原始响应，添加到结果中
            if show_raw_response and result.get('success'):
                llm_info = result.get('llm_info', {})
                llm_analysis = result.get('llm_analysis', {})
                
                if llm_analysis:
                    result['show_raw_response'] = True
                    result['raw_response_data'] = {
                        'provider': llm_info.get('provider', '未知'),
                        'processing_time': llm_info.get('processing_time', '未知'),
                        'content_length': llm_info.get('content_length', 0),
                        'response_length': llm_info.get('response_length', 0),
                        'content': llm_analysis.get('content', ''),  # 用户原始文档
                        'content_type': llm_analysis.get('content_type', ''),
                        'sent_to_ai': llm_analysis.get('sent_to_ai', ''),  # 发送给AI的完整内容
                        'note': llm_analysis.get('note', ''),
                        'raw_response': llm_analysis.get('raw_response', ''),
                        'analysis_timestamp': llm_analysis.get('analysis_timestamp', '')
                    }
            
            return render_template('result.html', 
                                 result=result, 
                                 download_links=result.get('download_links', []))
        except Exception as e:
            flash(f'处理文本时出错: {str(e)}')
            web_logger.error(f'处理文本时出错: {str(e)}', exc_info=True)
            return redirect(url_for('text_input'))
    
    # GET请求，显示表单
    api_providers = get_api_providers()
    return render_template('text_input.html', api_providers=api_providers)

@app.route('/api_config', methods=['GET', 'POST'])
def api_config_page():
    """API配置页面"""
    if request.method == 'POST':
        # 更新配置
        provider = request.form.get('provider')
        if provider:
            config_data = {}
            for key in ['api_key', 'secret_key', 'base_url', 'model', 'temperature', 'max_tokens']:
                value = request.form.get(f'{provider}_{key}')
                if value:
                    if key in ['temperature', 'max_tokens']:
                        try:
                            value = float(value) if key == 'temperature' else int(value)
                        except ValueError:
                            continue
                    config_data[key] = value
            
            # 设置启用状态
            enabled = request.form.get(f'{provider}_enabled') == 'on'
            config_data['enabled'] = enabled
            
            # 设置SSL验证状态
            verify_ssl = request.form.get(f'{provider}_verify_ssl') == 'on'
            config_data['verify_ssl'] = verify_ssl
            
            # 更新配置
            for key, value in config_data.items():
                set_api_config(provider, **{key: value})
            
            flash(f'{provider} 配置已更新')
        
        return redirect(url_for('api_config_page'))
    
    # GET请求，显示配置页面
    api_providers = get_api_providers()
    configs = {}
    for provider in api_providers:
        configs[provider['id']] = api_config.get_config(provider['id'])
    
    return render_template('api_config.html', 
                         api_providers=api_providers, 
                         configs=configs)

@app.route('/api/config')
def get_api_config_json():
    """获取API配置JSON"""
    try:
        providers_info = list_providers()
        configs = {}
        
        for provider_id in providers_info.keys():
            configs[provider_id] = get_api_config(provider_id)
        
        return jsonify({
            'success': True,
            'configs': configs
        })
    except Exception as e:
        logger.error(f"获取API配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/config/save', methods=['POST'])
def save_api_config_json():
    """保存API配置JSON"""
    try:
        config_data = request.get_json()
        
        for provider, config in config_data.items():
            for key, value in config.items():
                set_api_config(provider, **{key: value})
        
        return jsonify({
            'success': True,
            'message': '配置保存成功'
        })
    except Exception as e:
        logger.error(f"保存API配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/config/test', methods=['POST'])
def test_api_config_json():
    """测试API配置JSON"""
    try:
        data = request.get_json()
        providers = data.get('providers', [])
        
        results = {}
        
        for provider in providers:
            try:
                # 简单的连接测试
                config = get_api_config(provider)
                if not config.get('api_key'):
                    results[provider] = {
                        'success': False,
                        'error': 'API密钥未配置'
                    }
                    continue
                
                # 检查配置完整性
                if provider == 'baidu' and not config.get('secret_key'):
                    results[provider] = {
                        'success': False,
                        'error': 'Secret Key未配置'
                    }
                else:
                    results[provider] = {
                        'success': True,
                        'message': '配置检查通过'
                    }
                    
            except Exception as e:
                results[provider] = {
                    'success': False,
                    'error': str(e)
                }
        
        return jsonify({
            'success': True,
            'results': results
        })
    except Exception as e:
        logger.error(f"测试API配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/download/<filename>')
def download_file(filename):
    """文件下载"""
    try:
        file_path = os.path.join(app.config['DOWNLOAD_FOLDER'], filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            flash('文件不存在')
            return redirect(url_for('index'))
    except Exception as e:
        flash(f'下载文件时出错: {str(e)}')
        return redirect(url_for('index'))

@app.route('/api/process', methods=['POST'])
def api_process():
    """API接口：处理文本"""
    try:
        data = request.get_json()
        text_content = data.get('text', '')
        # 始终使用LLM
        use_llm = True
        llm_provider = data.get('llm_provider', 'sinopec')
        
        if not text_content:
            return jsonify({'error': '文本内容不能为空'}), 400
        
        # 处理文本，始终使用LLM
        process_data = extract_with_llm(text_content, llm_provider)
        
        return jsonify({
            'success': True,
            'data': process_data
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/providers')
def api_providers():
    """API接口：获取可用的提供商"""
    return jsonify(list_providers())

@app.route('/api/raw_response', methods=['POST'])
def api_raw_response():
    """API接口：获取中石化AI的原始响应"""
    try:
        data = request.get_json()
        text_content = data.get('text', '')
        llm_provider = data.get('llm_provider', 'sinopec')
        
        if not text_content:
            return jsonify({'error': '文本内容不能为空'}), 400
        
        # 导入LLM处理器
        from llm_processor import LLMProcessor
        
        processor = LLMProcessor()
        
        # 只支持中石化API
        start_time = time.time()
        
        if llm_provider != "sinopec":
            return jsonify({'error': f'仅支持中石化API，不支持: {llm_provider}'}), 400
        
        raw_response = processor._call_sinopec(text_content)
        
        content_info = "文档内容"
        content_preview = text_content[:500] + '...' if len(text_content) > 500 else text_content
        
        processing_time = time.time() - start_time
        
        return jsonify({
            'success': True,
            'provider': 'sinopec',
            'content_length': len(text_content),
            'response_length': len(raw_response) if raw_response else 0,
            'processing_time': f"{processing_time:.2f}秒",
            'raw_response': raw_response,
            'content_info': content_info,
            'content_preview': content_preview,
            'note': "中石化AI平台分析结果"
        })
        
    except Exception as e:
        web_logger.error(f"获取原始响应失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/environment')
def environment_page():
    """环境管理页面"""
    # 检查环境状态
    env_status = check_environment_status()
    return render_template('environment.html', env_status=env_status)

@app.route('/api/install_dependencies', methods=['POST'])
def install_dependencies():
    """API接口：安装依赖包"""
    try:
        # 获取要安装的包列表
        data = request.get_json() or {}
        packages = data.get('packages', [])
        
        if not packages:
            # 默认安装基础包
            packages = [
                'flask', 'jieba', 'python-docx', 'chardet', 
                'requests', 'lxml', 'PyPDF2', 'pdfplumber', 'PyMuPDF'
            ]
        
        results = []
        for package in packages:
            try:
                # 尝试安装包
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", package],
                    capture_output=True, text=True, timeout=300
                )
                
                if result.returncode == 0:
                    results.append({
                        'package': package,
                        'status': 'success',
                        'message': f'{package} 安装成功'
                    })
                else:
                    results.append({
                        'package': package,
                        'status': 'error',
                        'message': f'{package} 安装失败: {result.stderr}'
                    })
            except subprocess.TimeoutExpired:
                results.append({
                    'package': package,
                    'status': 'error',
                    'message': f'{package} 安装超时'
                })
            except Exception as e:
                results.append({
                    'package': package,
                    'status': 'error',
                    'message': f'{package} 安装异常: {str(e)}'
                })
        
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/check_environment')
def check_environment():
    """API接口：检查环境状态"""
    return jsonify(check_environment_status())

def check_environment_status():
    """检查环境状态"""
    status = {
        'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        'python_ok': sys.version_info >= (3, 7),
        'packages': {}
    }
    
    # 检查关键包
    packages_to_check = [
        ('flask', 'Flask Web框架'),
        ('jieba', '中文分词库'),
        ('docx', 'Word文档处理'),
        ('chardet', '编码检测'),
        ('requests', 'HTTP请求库'),
        ('lxml', 'XML处理库'),
        ('PyPDF2', 'PDF处理库1'),
        ('pdfplumber', 'PDF处理库2'),
        ('fitz', 'PDF处理库3 (PyMuPDF)'),
    ]
    
    for package, description in packages_to_check:
        try:
            if package == 'fitz':
                import fitz
            elif package == 'docx':
                import docx
            else:
                __import__(package)
            status['packages'][package] = {
                'installed': True,
                'description': description,
                'status': '已安装'
            }
        except ImportError:
            status['packages'][package] = {
                'installed': False,
                'description': description,
                'status': '未安装'
            }
    
    return status

def process_uploaded_file(file_path, use_llm=True, llm_provider='sinopec', output_format='excel'):
    """处理上传的文件"""
    start_time = time.time()
    
    try:
        # 获取文件信息
        file_info = get_file_info(file_path)
        
        # 提取文本内容
        text_content = extract_text_from_file(file_path)
        web_logger.info(f"提取的文本内容 text_content: {text_content}")

        web_logger.info(f"开始处理文本，使用LLM: {use_llm}, 提供商: {llm_provider}")
        # 使用LLM解析流程
        
        llm_result = extract_with_llm(text_content, llm_provider)
        web_logger.info(f"提取的文本内容 llm_result: {llm_result}")
        
        # 直接使用API返回的原始数据
        process_data = llm_result
        
        # 生成流程图
        download_links = []
        
        # 根据输出格式生成文件
        if output_format in ['excel', 'all']:
            # 生成Excel报告
            excel_path = generate_excel(process_data)
            if excel_path:
                filename = os.path.basename(excel_path)
                download_links.append({
                    'format': 'Excel报告',
                    'filename': filename,
                    'url': url_for('download_file', filename=os.path.basename(excel_path)),
                    'file_type': 'excel'
                })
        
        if output_format in ['pptx', 'all']:
            # 生成PowerPoint流程图
            pptx_path = generate_pptx(process_data)
            if pptx_path:
                download_links.append({
                    'format': 'PowerPoint',
                    'filename': os.path.basename(pptx_path),
                    'url': url_for('download_file', filename=os.path.basename(pptx_path)),
                    'file_type': 'pptx'
                })
        
        # 计算处理时间
        processing_time = time.time() - start_time
        file_info['processed_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        file_info['processing_time'] = f"{processing_time:.2f}秒"
        
        result = {
            'success': True,
            'process_data': process_data,  # 直接使用API返回的原始数据
            'file_info': file_info,
            'download_links': download_links,
            'llm_info': llm_result.get('llm_info', {}),  # 保留LLM信息
            'llm_analysis': llm_result.get('llm_analysis', {})  # 保留LLM分析信息
        }
        
        web_logger.info(f"文件处理完成，生成了{len(download_links)}个下载文件")
        return result
        
    except Exception as e:
        web_logger.error(f"处理文件失败: {str(e)}", exc_info=True)
        raise

def process_text_content(text_content, use_llm=True, llm_provider='sinopec', output_format='excel'):
    """处理文本内容"""
    start_time = time.time()
    
    try:
        # 使用LLM解析流程
        llm_result = extract_with_llm(text_content, llm_provider)
        
        # 直接使用API返回的原始数据
        process_data = llm_result
        
        # 生成流程图
        download_links = []
        
        # 根据输出格式生成文件
        if output_format in ['excel', 'all']:
            # 生成Excel报告
            excel_path = generate_excel(process_data)
            if excel_path:
                filename = os.path.basename(excel_path)
                download_links.append({
                    'format': 'Excel报告',
                    'filename': filename,
                    'url': url_for('download_file', filename=filename),
                    'file_type': 'excel'
                })
        
        if output_format in ['pptx', 'all']:
            # 生成PowerPoint流程图
            pptx_path = generate_pptx(process_data)
            if pptx_path:
                download_links.append({
                    'format': 'PowerPoint',
                    'filename': os.path.basename(pptx_path),
                    'url': url_for('download_file', filename=os.path.basename(pptx_path)),
                    'file_type': 'pptx'
                })
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 文件信息
        file_info = {
            'file_type': '文本输入',
            'file_size': f"{len(text_content)} 字符",
            'char_count': len(text_content),
            'processed_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'processing_time': f"{processing_time:.2f}秒"
        }
        
        result = {
            'success': True,
            'process_data': process_data,  # 直接使用API返回的原始数据
            'file_info': file_info,
            'download_links': download_links,
            'llm_info': llm_result.get('llm_info', {}),  # 保留LLM信息
            'llm_analysis': llm_result.get('llm_analysis', {})  # 保留LLM分析信息
        }
        
        web_logger.info(f"文本处理完成，生成了{len(download_links)}个下载文件")
        return result
        
    except Exception as e:
        web_logger.error(f"处理文本失败: {str(e)}", exc_info=True)
        raise

@app.route('/logs')
def logs_page():
    """日志管理页面"""
    web_logger.info("访问日志管理页面")
    
    # 获取日志摘要
    log_summary = log_analyzer.get_log_summary()
    
    # 获取最近的日志
    recent_logs = {
        'web': log_analyzer.get_recent_logs('web', 50),
        'main': log_analyzer.get_recent_logs('main', 50),
        'error': log_analyzer.get_recent_logs('error', 50),
        'llm': log_analyzer.get_recent_logs('llm', 50),
        'file': log_analyzer.get_recent_logs('file', 50),
    }
    
    # 分析常见错误
    error_analysis = log_analyzer.analyze_common_errors('main')
    
    return render_template('logs.html', 
                         log_summary=log_summary,
                         recent_logs=recent_logs,
                         error_analysis=error_analysis)

@app.route('/api/logs/<logger_name>')
def api_get_logs(logger_name):
    """API接口：获取指定日志"""
    try:
        lines = request.args.get('lines', 100, type=int)
        logs = log_analyzer.get_recent_logs(logger_name, lines)
        
        return jsonify({
            'success': True,
            'logs': logs,
            'logger_name': logger_name,
            'lines_count': len(logs)
        })
    except Exception as e:
        web_logger.error(f"获取日志失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/logs/errors')
def api_get_error_logs():
    """API接口：获取错误日志"""
    try:
        logger_name = request.args.get('logger', 'main')
        hours = request.args.get('hours', 24, type=int)
        
        error_logs = log_analyzer.get_error_logs(logger_name, hours)
        error_analysis = log_analyzer.analyze_common_errors(logger_name)
        
        return jsonify({
            'success': True,
            'error_logs': error_logs,
            'error_analysis': error_analysis,
            'logger_name': logger_name,
            'hours': hours
        })
    except Exception as e:
        web_logger.error(f"获取错误日志失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/logs/summary')
def api_log_summary():
    """API接口：获取日志摘要"""
    try:
        summary = log_analyzer.get_log_summary()
        return jsonify({
            'success': True,
            'summary': summary
        })
    except Exception as e:
        web_logger.error(f"获取日志摘要失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/browser_test')
def browser_test():
    """浏览器兼容性测试页面"""
    web_logger.info("访问浏览器兼容性测试页面")
    return render_template('browser_test.html')

@app.route('/api_viewer')
def api_viewer():
    """API查看器页面"""
    api_providers = get_api_providers()
    return render_template('api_raw_display.html', api_providers=api_providers)

@app.route('/api/browser_compatibility')
def api_browser_compatibility():
    """浏览器兼容性API"""
    try:
        user_agent = request.headers.get('User-Agent', '')
        
        # 检测浏览器类型
        browser_info = {
            'name': 'Unknown',
            'is_chromium': False,
            'is_supported': False,
            'user_agent': user_agent
        }
        
        if 'Chrome' in user_agent:
            if 'Edg' in user_agent:
                browser_info['name'] = 'Microsoft Edge'
                browser_info['is_chromium'] = True
                browser_info['is_supported'] = True
            elif 'OPR' in user_agent:
                browser_info['name'] = 'Opera'
                browser_info['is_chromium'] = True
                browser_info['is_supported'] = True
            elif 'Vivaldi' in user_agent:
                browser_info['name'] = 'Vivaldi'
                browser_info['is_chromium'] = True
                browser_info['is_supported'] = True
            else:
                browser_info['name'] = 'Google Chrome'
                browser_info['is_chromium'] = True
                browser_info['is_supported'] = True
        elif 'Firefox' in user_agent:
            browser_info['name'] = 'Mozilla Firefox'
            browser_info['is_supported'] = True
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            browser_info['name'] = 'Safari'
            browser_info['is_supported'] = True
        
        # 推荐的浏览器列表
        recommended_browsers = [
            {
                'name': 'Google Chrome',
                'min_version': '90',
                'download_url': 'https://www.google.com/chrome/',
                'features': ['backdrop-filter', 'css-grid', 'flexbox', 'css-variables']
            },
            {
                'name': 'Microsoft Edge',
                'min_version': '90',
                'download_url': 'https://www.microsoft.com/edge',
                'features': ['backdrop-filter', 'css-grid', 'flexbox', 'css-variables']
            },
            {
                'name': 'Opera',
                'min_version': '76',
                'download_url': 'https://www.opera.com/',
                'features': ['backdrop-filter', 'css-grid', 'flexbox', 'css-variables']
            },
            {
                'name': 'Brave Browser',
                'min_version': '1.20',
                'download_url': 'https://brave.com/',
                'features': ['backdrop-filter', 'css-grid', 'flexbox', 'css-variables']
            }
        ]
        
        return jsonify({
            'success': True,
            'browser_info': browser_info,
            'recommended_browsers': recommended_browsers,
            'optimization_tips': [
                '使用最新版本的Chromium内核浏览器',
                '启用硬件加速功能',
                '清理浏览器缓存和Cookie',
                '禁用不必要的浏览器扩展',
                '确保网络连接稳定'
            ]
        })
        
    except Exception as e:
        web_logger.error(f"浏览器兼容性检测失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def get_file_info(file_path):
    """获取文件信息"""
    try:
        file_size = os.path.getsize(file_path)
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # 格式化文件大小
        if file_size < 1024:
            size_str = f"{file_size} 字节"
        elif file_size < 1024 * 1024:
            size_str = f"{file_size / 1024:.2f} KB"
        else:
            size_str = f"{file_size / (1024 * 1024):.2f} MB"
        
        # 确定文件类型
        file_type_map = {
            '.txt': 'TXT文本文件',
            '.pdf': 'PDF文档',
            '.docx': 'Word文档',
            '.doc': 'Word文档(旧版)',
            '.xlsx': 'Excel表格',
            '.pptx': 'PowerPoint演示文稿'
        }
        
        file_type = file_type_map.get(file_ext, '未知文件类型')
        
        return {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_size': size_str,
            'file_type': file_type,
            'raw_size': file_size,
            'extension': file_ext
        }
    except Exception as e:
        web_logger.error(f"获取文件信息失败: {str(e)}", exc_info=True)
        return {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_size': '未知',
            'file_type': '未知文件类型',
            'error': str(e)
        }

def extract_text_from_file(file_path):
    """从文件中提取文本内容"""
    try:
        web_logger.info(f"开始从文件提取文本: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        web_logger.info(f"文件大小: {file_size} 字节")
        
        if file_size == 0:
            raise ValueError("文件为空")
        
        # 尝试直接读取文件内容（用于调试）
        try:
            with open(file_path, 'rb') as f:
                raw_content = f.read(1024)  # 读取前1KB内容用于调试
                web_logger.info(f"文件前1KB原始内容: {raw_content}")
        except Exception as e:
            web_logger.warning(f"无法直接读取文件内容: {e}")
        
        # 使用文件读取工厂
        file_reader = FileReaderFactory()
        file_result = file_reader.read_file(file_path)
        text_content = file_result['content']
        
        # 检查提取的内容
        if not text_content:
            raise ValueError("提取的文本内容为空")
        
        # 检查内容是否只有数字
        stripped_content = text_content.strip()
        if stripped_content.isdigit():
            web_logger.warning(f"提取的内容只有数字: '{stripped_content}'，可能是文件格式不支持或内容提取失败")
            
            # 尝试使用备用方法读取
            try:
                web_logger.info("尝试使用备用方法读取文件...")
                
                # 尝试不同的编码读取文本文件
                encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
                for encoding in encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            backup_content = f.read()
                            if backup_content and len(backup_content.strip()) > len(stripped_content):
                                web_logger.info(f"使用编码 {encoding} 成功读取到更多内容")
                                text_content = backup_content
                                break
                    except Exception:
                        continue
            except Exception as e:
                web_logger.warning(f"备用读取方法失败: {e}")
        
        # 最终检查内容
        if not text_content.strip():
            raise ValueError("文件内容为空或无法读取")
        
        # 如果内容仍然只是数字，给出警告
        if text_content.strip().isdigit():
            web_logger.warning(f"最终提取的内容仍然只有数字: '{text_content.strip()}'")
            raise ValueError(f"文件内容似乎只有数字 '{text_content.strip()}'，请检查文件格式是否正确或尝试其他文件")
        
        web_logger.info(f"文本提取成功，内容长度: {len(text_content)} 字符")
        web_logger.info(f"提取的内容前100个字符: {text_content[:100]}")
        
        return text_content
        
    except Exception as e:
        web_logger.error(f"文本提取失败: {str(e)}", exc_info=True)
        raise

def generate_excel(process_data):
    """生成Excel报告"""
    try:
        # 创建输出目录
        output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'downloads')
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成输出文件路径
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = os.path.join(output_dir, f'approval_process_report_{timestamp}.xlsx')
        
        # 调用Excel生成器
        from excel_generator import create_excel_report
        excel_path = create_excel_report(process_data, output_path)
        
        web_logger.info(f"Excel报告已生成: {excel_path}")
        return excel_path
    except Exception as e:
        web_logger.error(f"生成Excel报告失败: {str(e)}", exc_info=True)
        return None

def generate_pptx(process_data):
    """生成PowerPoint流程图"""
    try:
        web_logger.info("开始生成PowerPoint流程图")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pptx_filename = f"flowchart_{timestamp}.pptx"
        pptx_path = os.path.join(app.config['DOWNLOAD_FOLDER'], pptx_filename)
        
        # 生成PowerPoint流程图
        create_pptx_flowchart(process_data, pptx_path)
        web_logger.info(f"PowerPoint流程图生成成功: {pptx_path}")
        return pptx_path
        
    except Exception as e:
        web_logger.error(f"PowerPoint流程图生成失败: {str(e)}", exc_info=True)
        return None

# 添加api_debug路由，重定向到api_viewer
@app.route('/api_debug')
def api_debug():
    """API调试页面 - 重定向到API查看器"""
    web_logger.info("访问API调试页面，重定向到API查看器")
    return redirect(url_for('api_viewer'))

@app.route('/test_llm')
def test_llm():
    """测试LLMProcessor是否正常工作"""
    try:
        from llm_processor import LLMProcessor
        
        # 创建实例
        processor = LLMProcessor()
        
        # 检查方法是否存在
        if hasattr(processor, '_is_sinopec_api_configured'):
            config_status = processor._is_sinopec_api_configured()
            return f"✓ LLMProcessor正常工作，API配置状态: {config_status}"
        else:
            return "✗ LLMProcessor缺少_is_sinopec_api_configured方法"
            
    except Exception as e:
        import traceback
        return f"✗ LLMProcessor测试失败: {str(e)}<br><pre>{traceback.format_exc()}</pre>"

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000) 