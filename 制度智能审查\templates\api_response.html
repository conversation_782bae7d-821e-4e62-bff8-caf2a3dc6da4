{% extends "base.html" %}

{% block title %}API响应展示 - 制度智能审查系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-code me-2"></i>
                            API响应展示
                        </h4>
                        <div>
                            <a href="{{ url_for('index') }}" class="btn btn-outline-light btn-sm me-2">
                                <i class="fas fa-home me-1"></i>返回首页
                            </a>
                            <button id="refreshBtn" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                        </div>
                    </div>
                    <p class="mb-0 mt-2">直接查看各个AI提供商的原始API响应数据</p>
                </div>
                <div class="card-body">
                    <!-- 输入区域 -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="testContent" class="form-label">
                                    <i class="fas fa-edit me-1"></i>测试内容
                                </label>
                                <textarea id="testContent" class="form-control" rows="6" placeholder="请输入要分析的制度文本内容...">海南炼化公司域长负责制运行管理细则

第一条 为了规范域长负责制的运行管理，确保各项工作有序进行，特制定本细则。

第二条 域长负责制是指在特定区域内，由域长统一负责该区域的安全、生产、环保等各项工作。

第三条 审批流程：
1. 申请人提交申请材料
2. 直接主管进行初步审核  
3. 域长进行最终审批
4. 相关部门进行备案</textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="apiProvider" class="form-label">
                                    <i class="fas fa-robot me-1"></i>AI提供商
                                </label>
                                <select id="apiProvider" class="form-select">
                                    {% for provider in api_providers %}
                                    <option value="{{ provider.id }}">{{ provider.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mt-3">
                                <button id="testApiBtn" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-play me-2"></i>测试API
                                </button>
                            </div>
                            <div class="mt-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showRawRequest" checked>
                                    <label class="form-check-label" for="showRawRequest">
                                        显示请求数据
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoRefresh">
                                    <label class="form-check-label" for="autoRefresh">
                                        自动刷新(10秒)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载状态 -->
                    <div id="loadingIndicator" class="text-center mb-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">正在调用API，请稍候...</p>
                    </div>

                    <!-- 响应展示区域 -->
                    <div id="responseContainer" style="display: none;">
                        <!-- API信息概览 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h5 id="providerName">-</h5>
                                        <small>AI提供商</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5 id="processingTime">-</h5>
                                        <small>处理耗时</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h5 id="contentLength">-</h5>
                                        <small>内容长度</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-secondary text-white">
                                    <div class="card-body text-center">
                                        <h5 id="responseLength">-</h5>
                                        <small>响应长度</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 详细响应数据 -->
                        <div class="accordion" id="responseAccordion">
                            <!-- 请求数据 -->
                            <div class="accordion-item" id="requestSection">
                                <h2 class="accordion-header" id="requestHeader">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#requestCollapse">
                                        <i class="fas fa-arrow-up me-2"></i>
                                        发送的数据
                                        <span id="requestBadge" class="badge bg-primary ms-2">-</span>
                                    </button>
                                </h2>
                                <div id="requestCollapse" class="accordion-collapse collapse show" data-bs-parent="#responseAccordion">
                                    <div class="accordion-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted" id="requestInfo">-</small>
                                            <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('requestContent')">
                                                <i class="fas fa-copy me-1"></i>复制
                                            </button>
                                        </div>
                                        <pre id="requestContent" class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-size: 0.85em;">-</pre>
                                        <div id="requestNote" class="alert alert-info mt-3" style="display: none;">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <span id="requestNoteText">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 原始响应 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="responseHeader">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#responseCollapse">
                                        <i class="fas fa-arrow-down me-2"></i>
                                        API原始响应
                                        <span id="responseBadge" class="badge bg-success ms-2">-</span>
                                    </button>
                                </h2>
                                <div id="responseCollapse" class="accordion-collapse collapse" data-bs-parent="#responseAccordion">
                                    <div class="accordion-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-server me-1"></i>
                                                <span id="responseInfo">来源: API响应</span>
                                            </small>
                                            <button class="btn btn-sm btn-outline-success" onclick="copyToClipboard('responseContent')">
                                                <i class="fas fa-copy me-1"></i>复制
                                            </button>
                                        </div>
                                        <pre id="responseContent" class="bg-dark text-light p-3 rounded" style="max-height: 500px; overflow-y: auto; font-size: 0.85em;">-</pre>
                                    </div>
                                </div>
                            </div>

                            <!-- 解析结果 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="parsedHeader">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#parsedCollapse">
                                        <i class="fas fa-cogs me-2"></i>
                                        解析后的结果
                                        <span id="parsedBadge" class="badge bg-warning ms-2">-</span>
                                    </button>
                                </h2>
                                <div id="parsedCollapse" class="accordion-collapse collapse" data-bs-parent="#responseAccordion">
                                    <div class="accordion-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-magic me-1"></i>
                                                系统解析后的结构化数据
                                            </small>
                                            <button class="btn btn-sm btn-outline-warning" onclick="copyToClipboard('parsedContent')">
                                                <i class="fas fa-copy me-1"></i>复制
                                            </button>
                                        </div>
                                        <pre id="parsedContent" class="bg-light text-dark p-3 rounded border" style="max-height: 500px; overflow-y: auto; font-size: 0.85em;">-</pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 错误信息 -->
                    <div id="errorContainer" class="alert alert-danger" style="display: none;">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>API调用失败</h5>
                        <p id="errorMessage">-</p>
                        <div id="errorDetails" class="mt-3" style="display: none;">
                            <strong>详细信息：</strong>
                            <pre id="errorDetailsContent" class="bg-dark text-light p-2 rounded mt-2" style="font-size: 0.8em;">-</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let autoRefreshInterval = null;

// 复制到剪贴板功能
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(() => {
        // 显示复制成功提示
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>已复制';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-primary', 'btn-outline-success', 'btn-outline-warning');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动选择文本复制');
    });
}

// 格式化JSON
function formatJSON(jsonString) {
    try {
        const obj = JSON.parse(jsonString);
        return JSON.stringify(obj, null, 2);
    } catch (e) {
        return jsonString;
    }
}

// 测试API
async function testAPI() {
    const content = document.getElementById('testContent').value.trim();
    const provider = document.getElementById('apiProvider').value;
    const showRawRequest = document.getElementById('showRawRequest').checked;
    
    if (!content) {
        alert('请输入测试内容');
        return;
    }
    
    // 显示加载状态
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('responseContainer').style.display = 'none';
    document.getElementById('errorContainer').style.display = 'none';
    
    // 禁用按钮
    const btn = document.getElementById('testApiBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';
    
    try {
        const response = await fetch('/api/raw_response', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: content,
                llm_provider: provider
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 显示成功结果
            displayAPIResponse(result, showRawRequest);
        } else {
            // 显示错误
            displayError(result.error || '未知错误');
        }
        
    } catch (error) {
        console.error('API调用失败:', error);
        displayError('网络错误: ' + error.message);
    } finally {
        // 恢复按钮状态
        document.getElementById('loadingIndicator').style.display = 'none';
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-play me-2"></i>测试API';
    }
}

// 显示API响应
function displayAPIResponse(result, showRawRequest) {
    // 更新概览信息
    document.getElementById('providerName').textContent = result.provider || '-';
    document.getElementById('processingTime').textContent = result.processing_time || '-';
    document.getElementById('contentLength').textContent = (result.content_length || 0) + ' 字符';
    document.getElementById('responseLength').textContent = (result.response_length || 0) + ' 字符';
    
    // 更新请求数据
    if (showRawRequest) {
        document.getElementById('requestSection').style.display = 'block';
        document.getElementById('requestBadge').textContent = (result.content_length || 0) + ' 字符';
        document.getElementById('requestContent').textContent = result.content_preview || result.content || '-';
        document.getElementById('requestInfo').textContent = result.content_info || '发送的内容';
        
        // 显示说明信息
        if (result.note) {
            document.getElementById('requestNote').style.display = 'block';
            document.getElementById('requestNoteText').textContent = result.note;
        } else {
            document.getElementById('requestNote').style.display = 'none';
        }
    } else {
        document.getElementById('requestSection').style.display = 'none';
    }
    
    // 更新原始响应
    document.getElementById('responseBadge').textContent = (result.response_length || 0) + ' 字符';
    document.getElementById('responseContent').textContent = formatJSON(result.raw_response || '-');
    document.getElementById('responseInfo').textContent = `来源: ${result.provider || '未知'} API`;
    
    // 更新解析结果（如果有的话）
    if (result.parsed_result) {
        document.getElementById('parsedBadge').textContent = 'JSON';
        document.getElementById('parsedContent').textContent = formatJSON(result.parsed_result);
    } else {
        document.getElementById('parsedBadge').textContent = '无';
        document.getElementById('parsedContent').textContent = '暂无解析结果';
    }
    
    // 显示响应容器
    document.getElementById('responseContainer').style.display = 'block';
}

// 显示错误
function displayError(errorMessage, errorDetails = null) {
    document.getElementById('errorMessage').textContent = errorMessage;
    
    if (errorDetails) {
        document.getElementById('errorDetails').style.display = 'block';
        document.getElementById('errorDetailsContent').textContent = errorDetails;
    } else {
        document.getElementById('errorDetails').style.display = 'none';
    }
    
    document.getElementById('errorContainer').style.display = 'block';
}

// 自动刷新功能
function toggleAutoRefresh() {
    const autoRefresh = document.getElementById('autoRefresh').checked;
    
    if (autoRefresh) {
        autoRefreshInterval = setInterval(() => {
            if (document.getElementById('testContent').value.trim()) {
                testAPI();
            }
        }, 10000);
    } else {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }
}

// 事件监听
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('testApiBtn').addEventListener('click', testAPI);
    document.getElementById('refreshBtn').addEventListener('click', testAPI);
    document.getElementById('autoRefresh').addEventListener('change', toggleAutoRefresh);
    
    // 快捷键支持
    document.getElementById('testContent').addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            testAPI();
        }
    });
});

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
</script>

<style>
.accordion-button:not(.collapsed) {
    background-color: rgba(13, 110, 253, 0.1);
    border-color: rgba(13, 110, 253, 0.25);
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

#testContent {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.form-check {
    margin-bottom: 0.5rem;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %} 