<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境管理 - 制度智能审查</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-badge {
            font-size: 0.8em;
        }
        .package-card {
            transition: all 0.3s ease;
        }
        .package-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .install-progress {
            display: none;
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        .log-success {
            background-color: #d1edff;
            color: #0c63e4;
        }
        .log-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log-info {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-gear-fill"></i> 环境管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house"></i> 返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 系统信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle"></i> 系统信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Python版本:</strong> 
                                    <span class="badge {% if env_status.python_ok %}bg-success{% else %}bg-danger{% endif %}">
                                        {{ env_status.python_version }}
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>系统状态:</strong> 
                                    {% if env_status.python_ok %}
                                        <span class="badge bg-success">正常</span>
                                    {% else %}
                                        <span class="badge bg-danger">需要升级Python</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 一键安装 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-download"></i> 一键安装
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">点击下面的按钮自动安装所有必需的依赖包</p>
                        <button id="installAllBtn" class="btn btn-primary btn-lg">
                            <i class="bi bi-cloud-download"></i> 一键安装所有依赖
                        </button>
                        <button id="installMissingBtn" class="btn btn-warning btn-lg ms-2">
                            <i class="bi bi-exclamation-triangle"></i> 只安装缺失的包
                        </button>
                        <button id="refreshBtn" class="btn btn-secondary btn-lg ms-2">
                            <i class="bi bi-arrow-clockwise"></i> 刷新状态
                        </button>
                        
                        <!-- 安装进度 -->
                        <div id="installProgress" class="install-progress mt-3">
                            <div class="progress">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <p id="progressText" class="mt-2 text-center">准备安装...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 依赖包状态 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-box-seam"></i> 依赖包状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="packagesContainer">
                            {% for package, info in env_status.packages.items() %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card package-card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            {{ package }}
                                            <span class="status-badge badge {% if info.installed %}bg-success{% else %}bg-danger{% endif %} float-end">
                                                {% if info.installed %}
                                                    <i class="bi bi-check-circle"></i> 已安装
                                                {% else %}
                                                    <i class="bi bi-x-circle"></i> 未安装
                                                {% endif %}
                                            </span>
                                        </h6>
                                        <p class="card-text text-muted small">{{ info.description }}</p>
                                        {% if not info.installed %}
                                        <button class="btn btn-sm btn-outline-primary install-single-btn" 
                                                data-package="{{ package }}">
                                            <i class="bi bi-download"></i> 安装
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 安装日志 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-terminal"></i> 安装日志
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="logContainer" class="log-container">
                            <div class="log-entry log-info">
                                <i class="bi bi-info-circle"></i> 准备就绪，等待安装命令...
                            </div>
                        </div>
                        <button id="clearLogBtn" class="btn btn-sm btn-outline-secondary mt-2">
                            <i class="bi bi-trash"></i> 清空日志
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 日志管理
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            
            const icon = type === 'success' ? 'check-circle' : 
                        type === 'error' ? 'x-circle' : 'info-circle';
            
            logEntry.innerHTML = `<i class="bi bi-${icon}"></i> ${new Date().toLocaleTimeString()} - ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = 
                '<div class="log-entry log-info"><i class="bi bi-info-circle"></i> 日志已清空</div>';
        }

        // 更新进度条
        function updateProgress(percent, text) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const progressContainer = document.getElementById('installProgress');
            
            progressContainer.style.display = 'block';
            progressBar.style.width = percent + '%';
            progressText.textContent = text;
            
            if (percent >= 100) {
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 2000);
            }
        }

        // 安装依赖包
        async function installPackages(packages = []) {
            const installAllBtn = document.getElementById('installAllBtn');
            const installMissingBtn = document.getElementById('installMissingBtn');
            
            // 禁用按钮
            installAllBtn.disabled = true;
            installMissingBtn.disabled = true;
            
            addLog('开始安装依赖包...', 'info');
            updateProgress(10, '准备安装...');
            
            try {
                const response = await fetch('/api/install_dependencies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ packages: packages })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    let successCount = 0;
                    let totalCount = result.results.length;
                    
                    result.results.forEach((item, index) => {
                        const progress = ((index + 1) / totalCount) * 80 + 10;
                        updateProgress(progress, `安装 ${item.package}...`);
                        
                        if (item.status === 'success') {
                            addLog(item.message, 'success');
                            successCount++;
                        } else {
                            addLog(item.message, 'error');
                        }
                    });
                    
                    updateProgress(100, `安装完成！成功: ${successCount}/${totalCount}`);
                    addLog(`安装完成！成功安装 ${successCount}/${totalCount} 个包`, 'info');
                    
                    // 刷新页面状态
                    setTimeout(() => {
                        location.reload();
                    }, 3000);
                    
                } else {
                    addLog(`安装失败: ${result.error}`, 'error');
                    updateProgress(0, '安装失败');
                }
                
            } catch (error) {
                addLog(`网络错误: ${error.message}`, 'error');
                updateProgress(0, '网络错误');
            } finally {
                // 重新启用按钮
                installAllBtn.disabled = false;
                installMissingBtn.disabled = false;
            }
        }

        // 获取缺失的包
        function getMissingPackages() {
            const packages = [];
            document.querySelectorAll('.package-card').forEach(card => {
                const badge = card.querySelector('.status-badge');
                if (badge && badge.classList.contains('bg-danger')) {
                    const packageName = card.querySelector('.card-title').textContent.trim().split('\n')[0];
                    packages.push(packageName);
                }
            });
            return packages;
        }

        // 事件监听器
        document.getElementById('installAllBtn').addEventListener('click', () => {
            installPackages(); // 空数组表示安装所有包
        });

        document.getElementById('installMissingBtn').addEventListener('click', () => {
            const missingPackages = getMissingPackages();
            if (missingPackages.length === 0) {
                addLog('没有缺失的包需要安装', 'info');
                return;
            }
            installPackages(missingPackages);
        });

        document.getElementById('refreshBtn').addEventListener('click', () => {
            location.reload();
        });

        document.getElementById('clearLogBtn').addEventListener('click', clearLog);

        // 单个包安装
        document.querySelectorAll('.install-single-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const packageName = e.target.getAttribute('data-package');
                installPackages([packageName]);
            });
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('环境管理页面已加载', 'info');
            
            // 检查是否有缺失的包
            const missingCount = getMissingPackages().length;
            if (missingCount > 0) {
                addLog(`检测到 ${missingCount} 个包未安装，建议点击"只安装缺失的包"`, 'info');
            } else {
                addLog('所有依赖包都已安装完成！', 'success');
            }
        });
    </script>
</body>
</html> 