#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块
提供统一的日志管理功能
"""

import logging
import os
import sys
from datetime import datetime
from logging.handlers import RotatingFileHandler
from typing import Dict, Optional

# 默认日志格式
DEFAULT_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

class LoggerManager:
    """日志管理器"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LoggerManager, cls).__new__(cls)
            cls._instance._loggers = {}
            cls._instance._init_logger_dir()
        return cls._instance
    
    def _init_logger_dir(self):
        """初始化日志目录"""
        self.log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def get_logger(self, name: str, level: int = logging.INFO, 
                  log_file: Optional[str] = None, 
                  format_str: str = DEFAULT_FORMAT) -> logging.Logger:
        """获取日志记录器
        
        Args:
            name: 日志记录器名称
            level: 日志级别
            log_file: 日志文件名，如果为None则使用name.log
            format_str: 日志格式
            
        Returns:
            logging.Logger: 日志记录器
        """
        if name in self._loggers:
            return self._loggers[name]
        
        # 创建日志记录器
        logger = logging.getLogger(name)
        logger.setLevel(level)
        
        # 清除已有的处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 创建格式化器
        formatter = logging.Formatter(format_str)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 创建文件处理器
        if log_file is None:
            log_file = f"{name}.log"
        
        file_path = os.path.join(self.log_dir, log_file)
        file_handler = logging.handlers.RotatingFileHandler(
            file_path, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 缓存日志记录器
        self._loggers[name] = logger
        return logger
    
    def get_file_logger(self, name: str, level: int = logging.INFO) -> logging.Logger:
        """获取文件操作日志记录器"""
        return self.get_logger(name, level, f"{name}.log")
    
    def get_web_logger(self) -> logging.Logger:
        """获取Web应用日志记录器"""
        return self.get_logger("web", logging.INFO, "web.log")
    
    def get_api_logger(self) -> logging.Logger:
        """获取API日志记录器"""
        return self.get_logger("api", logging.INFO, "api.log")

# 全局日志管理器
_logger_manager = LoggerManager()

# 导出函数
def get_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """获取日志记录器"""
    return _logger_manager.get_logger(name, level)

def get_file_logger() -> logging.Logger:
    """获取文件操作日志记录器"""
    return _logger_manager.get_file_logger("file")

def get_web_logger() -> logging.Logger:
    """获取Web应用日志记录器"""
    return _logger_manager.get_web_logger()

def get_api_logger() -> logging.Logger:
    """获取API日志记录器"""
    return _logger_manager.get_api_logger()

# 辅助函数
def log_info(message: str, logger_name: str = "main") -> None:
    """记录信息日志"""
    logger = get_logger(logger_name)
    logger.info(message)

def log_warning(message: str, logger_name: str = "main") -> None:
    """记录警告日志"""
    logger = get_logger(logger_name)
    logger.warning(message)

def log_error(message: str, logger_name: str = "main") -> None:
    """记录错误日志"""
    logger = get_logger(logger_name)
    logger.error(message)

def log_debug(message: str, logger_name: str = "main") -> None:
    """记录调试日志"""
    logger = get_logger(logger_name)
    logger.debug(message)

def log_exception(message, logger_name="main"):
    """记录异常日志（包含堆栈信息）"""
    logger = get_logger(logger_name)
    logger.exception(message)

# 日志分析功能
class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = log_dir
    
    def get_recent_logs(self, logger_name="main", lines=100):
        """获取最近的日志"""
        log_file = os.path.join(self.log_dir, f"{logger_name}.log")
        if not os.path.exists(log_file):
            return []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if len(all_lines) > lines else all_lines
        except Exception as e:
            return [f"读取日志文件失败: {e}"]
    
    def get_error_logs(self, logger_name="main", hours=24):
        """获取指定时间内的错误日志"""
        log_file = os.path.join(self.log_dir, f"{logger_name}.log")
        if not os.path.exists(log_file):
            return []
        
        error_logs = []
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if 'ERROR' in line or 'EXCEPTION' in line:
                        error_logs.append(line.strip())
        except Exception as e:
            return [f"读取错误日志失败: {e}"]
        
        return error_logs
    
    def analyze_common_errors(self, logger_name="main"):
        """分析常见错误"""
        error_logs = self.get_error_logs(logger_name)
        error_patterns = {}
        
        for log in error_logs:
            # 简单的错误模式识别
            if "JSON" in log:
                error_patterns["JSON解析错误"] = error_patterns.get("JSON解析错误", 0) + 1
            elif "文件" in log:
                error_patterns["文件处理错误"] = error_patterns.get("文件处理错误", 0) + 1
            elif "API" in log:
                error_patterns["API调用错误"] = error_patterns.get("API调用错误", 0) + 1
            elif "网络" in log or "连接" in log:
                error_patterns["网络连接错误"] = error_patterns.get("网络连接错误", 0) + 1
            else:
                error_patterns["其他错误"] = error_patterns.get("其他错误", 0) + 1
        
        return error_patterns
    
    def get_log_summary(self):
        """获取日志摘要"""
        summary = {
            "日志文件": [],
            "总大小": 0,
            "最新更新": None
        }
        
        if not os.path.exists(self.log_dir):
            return summary
        
        for filename in os.listdir(self.log_dir):
            if filename.endswith('.log'):
                filepath = os.path.join(self.log_dir, filename)
                stat = os.stat(filepath)
                summary["日志文件"].append({
                    "文件名": filename,
                    "大小": stat.st_size,
                    "修改时间": datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
                summary["总大小"] += stat.st_size
                
                if summary["最新更新"] is None or stat.st_mtime > (summary.get("最新更新_timestamp", 0)):
                    summary["最新更新"] = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                    summary["最新更新_timestamp"] = stat.st_mtime
        
        return summary

# 全局日志分析器
log_analyzer = LogAnalyzer()

if __name__ == "__main__":
    # 测试日志功能
    logger = get_logger("test")
    logger.info("这是一条测试信息")
    logger.warning("这是一条测试警告")
    logger.error("这是一条测试错误")
    
    # 测试日志分析
    print("日志摘要:", log_analyzer.get_log_summary())
    print("最近日志:", log_analyzer.get_recent_logs("test", 5))

# 设置默认日志级别
logging.basicConfig(level=logging.INFO, format=DEFAULT_FORMAT) 