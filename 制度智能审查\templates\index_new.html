{% extends "base.html" %}

{% block title %}首页 - 制度智能审查系统{% endblock %}

{% block extra_css %}
<style>
/* 卡片悬停效果 */
.shadow-hover {
    transition: all 0.3s ease;
}

.shadow-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

/* 特性图标 */
.feature-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
}

.feature-icon-sm {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

/* 柔和背景色 */
.bg-primary-soft {
    background-color: rgba(13, 110, 253, 0.1);
}

.bg-success-soft {
    background-color: rgba(25, 135, 84, 0.1);
}

.bg-info-soft {
    background-color: rgba(13, 202, 240, 0.1);
}

.bg-warning-soft {
    background-color: rgba(255, 193, 7, 0.1);
}

/* 圆形图标居中 */
.rounded-circle.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

/* 功能卡片 */
.feature-card {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.08);
}

/* 推荐标签 */
.ribbon {
    width: 150px;
    height: 150px;
    overflow: hidden;
    position: absolute;
    z-index: 1;
}

.ribbon span {
    position: absolute;
    display: block;
    width: 225px;
    padding: 8px 0;
    background-color: #ff6b6b;
    box-shadow: 0 5px 10px rgba(0,0,0,.1);
    color: #fff;
    font-size: 13px;
    font-weight: bold;
    text-shadow: 0 1px 1px rgba(0,0,0,.2);
    text-transform: uppercase;
    text-align: center;
}

.ribbon-top-right {
    top: -10px;
    right: -10px;
}

.ribbon-top-right span {
    left: -25px;
    top: 30px;
    transform: rotate(45deg);
}
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold text-primary">制度智能审查系统</h1>
                <p class="lead mt-3">
                    基于大语言模型的制度智能审查与流程可视化工具
                </p>
                
                <div class="d-flex justify-content-center mt-4">
                    <span class="badge bg-success me-2 p-2">
                        <i class="fas fa-check-circle me-1"></i>
                        支持多种文件格式
                    </span>
                    <span class="badge bg-primary me-2 p-2">
                        <i class="fas fa-robot me-1"></i>
                        智能流程提取
                    </span>
                    <span class="badge bg-info p-2">
                        <i class="fas fa-project-diagram me-1"></i>
                        多种图表格式
                    </span>
                </div>
            </div>
            
            <!-- 功能卡片 -->
            <div class="row g-4 mb-5 justify-content-center">
                <!-- 文本输入卡片 -->
                <div class="col-md-5">
                    <div class="card feature-card h-100 shadow-hover">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon bg-primary-soft rounded-circle mb-4 mx-auto">
                                <i class="fas fa-keyboard text-primary"></i>
                            </div>
                            <h4 class="card-title mb-3">文本输入</h4>
                            <p class="card-text mb-4">
                                直接粘贴或输入制度文本内容，系统将自动进行智能审查并生成流程图
                            </p>
                            <a href="{{ url_for('text_input') }}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-keyboard me-2"></i>输入文本
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 文件上传卡片 -->
                <div class="col-md-5">
                    <div class="card feature-card h-100 shadow-hover">
                        <div class="ribbon ribbon-top-right"><span>推荐</span></div>
                        <div class="card-body text-center p-4">
                            <div class="feature-icon bg-success-soft rounded-circle mb-4 mx-auto">
                                <i class="fas fa-file-upload text-success"></i>
                            </div>
                            <h4 class="card-title mb-3">文件上传</h4>
                            <p class="card-text mb-4">
                                上传Word、PDF、Excel等格式的制度文件，系统将提取文本并进行智能审查
                            </p>
                            <a href="{{ url_for('upload_file') }}" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-upload me-2"></i>上传文件
                            </a>
                        </div>
                    </div>
                </div>
                

            </div>
            
            <!-- 系统特点 -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header bg-light">
                            <h4 class="mb-0">系统特点</h4>
                        </div>
                        <div class="card-body">
                            <div class="row g-4 justify-content-center">
                                <div class="col-md-6 col-lg-4">
                                    <div class="text-center">
                                        <div class="feature-icon-sm bg-primary-soft rounded-circle mb-3 mx-auto">
                                            <i class="fas fa-robot text-primary"></i>
                                        </div>
                                        <h5>智能解析</h5>
                                        <p class="small text-muted">
                                            基于大语言模型的智能解析，准确进行制度审查
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4">
                                    <div class="text-center">
                                        <div class="feature-icon-sm bg-success-soft rounded-circle mb-3 mx-auto">
                                            <i class="fas fa-file-alt text-success"></i>
                                        </div>
                                        <h5>多格式支持</h5>
                                        <p class="small text-muted">
                                            支持TXT、PDF、Word等多种文件格式
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4">
                                    <div class="text-center">
                                        <div class="feature-icon-sm bg-info-soft rounded-circle mb-3 mx-auto">
                                            <i class="fas fa-project-diagram text-info"></i>
                                        </div>
                                        <h5>多样化输出</h5>
                                        <p class="small text-muted">
                                            支持生成Excel详细报告和PowerPoint流程图
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 