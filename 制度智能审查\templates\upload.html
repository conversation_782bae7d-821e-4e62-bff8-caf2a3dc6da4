{% extends "base.html" %}

{% block title %}文件上传 - 制度智能审查系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header text-center">
                <h3 class="mb-0">
                    <i class="fas fa-upload me-2"></i>
                    文件上传
                </h3>
                <p class="mb-0 mt-2">上传制度文档，自动解析生成流程图</p>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <!-- 文件选择 -->
                    <div class="mb-4">
                        <label for="file" class="form-label">
                            <i class="fas fa-file me-2"></i>选择文件
                        </label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept=".txt,.pdf,.docx" required>
                        <div class="form-text">
                            支持格式：TXT、PDF、Word (.docx)，最大文件大小：16MB
                        </div>
                    </div>

                    <!-- 处理选项 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-brain me-2"></i>AI解析选项
                                    </h6>
                                    
                                    <!-- 隐藏字段，默认使用LLM -->
                                    <input type="hidden" name="use_llm" value="on">
                                    
                                    <div class="mt-2">
                                        <label for="llm_provider" class="form-label">选择AI提供商：</label>
                                        <select class="form-select" id="llm_provider" name="llm_provider" required>
                                                                                    {% for provider in providers %}
                                            {% if provider.id != 'local' %}
                                                <option value="{{ provider.id }}" 
                                                        {% if not provider.enabled or not provider.valid %}disabled{% endif %}>
                                                    {{ provider.name }}
                                                    {% if not provider.enabled %}(未启用){% endif %}
                                                    {% if not provider.valid %}(配置不完整){% endif %}
                                                </option>
                                            {% endif %}
                                        {% endfor %}
                                        </select>
                                        <div class="form-text">
                                            <small>
                                                <i class="fas fa-info-circle me-1"></i>
                                                请确保已在API设置中配置了相应的API密钥
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-download me-2"></i>输出格式
                                    </h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="output_format" id="format_excel" value="excel" checked>
                                        <label class="form-check-label" for="format_excel">
                                            <i class="fas fa-file-excel me-1"></i>Excel报告 (.xlsx)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="output_format" id="format_pptx" value="pptx">
                                        <label class="form-check-label" for="format_pptx">
                                            <i class="fas fa-file-powerpoint me-1"></i>PowerPoint (.pptx)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="output_format" id="format_all" value="all">
                                        <label class="form-check-label" for="format_all">
                                            <i class="fas fa-layer-group me-1"></i>全部格式
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                            <i class="fas fa-cogs me-2"></i>
                            开始处理
                        </button>
                        <div class="mt-3" id="processing" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">处理中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在处理文件，请稍候...</p>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 帮助信息 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    上传提示
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success me-2"></i>支持的文件格式：</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-file-alt me-2"></i>TXT文本文件</li>
                            <li><i class="fas fa-file-pdf me-2"></i>PDF文档</li>
                            <li><i class="fas fa-file-word me-2"></i>Word文档 (.docx)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-lightbulb text-warning me-2"></i>最佳实践：</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right me-2"></i>文档内容清晰完整</li>
                            <li><i class="fas fa-arrow-right me-2"></i>包含明确的审批步骤</li>
                            <li><i class="fas fa-arrow-right me-2"></i>标明审批人员和时限</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const submitBtn = document.getElementById('submitBtn');
    const processing = document.getElementById('processing');
    
    // 表单提交处理
    uploadForm.addEventListener('submit', function(e) {
        const fileInput = document.getElementById('file');
        if (!fileInput.files.length) {
            e.preventDefault();
            alert('请选择要上传的文件');
            return;
        }
        
        // 显示处理状态
        submitBtn.style.display = 'none';
        processing.style.display = 'block';
    });
    
    // 文件选择预览
    document.getElementById('file').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            console.log(`选择的文件: ${file.name}, 大小: ${fileSize}MB`);
            
            if (file.size > 16 * 1024 * 1024) {
                alert('文件大小超过16MB限制');
                this.value = '';
            }
        }
    });
});
</script>
{% endblock %} 