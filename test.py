# 使用编程函数功能，请仔细阅读下面代码规范要求
# 1. 请保留try catch结构代码，以便代码不要直接抛出异常无法处理
# 2. return 内容一定是一个dict字典结构，不要直接输出字符串或其他异常信息；
# 3. 如果要输出错误信息，可以直接在返回的字典中增加类似 "error" 或 "exception" 等字段，用于工作流外围处理，如下面代码示例
# 4. 不要直接raise exception，会导致上下游输出混乱
# 5. 尽量做完整的参数检查，以避免程序异常报错
# 6. 目前内置的python库列表为：tenacity==8.5.0,threadpoolctl==3.5.0,tokenizers==0.19.1,tqdm==4.66.2,transformers==4.44.2,typing-inspect==0.9.0,typing_extensions==4.11.0,tzdata==2024.1,tzlocal==5.2,unstructured-client==0.24.1,urllib3==2.2.1,websockets==12.0,wrapt==1.17.0,xlrd==2.0.1,XlsxWriter==3.2.0,yagmail==0.15.293,yarl==1.9.11,yt-dlp==2024.4.9,zhipuai==2.1.5.20230904,zipp==3.21.0,ufile==3.2.9
import hashlib
import hmac
import base64
import json
import time
import requests
import datetime
import json
import pandas as pd
from lxml import html


class AIPPT():

    def __init__(self, APPId, APISecret, Text, templateId):
        self.APPid = APPId
        self.APISecret = APISecret
        self.text = Text
        self.header = {}
        self.templateId = templateId

    def get_signature(self, ts):
        try:
            auth = self.md5(self.APPid + str(ts))
            return self.hmac_sha1_encrypt(auth, self.APISecret)
        except Exception as e:
            print(e)
            return None

    def hmac_sha1_encrypt(self, encrypt_text, encrypt_key):
        return base64.b64encode(
            hmac.new(encrypt_key.encode('utf-8'), encrypt_text.encode('utf-8'), hashlib.sha1).digest()).decode('utf-8')

    def md5(self, text):
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def get_headers(self):
        timestamp = int(time.time())
        signature = self.get_signature(timestamp)

        headers = {
            "appId": self.APPid,
            "timestamp": str(timestamp),
            "signature": signature,
            "Content-Type": "application/json; charset=utf-8"
        }
        return headers

    def get_body(self, text):
        body = {
            "query": text,
            "templateId": self.templateId
        }
        return body

    def get_process(self, sid):
        if (None != sid):
            response = requests.request("GET", url=f"https://zwapi.xfyun.cn/api/ppt/v2/progress?sid={sid}",
                                        headers=self.get_headers()).text
            return response
        else:
            return None

    def get_result(self, task_id):
        PPTurl = ''
        while (True):
            response = self.get_process(task_id)
            resp = json.loads(response)
            pptStatus = resp['data']['pptStatus']
            aiImageStatus = resp['data']['aiImageStatus']
            cardNoteStatus = resp['data']['cardNoteStatus']
            if ('done' == pptStatus and 'done' == aiImageStatus and 'done' == cardNoteStatus):
                PPTurl = resp['data']['pptUrl']
                break
            else:
                time.sleep(3)
        return PPTurl

    def gen_outline(self):
        url = "https://zwapi.xfyun.cn/api/ppt/v2/createOutline"

        form_data = {
            'query': self.text,
            'language': 'cn',
            'search': 'false'
        }

        timestamp = int(time.time())
        signature = self.get_signature(timestamp)
        headers = {
            "appId": self.APPid,
            "timestamp": str(int(time.time())),
            "signature": signature,
        }

        response = requests.post(
            url,
            files={k: (None, v) for k, v in form_data.items()},
            headers=headers
        )
        return response.text

    def genPPT(self, outline):
        url = "https://zwapi.xfyun.cn/api/ppt/v2/createPptByOutline"
        body = {
            "query": self.text,
            "outline": outline,
            "templateId": self.templateId,
            "author": "谷志昊 杜锐君 王书海 靳杰 尹壮 于菲 石东晴 何继强 李本标 杨蕴琦",
            "isCardNote": False,
            "search": True,
            "isFigure": True,
            "aiImage": "normal",
            # ai配图类型： normal、advanced （isFigure为true的话生效）； normal-普通配图，20%正文配图；advanced-高级配图，50%正文配图
        }

        response = requests.post(url, json=body, headers=self.get_headers()).text
        resp = json.loads(response)
        if (0 == resp['code']):
            return resp['data']['sid']
        else:
            print('创建PPT任务失败')
            return None


class DeepSeek():
    def __init__(self, api_key, api_url):
        self.api_key = api_key
        self.api_url = api_url

    def call_deepseek(self, prompt):

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "Accept": "application/json"
        }

        payload = {
            "model": "deepseek-chat",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7
        }

        try:
            response = requests.post(self.api_url, headers=headers, json=payload)
            response.raise_for_status()

            json_response = response.json()

            result = {
                # "id": json_response.get("id"),
                "content": json_response["choices"][0]["message"]["content"],
                # "usage": json_response.get("usage"),
                # "created": json_response.get("created")
            }

            return json.dumps(result["content"], indent=2, ensure_ascii=False)
        except requests.exceptions.RequestException as e:
            return json.dumps({"error": str(e)}, indent=2)


def analysis_chemical_product_price(focus, url):
    today = datetime.datetime.now().strftime("%Y-%m-%d")

    response = requests.get(url)
    response.encoding = 'utf8'  # 关键修复：新浪财经使用 GBK 编码
    tree = html.fromstring(response.text)

    # 爬取图片
    img_urls = tree.xpath('/html/body/div[5]/div[1]/div[1]/div/img/@src')[0]
    f_img = f"![图片]({img_urls}) "
    # print(f_img)

    # 能源商品涨跌榜
    content = []
    len_li_flu = len(tree.xpath('/html/body/div[5]/div[1]/div[2]/div[2]/div[2]/ul/li'))
    for i in range(len_li_flu):
        brand_name = tree.xpath(f'/html/body/div[5]/div[1]/div[2]/div[2]/div[2]/ul/li[{i + 1}]/div[1]/a/text()')[0]
        price = tree.xpath(f'/html/body/div[5]/div[1]/div[2]/div[2]/div[2]/ul/li[{i + 1}]/div[2]/text()')[0]
        fluctuation = tree.xpath(f'/html/body/div[5]/div[1]/div[2]/div[2]/div[2]/ul/li[{i + 1}]/div[3]/text()')[0]
        content.append([brand_name, price, fluctuation])
    data = pd.DataFrame(content, columns=['商品名称', '价格', '涨跌幅'])
    mkform = data.to_markdown(index=False)

    prompt = "你是一名化工行业的数据分析师，根据：\n" + mkform + "\n的数据生成一段分析概要，字数要求200以内"
    price_analysis = DeepSeek("***********************************",
                              "https://api.deepseek.com/v1/chat/completions").call_deepseek(prompt)

    # 项目展示
    len_li_item = len(tree.xpath('/html/body/div[5]/div[2]/div[1]/ul/li'))
    items = []
    for i in range(len_li_item):
        date = tree.xpath(f'/html/body/div[5]/div[2]/div[1]/ul/li[1]/span/text()')[0].split()[-1]
        if date != today:
            break
        title = tree.xpath(f'/html/body/div[5]/div[2]/div[1]/ul/li[{i + 1}]/a/text()')[0]
        link = tree.xpath(f'/html/body/div[5]/div[2]/div[1]/ul/li[{i + 1}]/a/@href')[0]
        items.append([title, "https://news.chemnet.com" + link, date])
    new_data = pd.DataFrame(items, columns=['标题', '链接', '日期'])
    markdown_table = new_data.to_markdown(index=False)

    result = "## " + focus + "\n" + f_img + "\n" + mkform + "\n" + price_analysis + "\n"
    print("-----" + focus + "爬取完成")
    return result, result + markdown_table + "\n"


def process_data(pages):
    print(">>>>>>>>>> 1. 爬取网页信息并分析 <<<<<<<<<<<<")
    sim_content = "# 本日行业数据分析\n"
    com_content = "# 本日行业数据分析\n"

    for page in pages:
        sim, com = analysis_chemical_product_price(page['title'], page['url'])
        sim_content += sim
        com_content += com
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    print(">>>>>>>>>> 1. 生成Markdown文件 <<<<<<<<<<<<")
    with open(f"report_{today}.md", "w", encoding="utf-8") as f:
        f.write(com_content)

    query = f"请帮我写一份PPT： 你是一名化工行业分析师，需要编写每日简报，严格按照{sim_content}内容生成"
    genPPT = AIPPT("d624e016", "OTgyZjE5MjY4NTg4ZmU0YzI4NWVjMzYx", query, "20240718489569D")

    print(">>>>>>>>>> 2. 生成大纲 <<<<<<<<<<<<")
    outline = genPPT.gen_outline()
    print(outline)
    print(">>>>>>>>>> 3. 生成报告 <<<<<<<<<<<<")
    task_id = genPPT.genPPT(json.loads(outline)["data"]["outline"])
    print(task_id)
    url = genPPT.get_result(task_id)
    print(">>>>>>>>>> 4. 生成报告 <<<<<<<<<<<<")
    print(url)
    with open(f'report_{today}.pptx', 'wb') as f:
        f.write(requests.get(url).content)
    print(">>>>>>>>>> 5. 完成 <<<<<<<<<<<<")
    return url
    # return param1


def main(**kwargs):
    """
    函数描述: 处理输入参数并返回结果

    参数:
        param1 (type): 参数1描述
        param2 (type): 参数2描述

    返回:
        dict: 包含处理结果的字典
    """
    # 1. 输入参数验证
    try:
        param1 = kwargs.get('url1')
        param2 = kwargs.get('url2')
        param3 = kwargs.get('url3')
        param4 = kwargs.get('url4')
        param5 = kwargs.get('url5')
        param6 = kwargs.get('url6')
        if param1 is None:
            return {"error": "缺少必要参数: param1", "success": False}

        # 2. 主要处理逻辑
        pages = []
        for link in [param1, param2, param3, param4, param5, param6]:
            pages.append({"title": link.split('~')[0], "url": link.split('~')[1]})

        result = process_data(pages)

        # 3. 返回标准化结果
        return {
            "success": True,
            "data": result,
            "error": None
        }

    except Exception as e:
        # 4. 异常处理
        return {
            "success": False,
            "data": None,
            "error": f"处理过程出现错误: {str(e)}"
        }

if __name__ == '__main__':
    param1 = '能源~https://news.chemnet.com/list-11--1.html'
    param2 = '化工~https://news.chemnet.com/list-14--1.html'
    param3 = '橡塑~https://news.chemnet.com/list-15--1.html'
    param4 = '有色~https://news.chemnet.com/list-12--1.html'
    param5 = '农化~https://news.chemnet.com/list-13--1.html'
    param6 = '合纤~https://news.chemnet.com/list-16--1.html'
    pages = []
    for link in [param1, param2, param3, param4, param5, param6]:
        pages.append({"title": link.split('~')[0], "url": link.split('~')[1]})
    result = process_data(pages)

