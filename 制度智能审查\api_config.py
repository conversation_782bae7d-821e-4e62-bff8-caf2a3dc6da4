# api_config.py
import os
from typing import Dict, Any
import json
import logging

# 配置日志
logger = logging.getLogger(__name__)

class APIConfig:
    """API配置管理类 - 仅支持中石化API"""
    
    def __init__(self):
        # 默认配置 - 仅中石化API
        self.configs = {
            # 中石化AI平台配置
            "sinopec": {
                "api_key": os.getenv("SINOPEC_API_KEY", "b3f420628ad2429abd76069b4306e311"),
                "base_url": "https://agent.ai.sinopec.com/aicoapi/gateway/v2/workflow/api_run",
                "workflow_id": "b6e3a50794484497a9983743040ae349",  # 工作流ID
                "input_field": "test",  # 实际有效的字段名：test（与接口文档一致）
                "input_type": "input",    # 正确的类型：input
                "stream": True,  # 官方文档建议使用流式输出
                "temperature": 0.3,
                "enabled": True,
                "verify_ssl": False  # 禁用SSL验证避免证书问题
            }
        }
        
        # 加载配置文件
        self.load_config()
    
    def load_config(self):
        """从配置文件加载设置"""
        config_file = "api_settings.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    # 更新配置，保留默认值
                    for provider, settings in saved_config.items():
                        if provider in self.configs:
                            self.configs[provider].update(settings)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open("api_settings.json", 'w', encoding='utf-8') as f:
                json.dump(self.configs, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get_config(self, provider: str = "sinopec") -> Dict[str, Any]:
        """获取中石化API配置"""
        return self.configs.get("sinopec", {})
    
    def set_config(self, provider: str, key: str, value: Any):
        """设置配置项"""
        if provider == "sinopec":
            self.configs["sinopec"][key] = value
            self.save_config()
    
    def get_enabled_providers(self) -> list:
        """获取已启用的API提供商列表"""
        if self.configs["sinopec"].get("enabled", False):
            return ["sinopec"]
        return []
    
    def get_primary_provider(self) -> str:
        """获取主要的API提供商"""
        return "sinopec"
    
    def validate_config(self, provider: str = "sinopec") -> bool:
        """验证配置是否完整"""
        config = self.get_config("sinopec")
        return bool(config.get("api_key") and config.get("workflow_id"))

# 全局配置实例
api_config = APIConfig()

# 便捷函数
def get_api_config(provider='sinopec'):
    """获取API配置"""
    try:
        # 查找配置文件路径
        config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'api_settings.json')
        
        # 如果配置文件不存在，创建默认配置
        if not os.path.exists(config_file):
            default_config = {
                'sinopec': {
                    'api_key': '',
                    'base_url': 'https://agent.ai.sinopec.com/aicoapi/gateway/v2/workflow/api_run',
                    'workflow_id': 'b6e3a50794484497a9983743040ae349',
                    'input_field': 'test',  # 根据url.txt文件设置
                    'input_type': 'input',  # 根据url.txt文件设置
                    'verify_ssl': False,    # 禁用SSL验证
                    'enabled': False
                }
            }
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=4)
        
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 确保返回特定提供商的配置
        if provider in config:
            # 确保必要的字段存在，不存在则添加默认值
            if provider == 'sinopec':
                sinopec_config = config[provider]
                if 'input_field' not in sinopec_config:
                    sinopec_config['input_field'] = 'test'
                if 'input_type' not in sinopec_config:
                    sinopec_config['input_type'] = 'input'
                if 'verify_ssl' not in sinopec_config:
                    sinopec_config['verify_ssl'] = False
                return sinopec_config
            return config[provider]
        else:
            logger.warning(f"未找到提供商 {provider} 的配置，使用空配置")
            return {}
        
    except Exception as e:
        logger.error(f"读取API配置失败: {e}")
        return {}

def set_api_config(provider: str, **kwargs):
    """设置API配置"""
    for key, value in kwargs.items():
        api_config.set_config("sinopec", key, value)

def list_providers() -> Dict[str, bool]:
    """列出所有提供商及其状态"""
    config = api_config.get_config("sinopec")
    return {
        "sinopec": {
            "enabled": config.get("enabled", False),
            "valid": api_config.validate_config("sinopec")
        }
    } 