#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel生成器
"""

import os
import sys
import json
import logging
from excel_generator import create_excel_report

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test')

def test_excel_generator():
    """测试Excel生成器"""
    # 测试文本
    test_text = """### 审批流程环节（按顺序列出）  
1. **域长提出变更申请**  
   引用原文：  
   > （一）域长提出变更申请；  

2. **部门主管进行初审**  
   引用原文：  
   > （二）部门主管进行初审；  

3. **安全生产部进行合规性审核**  
   引用原文：  
   > （三）安全生产部进行合规性审核；  

4. **总经理办公会审批**  
   引用原文：  
   > （四）总经理办公会审批；  

5. **人力资源部备案**  
   引用原文：  
   > （五）人力资源部备案。  

---

### 部门职责节点表  

| 流程环节               | 负责部门       | 职责类型       |  
|------------------------|----------------|----------------|  
| 域长提出变更申请       | 域长           | 发起           |  
| 部门主管进行初审       | 部门主管       | 审核           |  
| 安全生产部合规性审核   | 安全生产部     | 审核           |  
| 总经理办公会审批       | 总经理办公会   | 确认（终审）   |  
| 人力资源部备案         | 人力资源部     | 备案           |  

---

### 说明：  
1. **职责类型分类依据**：  
   - **发起**：域长作为申请提出方，启动流程。  
   - **审核**：部门主管和安全生产部分别对申请内容进行初审和合规性检查。  
   - **确认（终审）**：总经理办公会拥有最终审批权，决定是否通过变更。  
   - **备案**：人力资源部不参与决策，仅完成流程归档。  

2. **流程完整性**：  
   原文明确列出了从申请到备案的完整闭环流程，未发现隐性节点或变体表述。"""
    
    try:
        # 创建测试数据
        process_data = {
            "data": {
                "文本呈现": test_text
            }
        }
        
        logger.info("成功创建测试数据")
        
        # 创建输出目录
        output_dir = 'downloads'
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成Excel报告
        output_path = os.path.join(output_dir, 'test_excel_report.xlsx')
        excel_path = create_excel_report(process_data, output_path)
        
        logger.info(f"Excel报告已生成: {excel_path}")
        return excel_path
    except Exception as e:
        logger.error(f"测试Excel生成器失败: {e}", exc_info=True)
        return None

if __name__ == "__main__":
    test_excel_generator() 