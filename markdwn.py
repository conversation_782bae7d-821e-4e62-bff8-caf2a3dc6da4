import openpyxl
import re
import os
import argparse
try:
    from docx import Document # For reading .docx files
except ImportError:
    print("警告：未安装 'python-docx' 库。无法处理 .docx 文件。")
    print("请运行: pip install python-docx")
    Document = None # Placeholder if not installed

def read_markdown_from_file(filepath):
    """
    从指定的文件路径读取内容。
    支持 .txt 和 .docx 文件。
    返回文件内容的字符串。
    """
    _, extension = os.path.splitext(filepath)
    extension = extension.lower()
    
    content = ""
    
    try:
        if extension == '.txt':
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
        elif extension == '.docx':
            if Document is None:
                print(f"错误：无法读取 '{filepath}'，因为 'python-docx' 库未成功导入。")
                return None
            doc = Document(filepath)
            full_text = []
            for para in doc.paragraphs:
                full_text.append(para.text)
            content = '\n'.join(full_text)
        else:
            print(f"错误：不支持的文件类型 '{extension}'。请提供 .txt 或 .docx 文件。")
            return None
        return content
    except FileNotFoundError:
        print(f"错误：文件 '{filepath}' 未找到。")
        return None
    except Exception as e:
        print(f"读取文件 '{filepath}' 时发生错误: {e}")
        return None

def parse_markdown_table(md_text):
    """
    解析 Markdown 文本中的第一个表格。
    返回 (headers, data_rows)元组。
    headers 是一个列表，包含表头字符串。
    data_rows 是一个列表的列表，每个子列表代表一行数据。
    如果找不到有效的表格，则返回 (None, None)。
    """
    if not md_text:
        return None, None
        
    lines = md_text.strip().split('\n')
    
    table_lines = []
    in_table = False
    
    # 简单地查找表格结构： |...| \n |---| \n |...|
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            if in_table: # 空行可能表示表格结束
                break
            continue

        # 检查是否是 Markdown 表格行
        if line.startswith('|') and line.endswith('|'):
            if not in_table:
                # 检查下一行是否是分隔符行
                if i + 1 < len(lines):
                    next_line = lines[i+1].strip()
                    # 分隔符行也必须以 | 开始和结束，并且主要由 - : | 组成
                    if next_line.startswith('|') and next_line.endswith('|') and \
                       all(c in '|-:' for c in next_line[1:-1].replace(" ", "")):
                        in_table = True
                        table_lines.append(line) # 添加表头行
                        table_lines.append(next_line) # 添加分隔符行
                else: # 表格不完整
                    if in_table: break 
            elif in_table:
                table_lines.append(line)
        elif in_table: # 如果在表格内，但当前行不是表格行，则认为表格结束
            break
            
    if not table_lines or len(table_lines) < 2:
        # print("未找到有效的 Markdown 表格头部或分隔符。") # 可以取消注释以获取更详细的日志
        return None, None

    # 提取表头
    header_line = table_lines[0]
    headers = [header.strip() for header in header_line.split('|')[1:-1] if header.strip()] 
    # 修正: 确保分割后的元素不为空

    # 提取数据行 (跳过分隔符行 table_lines[1])
    data_rows = []
    for row_line in table_lines[2:]:
        cells = [cell.strip() for cell in row_line.split('|')[1:-1]]
        # 确保数据行和表头列数一致 (可选，但推荐)
        if len(cells) == len(headers):
            data_rows.append(cells)
        elif len(cells) > len(headers): # 如果数据列多于表头，截断
            data_rows.append(cells[:len(headers)])
            # print(f"警告: 数据行 '{row_line}' 的列数多于表头，已截断。")
        else: # 如果数据列少于表头，用空字符串填充
            data_rows.append(cells + [''] * (len(headers) - len(cells)))
            # print(f"警告: 数据行 '{row_line}' 的列数少于表头，已用空值填充。")


    if not headers:
        # print("未成功解析表头。") # 可以取消注释
        return None, None
        
    return headers, data_rows

def markdown_to_excel(md_text, excel_filename="markdown_output.xlsx", sheet_name="Sheet1"):
    """
    将 Markdown 文本中的表格转换为 Excel 文件。
    目前只处理 Markdown 文本中的第一个有效表格。
    """
    headers, data_rows = parse_markdown_table(md_text)

    if not headers:
        print("无法从 Markdown 文本中解析出表格数据。")
        return False

    # 创建一个新的 Excel 工作簿和工作表
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    sheet.title = sheet_name

    # 写入表头
    for col_idx, header_text in enumerate(headers, 1):
        sheet.cell(row=1, column=col_idx, value=header_text)

    # 写入数据行
    for row_idx, data_row in enumerate(data_rows, 2): # 数据从第二行开始
        for col_idx, cell_value in enumerate(data_row, 1):
            # 尝试将值转换为数字，如果可能的话
            try:
                # 先判断是否包含小数点，再尝试转换为浮点数
                if '.' in cell_value and cell_value.replace('.', '', 1).isdigit():
                    cell_value = float(cell_value)
                # 否则，如果全是数字，尝试转换为整数
                elif cell_value.isdigit():
                    cell_value = int(cell_value)
                # 如果是否定数开头的数字
                elif cell_value.startswith('-') and cell_value[1:].isdigit():
                     cell_value = int(cell_value)
                elif cell_value.startswith('-') and '.' in cell_value[1:] and cell_value[1:].replace('.', '', 1).isdigit():
                     cell_value = float(cell_value)

            except ValueError:
                pass #保持为字符串
            sheet.cell(row=row_idx, column=col_idx, value=cell_value)
            
    # 保存 Excel 文件
    try:
        workbook.save(excel_filename)
        print(f"成功将 Markdown 表格转换为 Excel 文件: '{excel_filename}'")
        return True
    except Exception as e:
        print(f"保存 Excel 文件失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="将 Markdown 文件中的表格转换为 Excel 文件。")
    parser.add_argument("input_file", help="包含 Markdown 表格的输入文件路径 (.txt 或 .docx)")
    parser.add_argument("-o", "--output", help="输出的 Excel 文件名 (.xlsx)。如果未提供，则基于输入文件名生成。")
    parser.add_argument("-s", "--sheet", default="Sheet1", help="Excel 工作表的名称 (默认为 'Sheet1')")

    args = parser.parse_args()

    input_filepath = args.input_file
    
    markdown_content = read_markdown_from_file(input_filepath)
    
    if markdown_content is None:
        return # 错误信息已在 read_markdown_from_file 中打印

    if not markdown_content.strip():
        print(f"文件 '{input_filepath}' 为空或只包含空白字符。")
        return

    if args.output:
        output_excel_file = args.output
        # 确保输出文件名是 .xlsx
        if not output_excel_file.lower().endswith('.xlsx'):
            output_excel_file += '.xlsx'
    else:
        # 基于输入文件名生成输出文件名
        base, _ = os.path.splitext(input_filepath)
        output_excel_file = base + ".xlsx"

    markdown_to_excel(markdown_content, output_excel_file, args.sheet)

# --- 主程序 ---
if __name__ == "__main__":
    main()
