# 基础库
openai>=1.0.0
jieba>=0.42.1
requests>=2.28.0

# Office文档处理
python-pptx>=0.6.21
python-docx>=0.8.11
openpyxl>=3.0.10

# PDF处理
PyPDF2>=3.0.1
pdfplumber>=0.9.0
pymupdf>=1.23.0

# 文本处理
chardet>=5.0.0
beautifulsoup4>=4.11.0

# Web框架
Flask>=2.3.0
Werkzeug>=2.3.0

# 数据处理
pandas>=1.5.0
numpy>=1.21.0
Pillow>=9.0.0

# 正则表达式增强
regex>=2022.0.0

# 日志和调试
colorama>=0.4.6

# 可选：AI模型支持
transformers>=4.21.0
torch>=1.12.0

Flask==2.3.3
Werkzeug==2.3.7
requests==2.31.0
python-docx==0.8.11
PyPDF2==3.0.1
openpyxl==3.1.2
python-pptx==0.6.21
Pillow==10.0.1
jieba==0.42.1
wordcloud==1.9.2
matplotlib==3.7.2
numpy==1.24.3
beautifulsoup4==4.12.2
lxml==4.9.3
chardet==5.2.0
urllib3==2.0.4
certifi==2023.7.22
idna==3.4
charset-normalizer==3.2.0
click==8.1.7
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.3
blinker==1.6.2
importlib-metadata==6.8.0
zipp==3.16.2
six==1.16.0
et-xmlfile==1.1.0
defusedxml==0.7.1
python-dateutil==2.8.2
pyparsing==2.4.7
kiwisolver==1.4.5
fonttools==4.42.1
cycler==0.11.0
contourpy==1.1.0
packaging==23.1
pandas==2.1.1
pytz==2023.3 