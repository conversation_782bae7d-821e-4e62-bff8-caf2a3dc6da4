import hashlib
import hmac
import base64
import json
import time
import requests
import datetime

import scrape
# from apscheduler.schedulers.blocking import BlockingScheduler

class AIPPT():

    def __init__(self, APPId, APISecret, Text, templateId):
        self.APPid = APPId
        self.APISecret = APISecret
        self.text = Text
        self.header = {}
        self.templateId = templateId

    def get_signature(self, ts):
        try:
            auth = self.md5(self.APPid + str(ts))
            return self.hmac_sha1_encrypt(auth,self.APISecret)
        except Exception as e:
            print(e)
            return None

    def hmac_sha1_encrypt(self, encrypt_text, encrypt_key):
        return base64.b64encode(hmac.new(encrypt_key.encode('utf-8'), encrypt_text.encode('utf-8'), hashlib.sha1).digest()).decode('utf-8')

    def md5(self, text):
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def get_headers(self):
        timestamp = int(time.time())
        signature = self.get_signature(timestamp)

        headers = {
            "appId": self.APPid,
            "timestamp": str(timestamp),
            "signature": signature,
            "Content-Type": "application/json; charset=utf-8"
        }
        return headers
    
    def get_body(self,text):
        body = {
            "query":text,
            "templateId":self.templateId
        }
        return body

    def get_process(self,sid):
        if(None != sid):
            response = requests.request("GET",url=f"https://zwapi.xfyun.cn/api/ppt/v2/progress?sid={sid}",headers=self.get_headers()).text
            return response
        else:
            return None

    def get_result(self,task_id):
        PPTurl = ''
        while(True):
            response = self.get_process(task_id)
            resp = json.loads(response)
            pptStatus = resp['data']['pptStatus']
            aiImageStatus = resp['data']['aiImageStatus']
            cardNoteStatus = resp['data']['cardNoteStatus']
            if('done' == pptStatus and 'done' == aiImageStatus and 'done' == cardNoteStatus):
                PPTurl = resp['data']['pptUrl']
                break
            else:
                time.sleep(3)
        return PPTurl

    def gen_outline(self):
        url ="https://zwapi.xfyun.cn/api/ppt/v2/createOutline"
        
        form_data = {
            'query': self.text,
            'language': 'cn',
            'search': 'false'
        }

        timestamp = int(time.time())
        signature = self.get_signature(timestamp)
        headers = {
            "appId": self.APPid,
            "timestamp": str(int(time.time())),
            "signature": signature,
        }

        response = requests.post(
            url,
            files={k: (None, v) for k, v in form_data.items()},
            headers=headers
        )
        return response.text

    def genPPT(self,outline):
        url = "https://zwapi.xfyun.cn/api/ppt/v2/createPptByOutline"
        body = {
                "query" : self.text,
                "outline" : outline,
                "templateId" : self.templateId,
                "author" : "谷志昊 杜锐君 王书海 靳杰 尹壮 于菲 石东晴 何继强 李本标 杨蕴琦",
                "isCardNote" : False,
                "search" : True,
                "isFigure" : True,
                "aiImage" : "normal",   # ai配图类型： normal、advanced （isFigure为true的话生效）； normal-普通配图，20%正文配图；advanced-高级配图，50%正文配图
            }

        response = requests.post(url,json=body,headers=self.get_headers()).text
        resp = json.loads(response)
        if (0 == resp['code']):
            return resp['data']['sid']
        else:
            print('创建PPT任务失败')
            return None

def process_data():

    print(">>>>>>>>>> 1. 爬取网页信息并分析 <<<<<<<<<<<<")
    sim_content = "# 本日行业数据分析\n"
    com_content = "# 本日行业数据分析\n"
    pages = [
        {
            "title": "能源", 
            "url": "https://news.chemnet.com/list-11--1.html", 
        },
        {
            "title": "化工", 
            "url": "https://news.chemnet.com/list-14--1.html", 
        },
        {
            "title": "橡塑", 
            "url": "https://news.chemnet.com/list-15--1.html", 
        },
        {
            "title": "有色", 
            "url": "https://news.chemnet.com/list-12--1.html", 
        },
        {
            "title": "农化", 
            "url": "https://news.chemnet.com/list-13--1.html", 
        },
        {
            "title": "合纤", 
            "url": "https://news.chemnet.com/list-16--1.html", 
        }
    ]
    for page in pages:
        sim, com = scrape.analysis_chemical_product_price(page['title'], page['url'])
        sim_content += sim
        com_content += com
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    print(">>>>>>>>>> 1. 生成Markdown文件 <<<<<<<<<<<<")
    with open(f"report_{today}.md", "w", encoding="utf-8") as f:
        f.write(com_content)
    
    query = f"请帮我写一份PPT： 你是一名化工行业分析师，需要编写每日简报，严格按照{sim_content}内容生成" 
    genPPT = AIPPT("d624e016", "OTgyZjE5MjY4NTg4ZmU0YzI4NWVjMzYx", query, "20240718489569D")

    print(">>>>>>>>>> 2. 生成大纲 <<<<<<<<<<<<")
    outline = genPPT.gen_outline()
    print(outline)
    print(">>>>>>>>>> 3. 生成报告 <<<<<<<<<<<<")
    task_id = genPPT.genPPT(json.loads(outline)["data"]["outline"])
    print(task_id)
    url = genPPT.get_result(task_id)
    print(">>>>>>>>>> 4. 生成报告 <<<<<<<<<<<<")
    print(url)
    with open(f'report_{today}.pptx', 'wb') as f:
        f.write(requests.get(url).content)
    print(">>>>>>>>>> 5. 完成 <<<<<<<<<<<<")
    return url

def main(**kwargs):
    """
    函数描述: 处理输入参数并返回结果

    参数:
        param1 (type): 参数1描述
        param2 (type): 参数2描述

    返回:
        dict: 包含处理结果的字典
    """
    # 1. 输入参数验证
    try:
        param1 = kwargs.get('aico')
        if param1 is None:
            return {"error": "缺少必要参数: param1", "success": False}

        # 2. 主要处理逻辑
        result = process_data(param1)

        # 3. 返回标准化结果
        return {
            "success": True,
            "data": result,
            "error": None
        }

    except Exception as e:
        # 4. 异常处理
        return {
            "success": False,
            "data": None,
            "error": f"处理过程出现错误: {str(e)}"
        }

if __name__ == '__main__':
    result = process_data()
    # sched = BlockingScheduler()
    # sched.add_job(process_data(), 'cron', hour=7, minute=30)
    # try:
    #     sched.start()
    # except KeyboardInterrupt:
    #     sched.shutdown()