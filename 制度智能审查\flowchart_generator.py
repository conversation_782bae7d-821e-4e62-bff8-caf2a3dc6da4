from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
from pptx.enum.text import PP_ALIGN
import os

class FlowchartGenerator:
    def __init__(self):
        self.slide_width = Inches(10)
        self.slide_height = Inches(7.5)
        self.shape_width = Inches(2)
        self.shape_height = Inches(1)
        self.vertical_spacing = Inches(1.5)
        self.horizontal_spacing = Inches(3)
    
    def create_flowchart(self, process_data, output_path):
        """创建流程图"""
        print("开始生成流程图...")
        
        # 创建演示文稿
        prs = Presentation()
        slide = prs.slides.add_slide(prs.slide_layouts[6])  # 空白布局
        
        # 添加标题
        self.add_title(slide, process_data.get('process_name', '审批流程'))
        
        # 绘制流程步骤
        shapes = self.draw_process_steps(slide, process_data['steps'])
        
        # 连接流程步骤
        self.connect_steps(slide, shapes)
        
        # 保存文件
        prs.save(output_path)
        print(f"流程图已保存到：{output_path}")
    
    def add_title(self, slide, title):
        """添加标题"""
        title_shape = slide.shapes.add_textbox(
            Inches(1), Inches(0.5),
            Inches(8), Inches(0.8)
        )
        title_frame = title_shape.text_frame
        title_frame.text = title
        
        # 设置标题样式
        paragraph = title_frame.paragraphs[0]
        paragraph.font.size = Pt(24)
        paragraph.font.bold = True
        paragraph.alignment = PP_ALIGN.CENTER
    
    def draw_process_steps(self, slide, steps):
        """绘制流程步骤"""
        shapes = []
        start_x = Inches(1)
        start_y = Inches(1.5)
        
        for i, step in enumerate(steps):
            # 计算位置
            x = start_x
            y = start_y + i * self.vertical_spacing
            
            # 确定形状类型
            if i == 0:
                shape_type = MSO_SHAPE.OVAL  # 开始节点
                fill_color = RGBColor(144, 238, 144)  # 浅绿色
            elif i == len(steps) - 1:
                shape_type = MSO_SHAPE.OVAL  # 结束节点
                fill_color = RGBColor(255, 182, 193)  # 浅粉色
            else:
                shape_type = MSO_SHAPE.RECTANGLE  # 处理节点
                fill_color = RGBColor(173, 216, 230)  # 浅蓝色
            
            # 创建形状
            shape = slide.shapes.add_shape(
                shape_type, x, y,
                self.shape_width, self.shape_height
            )
            
            # 设置样式
            self.set_shape_style(shape, fill_color)
            
            # 添加文本
            self.add_shape_text(shape, step)
            
            shapes.append(shape)
        
        return shapes
    
    def set_shape_style(self, shape, fill_color):
        """设置形状样式"""
        # 填充颜色
        shape.fill.solid()
        shape.fill.fore_color.rgb = fill_color
        
        # 边框
        shape.line.color.rgb = RGBColor(0, 0, 0)
        shape.line.width = Pt(1)
    
    def add_shape_text(self, shape, step):
        """为形状添加文本"""
        text_frame = shape.text_frame
        text_frame.clear()
        
        # 主要描述
        p = text_frame.paragraphs[0]
        p.text = f"步骤{step['step_number']}"
        p.font.size = Pt(12)
        p.font.bold = True
        p.alignment = PP_ALIGN.CENTER
        
        # 审批人信息
        if step['approver'] != "未指定":
            p2 = text_frame.add_paragraph()
            p2.text = step['approver']
            p2.font.size = Pt(10)
            p2.alignment = PP_ALIGN.CENTER
        
        # 动作信息
        p3 = text_frame.add_paragraph()
        p3.text = step['action']
        p3.font.size = Pt(10)
        p3.alignment = PP_ALIGN.CENTER
        
        # 设置文本框属性
        text_frame.margin_left = Inches(0.1)
        text_frame.margin_right = Inches(0.1)
        text_frame.margin_top = Inches(0.1)
        text_frame.margin_bottom = Inches(0.1)
        text_frame.word_wrap = True
    
    def connect_steps(self, slide, shapes):
        """连接流程步骤"""
        for i in range(len(shapes) - 1):
            current_shape = shapes[i]
            next_shape = shapes[i + 1]
            
            # 计算连接点
            start_x = current_shape.left + current_shape.width // 2
            start_y = current_shape.top + current_shape.height
            end_x = next_shape.left + next_shape.width // 2
            end_y = next_shape.top
            
            # 创建连接线
            connector = slide.shapes.add_connector(
                1,  # 直线连接器
                start_x, start_y,
                end_x, end_y
            )
            
            # 设置连接线样式
            connector.line.color.rgb = RGBColor(0, 0, 0)
            connector.line.width = Pt(2)
    
    def create_detailed_flowchart(self, process_data, output_path):
        """创建详细的流程图（包含更多信息）"""
        print("开始生成详细流程图...")
        
        prs = Presentation()
        slide = prs.slides.add_slide(prs.slide_layouts[6])
        
        # 添加标题
        self.add_title(slide, process_data.get('process_name', '详细审批流程'))
        
        # 创建详细的步骤描述
        self.create_detailed_steps(slide, process_data['steps'])
        
        prs.save(output_path)
        print(f"详细流程图已保存到：{output_path}")
    
    def create_detailed_steps(self, slide, steps):
        """创建详细的步骤描述"""
        start_x = Inches(0.5)
        start_y = Inches(1.5)
        
        for i, step in enumerate(steps):
            y = start_y + i * Inches(1.2)
            
            # 步骤框
            step_box = slide.shapes.add_shape(
                MSO_SHAPE.RECTANGLE,
                start_x, y,
                Inches(9), Inches(1)
            )
            
            # 设置样式
            step_box.fill.solid()
            step_box.fill.fore_color.rgb = RGBColor(240, 248, 255)
            step_box.line.color.rgb = RGBColor(70, 130, 180)
            step_box.line.width = Pt(2)
            
            # 添加详细文本
            text_frame = step_box.text_frame
            text_frame.clear()
            
            # 步骤标题
            p1 = text_frame.paragraphs[0]
            p1.text = f"步骤 {step['step_number']}: {step['action']}"
            p1.font.size = Pt(14)
            p1.font.bold = True
            
            # 审批人
            p2 = text_frame.add_paragraph()
            p2.text = f"审批人: {step['approver']}"
            p2.font.size = Pt(11)
            
            # 描述
            p3 = text_frame.add_paragraph()
            p3.text = f"描述: {step['description'][:50]}..."
            p3.font.size = Pt(10)

# 测试函数
def test_generator():
    # 测试数据
    test_data = {
        'process_name': '报销审批流程',
        'steps': [
            {
                'step_number': 1,
                'description': '员工填写报销单',
                'approver': '申请人',
                'action': '申请'
            },
            {
                'step_number': 2,
                'description': '直接主管审核费用合理性',
                'approver': '直接主管',
                'action': '审核'
            },
            {
                'step_number': 3,
                'description': '财务部门检查单据完整性',
                'approver': '财务部门',
                'action': '审批'
            },
            {
                'step_number': 4,
                'description': '总经理最终批准',
                'approver': '总经理',
                'action': '批准'
            }
        ]
    }
    
    generator = FlowchartGenerator()
    generator.create_flowchart(test_data, "test_flowchart.pptx")
    generator.create_detailed_flowchart(test_data, "test_detailed_flowchart.pptx")

if __name__ == "__main__":
    test_generator()
