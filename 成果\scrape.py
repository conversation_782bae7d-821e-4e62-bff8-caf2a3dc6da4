import requests
import datetime
import pandas as pd
import re
from tabulate import tabulate
import json

from bs4 import BeautifulSoup
import polars as pl

class DeepSeek():
    def __init__(self, api_key, api_url):
        self.api_key = api_key
        self.api_url = api_url

    def call_deepseek(self, prompt):

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "Accept": "application/json"
        }
        
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7
        }
    
        try:
            response = requests.post(self.api_url, headers=headers, json=payload)
            response.raise_for_status()
            
            json_response = response.json()
            
            result = {
                # "id": json_response.get("id"),
                "content": json_response["choices"][0]["message"]["content"],
                # "usage": json_response.get("usage"),
                # "created": json_response.get("created")
            }
            
            return json.dumps(result["content"], indent=2, ensure_ascii=False)
        except requests.exceptions.RequestException as e:
            return json.dumps({"error": str(e)}, indent=2)

def analysis_chemical_product_price(focus, url):
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status() 
    except requests.exceptions.RequestException as e:
        return pd.DataFrame()
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # 爬取图片
    img_urls = [img['src'] for img in soup.find_all('img') if 'src' in img.attrs][-1]
    f_img = f"![图片]({img_urls}) "
    
    # 爬取价格
    blank2_elements = soup.find_all(class_="blank2")
    if not blank2_elements:
        return pd.DataFrame()
    text =""
    for element in blank2_elements:
        content = element.get_text(strip=True)
        if content:
            text = text + content
    
    text = text.replace('商品名称价格日涨跌幅','')

    # 使用正则表达式匹配商品名称、价格和涨跌幅
    pattern = r'(.+?)(\d+\.?\d*)([+-]?\d+\.?\d+%)'
    matches = re.findall(pattern, text)

    # 创建DataFrame
    df = pd.DataFrame(matches, columns=['商品名称', '价格', '涨跌幅'])
    mkform = tabulate(df, headers='keys', tablefmt='github')

    prompt = "你是一名化工行业的数据分析师，根据：\n" + mkform + "\n的数据生成一段分析概要，字数要求200以内"
    price_analysis = DeepSeek("***********************************", "https://api.deepseek.com/v1/chat/completions").call_deepseek(prompt)

    # 爬取链接
    articles = []
    for li in soup.find('div', class_='content-list').select('ul li'):
        a_tag = li.find('a', target="_blank")
        title = a_tag.get_text(strip=True) if a_tag else "无标题"
        link = "https://news.chemnet.com" + a_tag['href'] if a_tag and 'href' in a_tag.attrs else "#"

        span_tag = li.find('span')
        date_text = span_tag.get_text(strip=True) if span_tag else ""
        date = date_text.split()[-1] if date_text else datetime.now().strftime("%Y-%m-%d")
        
        if date != today:
                continue

        if not re.match(r'\d{4}-\d{2}-\d{2}', date):
            date = datetime.now().strftime("%Y-%m-%d")
        
        articles.append({
            "标题": title,
            "链接": link,
            "日期": date
        })
    markdown_table= ""
    if articles:
        markdown_table = "| 标题 | 链接 | 日期 |\n"
        markdown_table += "|------|------|------|\n"
        for item in articles:
            markdown_table += f"| {item['标题']} | [{item['链接']}]({item['链接']}) | {item['日期']} |\n"
    result = "## "+ focus + "\n" + f_img + "\n" + mkform + "\n" + price_analysis + "\n"
    print("-----" + focus + "爬取完成")
    return result, result + markdown_table + "\n"