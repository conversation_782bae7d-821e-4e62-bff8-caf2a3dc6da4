{% extends "base.html" %}

{% block title %}处理结果 - 制度智能审查系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        处理完成
                    </h4>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home me-1"></i>返回首页
                    </a>
                </div>
                                    <p class="mb-0 mt-2">制度审查已成功完成并生成流程图</p>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-thumbs-up me-2"></i>
                    文件已成功处理，您可以下载生成的流程图文件。
                </div>
                
                <!-- LLM分析信息 -->
                {% if result and result.llm_info %}
                <div class="alert alert-info">
                    <i class="fas fa-robot me-2"></i>
                    <strong>AI分析信息：</strong>
                    使用 {{ result.llm_info.provider }} 模型进行分析，
                    耗时 {{ result.llm_info.processing_time }}，
                    响应长度 {{ result.llm_info.response_length }} 字符
                </div>
                {% endif %}
                
                <!-- 下载链接 -->
                {% if download_links %}
                <div class="mb-4">
                    <h5><i class="fas fa-download me-2"></i>下载文件</h5>
                    <div class="row justify-content-center">
                        {% for link in download_links %}
                        <div class="col-md-5 col-lg-4 mb-3">
                            <div class="file-card">
                                <div class="file-header">
                                    <h6 class="mb-1">
                                        <i class="fas fa-{{ 'file-excel' if link.file_type == 'excel' else 'file-powerpoint' if link.file_type == 'pptx' else 'file' }} me-2"></i>
                                        {{ link.format }}
                                    </h6>
                                    <small class="text-muted">{{ link.filename }}</small>
                                </div>
                                <div class="file-actions">
                                    <a href="{{ link.url }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-download me-1"></i>
                                        下载
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- API原始响应展示 -->
{% if result and result.show_raw_response and result.raw_response_data %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-code me-2"></i>
                    API原始响应数据
                </h5>
                <p class="mb-0 mt-2">查看AI提供商返回的原始数据</p>
            </div>
            <div class="card-body">
                <!-- 响应信息概览 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-primary">{{ result.raw_response_data.provider }}</h6>
                            <small class="text-muted">AI提供商</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-success">{{ result.raw_response_data.processing_time }}</h6>
                            <small class="text-muted">处理耗时</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-warning">{{ result.raw_response_data.content_length }}</h6>
                            <small class="text-muted">{{ result.raw_response_data.content_type or '内容长度' }}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-info">{{ result.raw_response_data.response_length }}</h6>
                            <small class="text-muted">响应长度</small>
                        </div>
                    </div>
                </div>
                
                <div class="accordion" id="rawResponseAccordion">
                    <!-- 用户原始文档展示 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="rawDocumentHeader">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#rawDocumentCollapse">
                                <i class="fas fa-file-text me-2"></i>
                                用户上传的原始文档
                                <span class="badge bg-secondary ms-2">{{ result.raw_response_data.content_length }} 字符</span>
                            </button>
                        </h2>
                        <div id="rawDocumentCollapse" class="accordion-collapse collapse" data-bs-parent="#rawResponseAccordion">
                            <div class="accordion-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        分析时间: {{ result.raw_response_data.analysis_timestamp }}
                                    </small>
                                    <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('rawDocumentContent')">
                                        <i class="fas fa-copy me-1"></i>复制
                                    </button>
                                </div>
                                <pre id="rawDocumentContent" class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-size: 0.85em;">{{ result.raw_response_data.content }}</pre>
                                <div class="alert alert-success mt-3 mb-0">
                                    <i class="fas fa-info-circle me-2"></i>这是您上传的原始文档内容
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 发送给AI的完整内容展示 -->
                    {% if result.raw_response_data.sent_to_ai %}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="sentToAiHeader">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sentToAiCollapse">
                                <i class="fas fa-paper-plane me-2"></i>
                                发送给AI的完整内容（含指令）
                                <span class="badge bg-warning ms-2">{{ result.raw_response_data.sent_to_ai|length }} 字符</span>
                            </button>
                        </h2>
                        <div id="sentToAiCollapse" class="accordion-collapse collapse" data-bs-parent="#rawResponseAccordion">
                            <div class="accordion-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-robot me-1"></i>
                                        包含分析指令和用户文档
                                    </small>
                                    <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('sentToAiContent')">
                                        <i class="fas fa-copy me-1"></i>复制
                                    </button>
                                </div>
                                <pre id="sentToAiContent" class="bg-warning bg-opacity-10 p-3 rounded" style="max-height: 500px; overflow-y: auto; font-size: 0.85em;">{{ result.raw_response_data.sent_to_ai }}</pre>
                                <div class="alert alert-warning mt-3 mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>这是实际发送给AI的内容，包含分析指令和您的文档
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 原始响应展示 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="rawResponseHeader">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#rawResponseCollapse">
                                <i class="fas fa-reply me-2"></i>
                                API原始响应
                                <span class="badge bg-secondary ms-2">{{ result.raw_response_data.response_length }} 字符</span>
                            </button>
                        </h2>
                        <div id="rawResponseCollapse" class="accordion-collapse collapse" data-bs-parent="#rawResponseAccordion">
                            <div class="accordion-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-robot me-1"></i>
                                        来源: {{ result.raw_response_data.provider }} API
                                    </small>
                                    <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('rawResponseContent')">
                                        <i class="fas fa-copy me-1"></i>复制
                                    </button>
                                </div>
                                <pre id="rawResponseContent" class="bg-dark text-light p-3 rounded" style="max-height: 500px; overflow-y: auto; font-size: 0.85em;">{{ result.raw_response_data.raw_response }}</pre>
                                {% if "示例分析" in result.raw_response_data.raw_response or "假设制度文本" in result.raw_response_data.raw_response %}
                                <div class="alert alert-warning mt-3 mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>注意：</strong>AI返回的是示例/模板内容，而非基于您实际文档的分析。这可能是由于AI工作流配置导致的。
                                    建议联系技术支持优化AI模型配置。
                                </div>
                                {% else %}
                                <div class="alert alert-success mt-3 mb-0">
                                    <i class="fas fa-check-circle me-2"></i>AI基于您的实际文档内容进行了分析
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- LLM分析过程展示 -->
{% if result and result.llm_analysis and not result.show_raw_response %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-brain me-2"></i>
                    AI分析过程
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="analysisAccordion">
                    <!-- 文档内容展示 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="documentHeader">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#documentCollapse">
                                <i class="fas fa-file-text me-2"></i>
                                使用的{{ result.llm_analysis.content_type or '文档内容' }}
                            </button>
                        </h2>
                        <div id="documentCollapse" class="accordion-collapse collapse" data-bs-parent="#analysisAccordion">
                            <div class="accordion-body">
                                <pre class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto; font-size: 0.9em;">{{ result.llm_analysis.content }}</pre>
                                {% if result.llm_analysis.note %}
                                <div class="alert alert-info mt-3 mb-0">
                                    <i class="fas fa-info-circle me-2"></i>{{ result.llm_analysis.note }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- 原始响应展示 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="responseHeader">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#responseCollapse">
                                <i class="fas fa-reply me-2"></i>
                                AI原始响应
                            </button>
                        </h2>
                        <div id="responseCollapse" class="accordion-collapse collapse" data-bs-parent="#analysisAccordion">
                            <div class="accordion-body">
                                <pre class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-size: 0.9em;">{{ result.llm_analysis.raw_response }}</pre>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 解析过程展示 -->
                    {% if result.llm_analysis.parsing_steps %}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="parsingHeader">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#parsingCollapse">
                                <i class="fas fa-cogs me-2"></i>
                                解析过程
                            </button>
                        </h2>
                        <div id="parsingCollapse" class="accordion-collapse collapse" data-bs-parent="#analysisAccordion">
                            <div class="accordion-body">
                                {% for step in result.llm_analysis.parsing_steps %}
                                <div class="mb-2">
                                    <span class="badge bg-{{ 'success' if step.status == 'success' else 'warning' if step.status == 'warning' else 'danger' }}">
                                        {{ step.step }}
                                    </span>
                                    <span class="ms-2">{{ step.message }}</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 流程解析结果 -->
{% if result and result.process_data %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-project-diagram me-2"></i>
                    流程解析结果
                </h5>
            </div>
            <div class="card-body">
                <h6 class="text-primary">{{ result.process_data.process_name }}</h6>
                
                <!-- 流程步骤 -->
                {% if result.process_data.steps %}
                <div class="mt-3">
                    <h6><i class="fas fa-list-ol me-2"></i>审批步骤</h6>
                    {% for step in result.process_data.steps %}
                    <div class="process-step mb-3 p-3 border-start border-primary border-3 bg-light rounded-end">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>步骤 {{ step.step_number }}:</strong> {{ step.description }}
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>审批人：{{ step.department or step.approver or '未指定' }}
                                    {% if step.time_limit %}
                                    | <i class="fas fa-clock me-1"></i>时限：{{ step.time_limit }}
                                    {% endif %}
                                </small>
                            </div>
                            <span class="badge bg-primary">{{ step.action }}</span>
                        </div>
                        
                        {% if step.conditions %}
                        <div class="mt-2">
                            <small class="text-info">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                条件：{{ step.conditions|join(', ') }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                <!-- 条件分支 -->
                {% if result.process_data.conditions %}
                <div class="mt-4">
                    <h6><i class="fas fa-code-branch me-2"></i>条件分支</h6>
                    {% for condition in result.process_data.conditions %}
                    <div class="alert alert-info">
                        <strong>条件：</strong>{{ condition.condition }}
                        <br>
                        <strong>动作：</strong>{{ condition.action }}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    流程统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ result.process_data.total_steps }}</h4>
                            <small class="text-muted">总步骤数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ result.process_data.conditions|length }}</h4>
                        <small class="text-muted">条件分支</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <span class="badge bg-{{ 'success' if result.process_data.complexity == '简单' else 'warning' if result.process_data.complexity == '中等' else 'danger' }} fs-6">
                        复杂度：{{ result.process_data.complexity }}
                    </span>
                </div>
                
                {% if result.process_data.has_branches %}
                <div class="mt-3 text-center">
                    <span class="badge bg-info">
                        <i class="fas fa-code-branch me-1"></i>包含分支流程
                    </span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- 文件信息 -->
        {% if result.file_info %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    文件信息
                </h6>
            </div>
            <div class="card-body">
                <small>
                    <strong>文件类型：</strong>{{ result.file_info.file_type }}<br>
                    <strong>文件大小：</strong>{{ result.file_info.file_size }}<br>
                    <strong>字符数：</strong>{{ result.file_info.char_count }}<br>
                    {% if result.file_info.encoding %}
                    <strong>编码：</strong>{{ result.file_info.encoding }}<br>
                    {% endif %}
                    <strong>处理时间：</strong>{{ result.file_info.processed_at }}<br>
                    <strong>耗时：</strong>{{ result.file_info.processing_time }}
                </small>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- 操作建议 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    后续操作建议
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-edit text-primary" style="font-size: 2rem;"></i>
                            <h6 class="mt-2">编辑流程图</h6>
                            <p class="small text-muted">
                                使用PowerPoint或Visio打开生成的文件，可以进一步编辑和美化流程图
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-share-alt text-success" style="font-size: 2rem;"></i>
                            <h6 class="mt-2">分享流程图</h6>
                            <p class="small text-muted">
                                将生成的流程图分享给团队成员，用于制度培训和流程优化
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-sync-alt text-warning" style="font-size: 2rem;"></i>
                            <h6 class="mt-2">持续优化</h6>
                            <p class="small text-muted">
                                根据实际使用情况，不断完善制度文档和流程图的准确性
                            </p>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <a href="{{ url_for('text_input') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-keyboard me-1"></i>处理新文本
                    </a>
                    <a href="{{ url_for('upload_file') }}" class="btn btn-outline-success">
                        <i class="fas fa-upload me-1"></i>上传新文件
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 自动滚动到下载区域
    const downloadSection = document.querySelector('.file-card');
    if (downloadSection) {
        setTimeout(() => {
            downloadSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 500);
    }
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(function() {
        // 显示复制成功提示
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>已复制';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-primary');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(function(err) {
        alert('复制失败: ' + err);
    });
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.file-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.file-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.file-header {
    margin-bottom: 20px;
    text-align: center;
}

.file-header h6 {
    color: #667eea;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 8px;
}

.file-header small {
    display: block;
    word-break: break-all;
    padding: 0 10px;
}

.file-actions {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: auto;
}

.file-actions .btn {
    border-radius: 20px;
    padding: 10px 20px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    min-width: 140px;
    box-shadow: 0 2px 5px rgba(102, 126, 234, 0.2);
}

.file-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.process-step {
    position: relative;
    transition: all 0.3s ease;
}

.process-step:hover {
    background-color: #f8f9fa;
    transform: translateX(3px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .file-card {
        margin-bottom: 15px;
        padding: 15px;
    }
    
    .file-actions .btn {
        width: 100%;
        margin-bottom: 8px;
    }
    
    .file-header h6 {
        font-size: 1rem;
    }
}
</style>
{% endblock %} 