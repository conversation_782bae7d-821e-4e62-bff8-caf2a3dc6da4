import logging
import json
import os
import sys
from datetime import datetime

# 导入测试目标模块
from llm_processor import LLMProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test')

def save_to_file(data, filename):
    """将数据保存到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            if isinstance(data, str):
                f.write(data)
            else:
                json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"响应已保存到 {filename}")
    except Exception as e:
        logger.error(f"保存文件失败: {e}")

def main():
    """测试中石化API响应并保存到文件"""
    try:
        # 初始化处理器
        processor = LLMProcessor()
        
        # 测试文本
        test_text = """
        第五条  运行管理机制
        （一）域长提出变更申请
        （二）部门主管进行初审
        （三）安全生产部进行合规性审核
        （四）总经理办公会审批
        （五）人力资源部备案
        """
        
        # 调用API
        logger.info("开始调用中石化API...")
        document_content, raw_response = processor._call_sinopec(test_text, use_stream=True)
        
        # 保存原始响应
        now = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = "test_outputs"
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存原始字符串响应
        raw_filename = f"{output_dir}/raw_response_{now}.txt"
        save_to_file(raw_response, raw_filename)
        
        # 尝试解析并保存JSON格式
        try:
            # 准备测试数据
            test_data = {
                "success": True,
                "data": {
                    "文本呈现": raw_response,
                    "raw_response": raw_response,
                    "timestamp": now,
                    "note": "中石化AI平台分析结果"
                }
            }
            
            # 保存为JSON格式
            json_filename = f"{output_dir}/test_processor_response.json"
            save_to_file(test_data, json_filename)
            
            logger.info("测试完成")
        except Exception as e:
            logger.error(f"创建JSON测试数据失败: {e}")
    
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 