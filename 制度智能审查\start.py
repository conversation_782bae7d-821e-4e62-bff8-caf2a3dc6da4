#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
制度智能审查系统启动器
"""

import sys
import os
import subprocess
import time
import webbrowser
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✓ Python版本: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """检查必要依赖"""
    required_packages = [
        'flask',
        'requests', 
        'python-docx',
        'PyPDF2',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n正在安装缺失的依赖包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                '-r', 'requirements.txt'
            ])
            print("✓ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败，请手动运行:")
            print("pip install -r requirements.txt")
            return False
    
    return True

def start_web_app():
    """启动Web应用"""
    print("\n" + "="*50)
    print("制度智能审查系统启动中...")
    print("="*50)
    
    try:
        # 启动Flask应用
        from web_app import app
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(1.5)  # 等待服务器启动
            webbrowser.open('http://127.0.0.1:5000')
        
        import threading
        threading.Thread(target=open_browser, daemon=True).start()
        
        print("🚀 服务器启动成功!")
        print("📱 访问地址: http://127.0.0.1:5000")
        print("🛑 按 Ctrl+C 停止服务器")
        print("="*50)
        
        # 启动应用
        app.run(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请检查端口5000是否被占用")

def main():
    """主函数"""
    print("制度智能审查系统启动器")
    print("="*50)
    
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 检查Python版本
    if not check_python_version():
        input("按Enter键退出...")
        return
    
    # 检查依赖
    print("\n🔍 检查依赖包...")
    if not check_dependencies():
        input("按Enter键退出...")
        return
    
    # 启动应用
    start_web_app()

if __name__ == "__main__":
    main() 