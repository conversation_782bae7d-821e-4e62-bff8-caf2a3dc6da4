# file_readers.py
import os
import sys
import struct
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

# 导入日志模块
from logger_config import get_logger

# PDF处理库
try:
    import PyPDF2
    import pdfplumber
    import fitz  # PyMuPDF
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("警告: PDF处理库未安装，请运行: pip install PyPDF2 pdfplumber pymupdf")

# Word处理库
try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("警告: Word处理库未安装，请运行: pip install python-docx")

# 字符编码检测库
try:
    import chardet
    CHARDET_AVAILABLE = True
except ImportError:
    CHARDET_AVAILABLE = False
    print("警告: 字符编码检测库未安装，请运行: pip install chardet")

# 初始化日志
file_logger = get_logger("file")

def detect_file_type_by_content(file_path: str) -> Optional[str]:
    """通过文件内容检测文件类型"""
    file_logger.info(f"开始检测文件类型: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            # 读取文件头部字节
            header = f.read(8)
            
        if not header:
            file_logger.warning(f"文件为空: {file_path}")
            return None
        
        # PDF文件检测
        if header.startswith(b'%PDF'):
            file_logger.info(f"检测到PDF文件: {file_path}")
            return '.pdf'
        
        # Word文档检测 (DOCX是ZIP格式)
        if header.startswith(b'PK\x03\x04'):
            # 进一步检查是否为DOCX
            try:
                with open(file_path, 'rb') as f:
                    content = f.read(1024)
                    if b'word/' in content or b'document.xml' in content:
                        file_logger.info(f"检测到DOCX文件: {file_path}")
                        return '.docx'
            except:
                pass
        
        # 尝试作为文本文件读取
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                f.read(100)  # 尝试读取前100个字符
            file_logger.info(f"检测到文本文件: {file_path}")
            return '.txt'
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    f.read(100)
                file_logger.info(f"检测到文本文件(GBK编码): {file_path}")
                return '.txt'
            except:
                pass
        
        file_logger.warning(f"无法识别文件类型: {file_path}")
        return None
        
    except Exception as e:
        file_logger.error(f"文件类型检测失败: {file_path}, 错误: {e}")
        return None

def get_file_extension_smart(file_path: str) -> Optional[str]:
    """智能获取文件扩展名（优先使用文件名，失败时使用内容检测）"""
    file_logger.info(f"智能获取文件扩展名: {file_path}")
    
    # 首先尝试从文件名获取扩展名
    if '.' in os.path.basename(file_path):
        ext = os.path.splitext(file_path)[1].lower()
        if ext:
            file_logger.info(f"从文件名获取扩展名: {ext}")
            return ext
    
    # 如果文件名没有扩展名，使用内容检测
    file_logger.info(f"文件名无扩展名，使用内容检测: {file_path}")
    detected_ext = detect_file_type_by_content(file_path)
    if detected_ext:
        file_logger.info(f"内容检测结果: {detected_ext}")
    
    return detected_ext

class FileReader(ABC):
    """文件读取器基类"""
    
    @abstractmethod
    def read(self, file_path):
        """读取文件内容"""
        pass
    
    @abstractmethod
    def can_handle(self, file_path):
        """检查是否能处理该文件类型"""
        pass

class TxtReader(FileReader):
    """TXT文件读取器"""
    
    def can_handle(self, file_path):
        file_ext = get_file_extension_smart(file_path)
        return file_ext == '.txt'
    
    def read(self, file_path):
        """读取TXT文件"""
        try:
            encoding = 'utf-8'  # 默认编码
            
            # 如果有chardet，使用自动检测编码
            if CHARDET_AVAILABLE:
                try:
                    with open(file_path, 'rb') as f:
                        raw_data = f.read()
                        detected = chardet.detect(raw_data)
                        if detected and detected['encoding']:
                            encoding = detected['encoding']
                except Exception as e:
                    file_logger.warning(f"编码检测失败，使用默认编码: {e}")
            
            # 尝试多种编码读取文件
            encodings_to_try = [encoding, 'utf-8', 'gbk', 'gb2312', 'latin-1']
            content = None
            used_encoding = None
            
            for enc in encodings_to_try:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        content = f.read()
                        used_encoding = enc
                        break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    file_logger.warning(f"使用编码 {enc} 读取失败: {e}")
                    continue
            
            if content is None:
                raise Exception("无法使用任何编码读取文件")
            
            return {
                'content': content,
                'metadata': {
                    'file_type': 'txt',
                    'encoding': used_encoding,
                    'size': len(content)
                }
            }
        except Exception as e:
            raise Exception(f"读取TXT文件失败: {str(e)}")

class PDFReader(FileReader):
    """PDF文件读取器"""
    
    def can_handle(self, file_path):
        file_ext = get_file_extension_smart(file_path)
        return file_ext == '.pdf' and PDF_AVAILABLE
    
    def read(self, file_path):
        """读取PDF文件，使用多种方法确保最佳效果"""
        if not PDF_AVAILABLE:
            raise Exception("PDF处理库未安装")
        
        content = ""
        metadata = {
            'file_type': 'pdf',
            'pages': 0,
            'method_used': []
        }
        
        # 方法1: 使用pdfplumber（推荐，处理表格和复杂布局效果好）
        try:
            content_plumber = self._read_with_pdfplumber(file_path)
            if content_plumber.strip():
                content = content_plumber
                metadata['method_used'].append('pdfplumber')
        except Exception as e:
            print(f"pdfplumber读取失败: {e}")
        
        # 方法2: 如果pdfplumber失败，使用PyMuPDF
        if not content.strip():
            try:
                content_fitz = self._read_with_pymupdf(file_path)
                if content_fitz.strip():
                    content = content_fitz
                    metadata['method_used'].append('pymupdf')
            except Exception as e:
                print(f"PyMuPDF读取失败: {e}")
        
        # 方法3: 最后尝试PyPDF2
        if not content.strip():
            try:
                content_pypdf2 = self._read_with_pypdf2(file_path)
                if content_pypdf2.strip():
                    content = content_pypdf2
                    metadata['method_used'].append('pypdf2')
            except Exception as e:
                print(f"PyPDF2读取失败: {e}")
        
        if not content.strip():
            raise Exception("所有PDF读取方法都失败了")
        
        return {
            'content': content,
            'metadata': metadata
        }
    
    def _read_with_pdfplumber(self, file_path):
        """使用pdfplumber读取PDF"""
        content = ""
        with pdfplumber.open(file_path) as pdf:
            for page_num, page in enumerate(pdf.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        content += f"\n--- 第{page_num}页 ---\n"
                        content += page_text + "\n"
                    
                    # 提取表格
                    tables = page.extract_tables()
                    for table_num, table in enumerate(tables, 1):
                        content += f"\n--- 第{page_num}页 表格{table_num} ---\n"
                        for row in table:
                            if row:
                                content += " | ".join([cell or "" for cell in row]) + "\n"
                
                except Exception as e:
                    print(f"处理第{page_num}页时出错: {e}")
                    continue
        
        return content
    
    def _read_with_pymupdf(self, file_path):
        """使用PyMuPDF读取PDF"""
        content = ""
        doc = fitz.open(file_path)
        
        for page_num in range(doc.page_count):
            try:
                page = doc[page_num]
                page_text = page.get_text()
                if page_text.strip():
                    content += f"\n--- 第{page_num + 1}页 ---\n"
                    content += page_text + "\n"
            except Exception as e:
                print(f"处理第{page_num + 1}页时出错: {e}")
                continue
        
        doc.close()
        return content
    
    def _read_with_pypdf2(self, file_path):
        """使用PyPDF2读取PDF"""
        content = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        content += f"\n--- 第{page_num}页 ---\n"
                        content += page_text + "\n"
                except Exception as e:
                    print(f"处理第{page_num}页时出错: {e}")
                    continue
        
        return content

class WordReader(FileReader):
    """Word文档读取器"""
    
    def can_handle(self, file_path):
        file_ext = get_file_extension_smart(file_path)
        return file_ext in ['.docx', '.doc'] and DOCX_AVAILABLE
    
    def read(self, file_path):
        """读取Word文档"""
        if not DOCX_AVAILABLE:
            raise Exception("Word处理库未安装")
        
        try:
            file_logger.info(f"使用python-docx读取Word文档: {file_path}")
            
            # 使用python-docx读取文档
            doc = Document(file_path)
            
            # 提取文本内容
            content = []
            
            # 提取标题
            for i, para in enumerate(doc.paragraphs):
                if para.style.name.startswith('Heading'):
                    content.append(f"\n## {para.text}\n")
                else:
                    content.append(para.text)
            
            # 提取表格内容
            for i, table in enumerate(doc.tables):
                content.append(f"\n表格 {i+1}:")
                for row in table.rows:
                    row_content = []
                    for cell in row.cells:
                        # 处理单元格中的段落
                        cell_text = '\n'.join(p.text for p in cell.paragraphs if p.text.strip())
                        row_content.append(cell_text)
                    content.append(" | ".join(row_content))
                content.append("\n")
            
            # 合并所有内容
            full_content = "\n".join(content)
            
            # 检查内容是否为空或只有数字
            if not full_content.strip():
                file_logger.warning("提取的Word文档内容为空")
                raise ValueError("提取的Word文档内容为空")
            
            # 如果提取的内容只有数字，尝试备用方法
            if full_content.strip().isdigit():
                file_logger.warning(f"提取的Word内容只有数字: '{full_content.strip()}'")
                
                # 尝试备用方法：使用更底层的方式提取文本
                try:
                    file_logger.info("尝试使用备用方法提取Word文档内容")
                    
                    # 尝试提取文档属性
                    props_content = []
                    try:
                        core_props = doc.core_properties
                        if core_props.title:
                            props_content.append(f"标题: {core_props.title}")
                        if core_props.subject:
                            props_content.append(f"主题: {core_props.subject}")
                        if core_props.author:
                            props_content.append(f"作者: {core_props.author}")
                        if core_props.comments:
                            props_content.append(f"注释: {core_props.comments}")
                        if core_props.keywords:
                            props_content.append(f"关键词: {core_props.keywords}")
                    except:
                        pass
                    
                    # 尝试提取文档中的所有文本块
                    all_runs = []
                    for para in doc.paragraphs:
                        for run in para.runs:
                            if run.text.strip():
                                all_runs.append(run.text)
                    
                    # 如果找到了更多内容，使用这些内容
                    if props_content or all_runs:
                        backup_content = "\n".join(props_content + all_runs)
                        if len(backup_content) > len(full_content):
                            file_logger.info("备用方法成功提取到更多内容")
                            full_content = backup_content
                
                except Exception as e:
                    file_logger.warning(f"备用方法提取失败: {e}")
            
            # 最终检查
            if full_content.strip().isdigit() and len(full_content.strip()) < 10:
                file_logger.error(f"无法从Word文档中提取有效内容，只得到数字: '{full_content.strip()}'")
                raise ValueError(f"无法从Word文档中提取有效内容，只得到数字: '{full_content.strip()}'")
            
            return {
                'content': full_content,
                'metadata': {
                    'file_type': 'docx',
                    'paragraphs': len(doc.paragraphs),
                    'tables': len(doc.tables),
                    'size': len(full_content)
                }
            }
            
        except Exception as e:
            file_logger.error(f"读取Word文档失败: {str(e)}", exc_info=True)
            raise Exception(f"读取Word文档失败: {str(e)}")

class FileReaderFactory:
    """文件读取器工厂类"""
    
    def __init__(self):
        self.readers = [
            TxtReader(),
            PDFReader(),
            WordReader()
        ]
    
    def get_reader(self, file_path):
        """根据文件类型获取对应的读取器"""
        print(f"调试: 尝试为文件获取读取器: {file_path}")
        
        if not file_path:
            raise Exception("文件路径为空")
        
        file_ext = get_file_extension_smart(file_path)
        print(f"调试: 文件扩展名: '{file_ext}'")
        
        if not file_ext:
            raise Exception("文件没有扩展名，无法确定文件类型")
        
        for reader in self.readers:
            print(f"调试: 检查读取器 {type(reader).__name__} 是否能处理文件")
            if reader.can_handle(file_path):
                print(f"调试: 使用读取器 {type(reader).__name__}")
                return reader
        
        # 获取支持的格式列表
        supported_formats = self.get_supported_formats()
        raise Exception(f"不支持的文件格式: '{file_ext}'。支持的格式: {supported_formats}")
    
    def read_file(self, file_path):
        """读取文件内容"""
        file_logger.info(f"开始读取文件: {file_path}")
        
        if not os.path.exists(file_path):
            file_logger.error(f"文件不存在: {file_path}")
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        file_logger.info(f"文件大小: {file_size} 字节")
        
        if file_size == 0:
            file_logger.error("文件为空")
            raise ValueError("文件为空")
        
        # 获取文件类型
        reader = self.get_reader(file_path)
        
        if reader:
            try:
                result = reader.read(file_path)
                content = result['content']
                metadata = result['metadata']
                
                # 检查内容是否为空或只有数字
                stripped_content = content.strip()
                if not stripped_content:
                    file_logger.warning("提取的内容为空")
                    raise ValueError("提取的内容为空")
                elif stripped_content.isdigit() and len(stripped_content) < 10:
                    file_logger.warning(f"提取的内容只有数字: '{stripped_content}'")
                    
                    # 尝试直接读取文件内容
                    try:
                        file_logger.info("尝试直接读取文件内容...")
                        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
                        for encoding in encodings:
                            try:
                                with open(file_path, 'r', encoding=encoding) as f:
                                    direct_content = f.read()
                                    if direct_content and len(direct_content.strip()) > len(stripped_content):
                                        file_logger.info(f"使用编码 {encoding} 直接读取成功")
                                        content = direct_content
                                        metadata['note'] = f"使用备用方法（编码：{encoding}）读取"
                                        break
                            except Exception:
                                continue
                    except Exception as e:
                        file_logger.warning(f"直接读取失败: {e}")
                
                file_logger.info(f"文件读取成功，内容长度: {len(content)} 字符")
                file_logger.debug(f"内容前100个字符: {content[:100]}")
                
                return {
                    'content': content,
                    'metadata': metadata
                }
            except Exception as e:
                file_logger.error(f"使用专用读取器失败: {str(e)}", exc_info=True)
                # 尝试使用通用方法读取
        
        # 如果没有合适的读取器或专用读取器失败，尝试通用方法
        file_logger.info("尝试使用通用方法读取文件...")
        try:
            # 尝试不同编码读取文本
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            content = None
            used_encoding = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                        used_encoding = encoding
                        file_logger.info(f"使用编码 {encoding} 成功读取")
                        break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    file_logger.warning(f"使用编码 {encoding} 读取失败: {e}")
            
            # 如果文本读取失败，尝试二进制读取并转换
            if content is None:
                file_logger.info("文本读取失败，尝试二进制读取...")
                with open(file_path, 'rb') as f:
                    binary_data = f.read()
                    
                # 尝试检测编码
                if CHARDET_AVAILABLE:
                    detected = chardet.detect(binary_data)
                    if detected and detected['encoding']:
                        try:
                            content = binary_data.decode(detected['encoding'])
                            used_encoding = detected['encoding']
                            file_logger.info(f"使用检测到的编码 {used_encoding} 成功读取")
                        except:
                            pass
                
                # 如果仍然失败，尝试强制解码
                if content is None:
                    try:
                        content = binary_data.decode('latin-1')
                        used_encoding = 'latin-1 (forced)'
                        file_logger.info("使用强制Latin-1编码读取")
                    except:
                        pass
            
            if content is None:
                file_logger.error("所有读取方法都失败")
                raise ValueError("无法读取文件内容")
            
            return {
                'content': content,
                'metadata': {
                    'file_type': 'unknown',
                    'encoding': used_encoding,
                    'size': len(content),
                    'note': '使用通用方法读取'
                }
            }
            
        except Exception as e:
            file_logger.error(f"读取文件失败: {str(e)}", exc_info=True)
            raise ValueError(f"读取文件失败: {str(e)}")
    
    def get_supported_formats(self):
        """获取支持的文件格式列表"""
        formats = []
        
        # TXT格式总是支持的
        formats.append('.txt')
        
        # PDF格式
        if PDF_AVAILABLE:
            formats.append('.pdf')
        
        # Word格式
        if DOCX_AVAILABLE:
            formats.append('.docx')
        
        return formats

# 便捷函数
def read_document(file_path):
    """便捷的文档读取函数"""
    factory = FileReaderFactory()
    return factory.read_file(file_path)

def get_supported_formats():
    """获取支持的文件格式"""
    factory = FileReaderFactory()
    return factory.get_supported_formats()

# 测试函数
def test_readers():
    """测试各种文件读取器"""
    factory = FileReaderFactory()
    
    print("支持的文件格式:", factory.get_supported_formats())
    
    # 创建测试文件
    test_txt = "test_input.txt"
    with open(test_txt, 'w', encoding='utf-8') as f:
        f.write("""
        测试制度文档
        
        第一条：员工请假需要填写申请表
        第二条：主管审核申请表
        第三条：人事部门最终审批
        """)
    
    try:
        result = factory.read_file(test_txt)
        print(f"\n读取结果:")
        print(f"文件类型: {result['metadata']['file_type']}")
        print(f"内容长度: {len(result['content'])}")
        print(f"内容预览: {result['content'][:100]}...")
        
        # 清理测试文件
        os.remove(test_txt)
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_readers() 