#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中石化API修复脚本 - 解决流程解析结果不一致问题
"""

import json
import requests
import time
import tempfile
import base64
import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api_config import get_api_config

def test_sinopec_stream_mode():
    """测试中石化API的流式模式"""
    print("🔄 测试中石化API流式模式...")
    
    # 测试文档内容
    test_content = """海南炼化公司域长负责制运行管理细则

第一条 申请流程
员工需要申请域长审批的事项时，应按以下流程进行：
1. 填写申请表格
2. 直接主管初步审核
3. 域长最终审批
4. 相关部门备案

第二条 审批权限
域长有权对以下事项进行审批：
1. 10万元以下的费用支出
2. 人员调动申请
3. 设备采购申请

第三条 时间要求
1. 申请人提交申请后，直接主管应在2个工作日内完成初审
2. 域长应在3个工作日内完成最终审批
3. 相关部门应在1个工作日内完成备案"""
    
    # 获取API配置
    config = get_api_config('sinopec')
    if not config or not config.get('enabled'):
        print("❌ 中石化API未配置或未启用")
        return None
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', encoding='utf-8', delete=False) as temp_file:
        temp_file.write(test_content)
        temp_file_path = temp_file.name
    
    try:
        # 读取文件并编码为base64
        with open(temp_file_path, 'rb') as f:
            file_content = f.read()
            base64_content = base64.b64encode(file_content).decode('utf-8')
        
        # 构建API请求 - 使用流式模式
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "content": [
                {
                    "field_name": "files",
                    "type": "file",
                    "value": base64_content
                }
            ],
            "stream": True  # 强制使用流式模式
        }
        
        api_url = f"{config['base_url']}/{config['workflow_id']}"
        
        print(f"🔗 请求URL: {api_url}")
        print(f"📊 使用流式模式: True")
        
        # 发起API调用
        print("🚀 发起流式API调用...")
        response = requests.post(
            api_url,
            headers=headers,
            json=data,
            timeout=120,
            verify=config.get("verify_ssl", True),
            stream=True  # 启用流式接收
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 流式API调用成功")
            
            # 处理流式响应
            full_response = ""
            chunks = []
            
            for line in response.iter_lines():
                if line:
                    try:
                        # 解码每一行
                        line_str = line.decode('utf-8')
                        
                        # 跳过空行和非JSON行
                        if line_str.strip() and line_str.startswith('data: '):
                            json_str = line_str[6:]  # 移除 "data: " 前缀
                            
                            if json_str.strip() == '[DONE]':
                                print("📋 流式响应结束")
                                break
                            
                            try:
                                chunk_data = json.loads(json_str)
                                chunks.append(chunk_data)
                                
                                # 检查是否包含文本内容
                                if 'data' in chunk_data and 'text' in chunk_data['data']:
                                    text_content = chunk_data['data']['text']
                                    if isinstance(text_content, list) and text_content:
                                        for text_item in text_content:
                                            if isinstance(text_item, dict) and 'output' in text_item:
                                                full_response += text_item['output']
                                            elif isinstance(text_item, str):
                                                full_response += text_item
                                    elif isinstance(text_content, str):
                                        full_response += text_content
                                
                                print(f"📦 收到数据块: {chunk_data.get('event', 'unknown')}")
                                
                            except json.JSONDecodeError as e:
                                print(f"⚠️ JSON解析失败: {e}")
                                continue
                    except Exception as e:
                        print(f"⚠️ 处理响应行时出错: {e}")
                        continue
            
            print(f"📝 完整响应长度: {len(full_response)} 字符")
            if full_response:
                print(f"📝 响应前300字符: {full_response[:300]}")
                
                # 保存流式响应
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                stream_result_file = f"sinopec_stream_result_{timestamp}.txt"
                with open(stream_result_file, 'w', encoding='utf-8') as f:
                    f.write(full_response)
                print(f"💾 流式响应已保存到: {stream_result_file}")
                
                return full_response
            else:
                print("⚠️ 未获取到有效的响应内容")
                return None
            
        else:
            print(f"❌ 流式API调用失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            return None
            
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file_path)
        except:
            pass

def test_sinopec_session_query():
    """测试通过session_id查询结果的不同方法"""
    print("\n🔍 测试session_id查询方法...")
    
    # 首先发起一个非流式调用获取session_id
    config = get_api_config('sinopec')
    if not config or not config.get('enabled'):
        print("❌ 中石化API未配置或未启用")
        return None
    
    test_content = "测试内容：申请流程包括提交申请、主管审核、最终审批三个步骤。"
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', encoding='utf-8', delete=False) as temp_file:
        temp_file.write(test_content)
        temp_file_path = temp_file.name
    
    try:
        # 读取文件并编码为base64
        with open(temp_file_path, 'rb') as f:
            file_content = f.read()
            base64_content = base64.b64encode(file_content).decode('utf-8')
        
        # 发起非流式调用获取session_id
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "content": [
                {
                    "field_name": "files",
                    "type": "file",
                    "value": base64_content
                }
            ],
            "stream": False
        }
        
        api_url = f"{config['base_url']}/{config['workflow_id']}"
        
        print("🚀 发起非流式调用获取session_id...")
        response = requests.post(
            api_url,
            headers=headers,
            json=data,
            timeout=60,
            verify=config.get("verify_ssl", True)
        )
        
        if response.status_code == 200:
            result = response.json()
            session_id = result.get('data', {}).get('session_id')
            
            if session_id:
                print(f"✅ 获取到session_id: {session_id}")
                
                # 尝试不同的查询URL
                query_urls = [
                    f"{config['base_url']}/sessions/{session_id}/result",
                    f"{config['base_url']}/sessions/{session_id}",
                    f"https://agent.ai.sinopec.com/aicoapi/gateway/v2/sessions/{session_id}/result",
                    f"https://agent.ai.sinopec.com/aicoapi/gateway/v2/workflow/sessions/{session_id}/result",
                    f"https://agent.ai.sinopec.com/aicoapi/gateway/v2/workflow/result/{session_id}",
                ]
                
                for i, query_url in enumerate(query_urls, 1):
                    print(f"\n🔍 尝试查询方法 {i}: {query_url}")
                    
                    try:
                        query_response = requests.get(
                            query_url,
                            headers=headers,
                            timeout=30,
                            verify=config.get("verify_ssl", True)
                        )
                        
                        print(f"   状态码: {query_response.status_code}")
                        
                        if query_response.status_code == 200:
                            query_result = query_response.json()
                            print(f"   ✅ 查询成功")
                            print(f"   响应字段: {list(query_result.keys())}")
                            
                            # 保存查询结果
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            query_file = f"sinopec_query_result_{i}_{timestamp}.json"
                            with open(query_file, 'w', encoding='utf-8') as f:
                                json.dump(query_result, f, ensure_ascii=False, indent=2)
                            print(f"   💾 查询结果已保存到: {query_file}")
                            
                        else:
                            print(f"   ❌ 查询失败: {query_response.status_code}")
                            if query_response.text:
                                print(f"   错误信息: {query_response.text[:200]}")
                    
                    except Exception as e:
                        print(f"   ❌ 查询异常: {e}")
                
            else:
                print("❌ 未获取到session_id")
        else:
            print(f"❌ 初始调用失败: {response.status_code}")
            
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file_path)
        except:
            pass

def test_different_input_formats():
    """测试不同的输入格式"""
    print("\n🧪 测试不同的输入格式...")
    
    config = get_api_config('sinopec')
    if not config or not config.get('enabled'):
        print("❌ 中石化API未配置或未启用")
        return
    
    test_content = "申请流程：1.提交申请 2.主管审核 3.最终审批"
    
    # 测试不同的输入格式
    input_formats = [
        {
            "name": "文件格式(files)",
            "content": [{"field_name": "files", "type": "file", "value": ""}]
        },
        {
            "name": "文本格式(text)",
            "content": [{"field_name": "text", "type": "input", "value": test_content}]
        },
        {
            "name": "用户输入格式(user_input)",
            "content": [{"field_name": "user_input", "type": "input", "value": test_content}]
        },
        {
            "name": "混合格式",
            "content": [
                {"field_name": "text", "type": "input", "value": test_content},
                {"field_name": "user_input", "type": "input", "value": "请分析这个流程"}
            ]
        }
    ]
    
    headers = {
        "Authorization": f"Bearer {config['api_key']}",
        "Content-Type": "application/json"
    }
    
    api_url = f"{config['base_url']}/{config['workflow_id']}"
    
    for i, format_test in enumerate(input_formats, 1):
        print(f"\n📋 测试格式 {i}: {format_test['name']}")
        
        # 如果是文件格式，需要准备base64内容
        content_data = format_test['content'].copy()
        if any(item.get('type') == 'file' for item in content_data):
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', encoding='utf-8', delete=False) as temp_file:
                temp_file.write(test_content)
                temp_file_path = temp_file.name
            
            try:
                with open(temp_file_path, 'rb') as f:
                    file_content = f.read()
                    base64_content = base64.b64encode(file_content).decode('utf-8')
                
                # 更新文件字段的value
                for item in content_data:
                    if item.get('type') == 'file':
                        item['value'] = base64_content
            finally:
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
        
        data = {
            "content": content_data,
            "stream": True  # 使用流式模式
        }
        
        try:
            print(f"   🚀 发起API调用...")
            response = requests.post(
                api_url,
                headers=headers,
                json=data,
                timeout=60,
                verify=config.get("verify_ssl", True),
                stream=True
            )
            
            print(f"   📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 调用成功")
                
                # 简单处理流式响应
                response_content = ""
                for line in response.iter_lines():
                    if line:
                        try:
                            line_str = line.decode('utf-8')
                            if line_str.startswith('data: ') and not line_str.endswith('[DONE]'):
                                json_str = line_str[6:]
                                chunk_data = json.loads(json_str)
                                
                                if 'data' in chunk_data and 'text' in chunk_data['data']:
                                    text_content = chunk_data['data']['text']
                                    if isinstance(text_content, list):
                                        for text_item in text_content:
                                            if isinstance(text_item, dict) and 'output' in text_item:
                                                response_content += text_item['output']
                        except:
                            continue
                
                if response_content:
                    print(f"   📝 获取到响应内容，长度: {len(response_content)} 字符")
                    print(f"   📝 内容预览: {response_content[:100]}")
                else:
                    print(f"   ⚠️ 未获取到有效响应内容")
            else:
                print(f"   ❌ 调用失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 调用异常: {e}")

def main():
    """主函数"""
    print("🔧 中石化API修复测试")
    print("=" * 60)
    
    # 1. 测试流式模式
    stream_result = test_sinopec_stream_mode()
    
    # 2. 测试session查询
    test_sinopec_session_query()
    
    # 3. 测试不同输入格式
    test_different_input_formats()
    
    print("\n" + "=" * 60)
    print("✅ 修复测试完成！")
    
    print("\n📋 测试总结:")
    if stream_result:
        print("   ✅ 流式模式获取到了完整结果")
        print("   💡 建议修改系统配置，启用流式模式")
    else:
        print("   ⚠️ 流式模式测试未获取到完整结果")
    
    print("\n🔧 修复建议:")
    print("   1. 在API配置中将stream设置为true")
    print("   2. 修改llm_processor.py中的流式响应处理逻辑")
    print("   3. 确保正确处理流式响应的数据格式")
    print("   4. 测试不同的输入字段名称(text vs files)")

if __name__ == "__main__":
    main() 