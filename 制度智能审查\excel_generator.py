#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel格式审批流程生成器 - 简化版
"""

import os
import pandas as pd
import re
from datetime import datetime
from typing import Dict, List, Any
import logging
import json
import ast
import openpyxl.utils

# 配置日志
logger = logging.getLogger(__name__)

class ExcelGenerator:
    """Excel格式流程图生成器 - 简化版，只将API返回数据转换为Excel"""
    
    def __init__(self):
        self.logger = logger
    
    def create_excel_report(self, process_data: Dict[str, Any], output_path: str):
        """创建Excel格式的审批流程报告，仅转换API返回的数据"""
        try:
            self.logger.info("开始生成Excel格式流程报告...")
            
            # 创建Excel写入器
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 提取流程数据
                steps_data, departments_data = self._extract_process_data(process_data)
                
                # 创建流程步骤表
                self._create_steps_sheet(writer, steps_data)
                
                # 创建部门职责表
                self._create_departments_sheet(writer, departments_data)
                
                # 创建API数据表
                self._create_api_data_sheet(writer, process_data)
            
            self.logger.info(f"Excel报告已生成: {output_path}")
            return output_path
        except Exception as e:
            self.logger.error(f"生成Excel报告失败: {e}")
            raise
    
    def _extract_process_data(self, process_data: Dict[str, Any]) -> tuple:
        """从API响应中提取流程数据"""
        steps_data = []
        departments_data = []
        
        try:
            self.logger.info(f"开始从API响应提取流程数据")
            
            # 从多个可能的位置尝试提取文本内容
            content_text = None
            
            # 尝试提取API调用返回的原始文本
            raw_text = None
            
            # 1. 检查是否有有效的LLM分析数据
            if 'llm_analysis' in process_data and process_data['llm_analysis']:
                # 从llm_analysis中获取raw_response
                llm_analysis = process_data['llm_analysis']
                
                # 保存原始响应，用于后续处理
                if 'raw_response' in llm_analysis and llm_analysis['raw_response']:
                    raw_text = llm_analysis['raw_response']
            
            # 2. 尝试从data字段提取
            if 'data' in process_data:
                # 尝试从data.文本呈现字段提取
                if '文本呈现' in process_data['data']:
                    content_text = process_data['data']['文本呈现']
                
                # 尝试从data.content字段提取
                elif 'content' in process_data['data']:
                    content_text = process_data['data']['content']
                
                # 尝试从data.raw_response字段提取
                elif 'raw_response' in process_data['data']:
                    raw_text = process_data['data']['raw_response']
            
            # 3. 如果有原始JSON响应，尝试解析它
            if not content_text and raw_text:
                try:
                    # 尝试直接作为JSON字符串解析
                    try:
                        json_obj = json.loads(raw_text) if isinstance(raw_text, str) else raw_text
                        
                        # 尝试提取文本呈现字段（工作流完成事件）
                        if isinstance(json_obj, dict):
                            # 检查workflow_finished事件
                            if json_obj.get('event') == 'workflow_finished' and 'data' in json_obj:
                                if '文本呈现' in json_obj['data']:
                                    content_text = json_obj['data']['文本呈现']
                            
                            # 尝试提取data字段中的文本呈现
                            elif 'data' in json_obj and isinstance(json_obj['data'], dict):
                                if '文本呈现' in json_obj['data']:
                                    content_text = json_obj['data']['文本呈现']
                    except:
                        # 如果不是有效的JSON，尝试查找事件流中的文本呈现
                        match = re.search(r'"event"\s*:\s*"workflow_finished".*?"data"\s*:\s*{.*?"文本呈现"\s*:\s*"(.*?)"', raw_text, re.DOTALL)
                        if match:
                            content_text = match.group(1)
                            # 处理转义字符
                            content_text = content_text.replace('\\n', '\n').replace('\\t', '\t').replace('\\"', '"')
                except Exception as e:
                    self.logger.warning(f"解析原始响应失败: {e}")
            
            # 4. 如果找到了内容文本，提取流程步骤和部门职责
            if content_text:
                self.logger.info(f"找到内容文本，长度: {len(content_text)} 字符")
                steps_data = self._extract_steps(content_text)
                departments_data = self._extract_departments_table(content_text)
                return steps_data, departments_data
            else:
                self.logger.warning("未找到内容文本")
                
        except Exception as e:
            self.logger.error(f"提取流程数据失败: {e}")
        
        # 如果没有找到任何数据，返回空列表
        if not steps_data:
            self.logger.warning("未提取到流程步骤数据")
        if not departments_data:
            self.logger.warning("未提取到部门职责数据")
            
        return steps_data, departments_data
    
    def _extract_steps(self, text: str) -> List[Dict[str, str]]:
        """从文本中提取流程步骤"""
        steps = []
        try:
            self.logger.info("开始提取流程步骤，原文长度：%d", len(text))
            
            # 查找流程步骤部分
            # 多种模式匹配，提高兼容性
            
            # 模式1: 查找"审批流程环节"部分
            process_section = None
            patterns = [
                r'(?:审批流程环节|流程环节).*?(?:（按顺序列出）)?\s*\n(.*?)(?:\n\n---|\n\n###|\n\n####|$)',
                r'[0-9]+\.\s+\*\*([^*]+)\*\*'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text, re.DOTALL)
                if match:
                    if pattern.startswith('[0-9]+'):
                        # 直接搜索步骤的模式
                        process_section = text
                        self.logger.info("找到直接的步骤格式")
                    else:
                        # 搜索章节的模式
                        process_section = match.group(1)
                        self.logger.info("找到流程章节，长度：%d", len(process_section))
                    break
            
            if not process_section and "流程环节" in text:
                # 尝试提取流程环节相关段落
                lines = text.split('\n')
                start_idx = -1
                end_idx = -1
                
                for i, line in enumerate(lines):
                    if "流程环节" in line:
                        start_idx = i
                    elif start_idx >= 0 and (line.strip() == "" or line.startswith("###") or line.startswith("####")):
                        end_idx = i
                        break
                
                if start_idx >= 0 and end_idx > start_idx:
                    process_section = '\n'.join(lines[start_idx:end_idx])
                    self.logger.info("通过行扫描找到流程章节，长度：%d", len(process_section))
                elif start_idx >= 0:
                    process_section = '\n'.join(lines[start_idx:])
                    self.logger.info("通过行扫描找到流程章节（到文档末尾），长度：%d", len(process_section))
            
            if process_section:
                # 提取步骤
                # 尝试多种步骤格式模式
                step_patterns = [
                    # 模式1: 数字.空格**步骤名称**
                    re.compile(r'(\d+)\.\s+\*\*([^*]+)\*\*'),
                    # 模式2: 数字.空格步骤名称
                    re.compile(r'(\d+)\.\s+([^\n]+)'),
                    # 模式3: **步骤名称**(数字)
                    re.compile(r'\*\*([^*]+)\*\*\s*\((\d+)\)')
                ]
                
                for step_pattern in step_patterns:
                    steps_matches = step_pattern.finditer(process_section)
                    steps_found = False
                    
                    for match in steps_matches:
                        steps_found = True
                        if len(match.groups()) >= 2:
                            if step_pattern.pattern.startswith(r'(\d+)'):
                                # 模式1和2
                                step_number = match.group(1)
                                step_name = match.group(2).strip().replace('**', '')
                            else:
                                # 模式3
                                step_name = match.group(1).strip()
                                step_number = match.group(2)
                            
                            # 查找引用原文
                            original_text = ""
                            try:
                                # 尝试匹配引用原文格式
                                quote_pattern = re.compile(r'(?:\*\*' + re.escape(step_name) + r'\*\*|\b' + re.escape(step_name) + r'\b).*?(?:引用原文|：|:).*?[>"`\']?\s*([^;\n]+)[;\n"`\']?', re.DOTALL | re.IGNORECASE)
                                quote_match = quote_pattern.search(process_section)
                                
                                if quote_match:
                                    original_text = quote_match.group(1).strip()
                                    self.logger.info("步骤 %s 找到引用原文: %s", step_name, original_text[:30] + "..." if len(original_text) > 30 else original_text)
                                else:
                                    # 尝试通过分析步骤周围的内容提取原文
                                    step_content = process_section.split(step_name)[1].split('\n\n')[0] if step_name in process_section else ""
                                    if '>' in step_content:
                                        original_text = step_content.split('>')[1].strip()
                                        if ';' in original_text:
                                            original_text = original_text.split(';')[0].strip()
                                        self.logger.info("步骤 %s 通过上下文分析找到引用原文: %s", step_name, original_text[:30] + "..." if len(original_text) > 30 else original_text)
                            except Exception as e:
                                self.logger.warning(f"提取引用原文失败: {e}")
                            
                            steps.append({
                                "step_number": step_number,
                                "step_name": step_name,
                                "original_text": original_text
                            })
                            self.logger.info("添加步骤: %s - %s", step_number, step_name)
                    
                    # 如果找到了步骤，不再尝试其他模式
                    if steps_found:
                        self.logger.info("使用模式 %s 成功找到 %d 个步骤", step_pattern.pattern, len(steps))
                        break
                
                # 如果没有找到步骤，尝试直接查找数字序号的行
                if not steps:
                    self.logger.info("尝试使用简单行模式提取步骤")
                    line_pattern = re.compile(r'(\d+)[\.、]\s*(.+)')
                    for line in process_section.split('\n'):
                        match = line_pattern.match(line.strip())
                        if match:
                            step_number = match.group(1)
                            step_name = match.group(2).strip()
                            
                            steps.append({
                                "step_number": step_number,
                                "step_name": step_name,
                                "original_text": ""
                            })
                            self.logger.info("添加简单行步骤: %s - %s", step_number, step_name)
            
            # 如果仍然没有找到步骤，尝试直接查找包含"环节"的行
            if not steps and "环节" in text:
                self.logger.info("尝试从全文中提取包含'环节'的行")
                for line in text.split('\n'):
                    if "环节" in line and ":" in line:
                        parts = line.split(":")
                        if len(parts) >= 2:
                            step_name = parts[1].strip()
                            steps.append({
                                "step_number": str(len(steps) + 1),
                                "step_name": step_name,
                                "original_text": ""
                            })
                            self.logger.info("添加包含'环节'的行步骤: %s", step_name)
            
            self.logger.info(f"提取到 {len(steps)} 个流程步骤")
        except Exception as e:
            self.logger.error(f"提取流程步骤失败: {e}")
        
        return steps
    
    def _extract_departments_table(self, text: str) -> List[Dict[str, str]]:
        """从文本中提取部门职责表"""
        departments = []
        try:
            self.logger.info("开始提取部门职责表")
            
            # 查找部门职责表部分
            # 多种模式匹配，提高兼容性
            
            # 查找表格部分
            table_section = None
            patterns = [
                r'(?:部门职责节点表|流程参与部门|部门职责表).*?\n\n\|(.*?)(?:\n\n---|\n\n###|\n\n####|$)',
                r'\|\s*流程环节\s*\|(.*?)(?:\n\n---|\n\n###|\n\n####|$)'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text, re.DOTALL)
                if match:
                    table_section = "|\n|" + match.group(1)
                    self.logger.info(f"使用模式 '{pattern}' 找到部门职责表，长度: {len(table_section)}")
                    break
            
            if not table_section:
                # 尝试扫描查找表格
                lines = text.split('\n')
                table_start = -1
                table_end = -1
                
                for i, line in enumerate(lines):
                    if '部门职责' in line or '职责节点' in line:
                        # 找到可能的表格标题
                        for j in range(i+1, min(i+10, len(lines))):
                            if '|' in lines[j] and ('流程环节' in lines[j] or '部门' in lines[j] or '职责' in lines[j]):
                                table_start = j
                                break
                        if table_start > 0:
                            break
                
                if table_start > 0:
                    # 找到表格结束
                    for i in range(table_start+1, len(lines)):
                        if not lines[i].strip() or not '|' in lines[i]:
                            table_end = i
                            break
                    
                    if table_end > table_start:
                        table_section = '\n'.join(lines[table_start:table_end])
                        self.logger.info(f"通过扫描找到部门职责表，从第{table_start}行到第{table_end}行，长度: {len(table_section)}")
            
            if not table_section and '|' in text:
                # 尝试直接查找表格
                table_lines = []
                capture = False
                
                for line in text.split('\n'):
                    if '|' in line and ('流程环节' in line or '步骤' in line or '节点' in line or '部门' in line):
                        capture = True
                        table_lines.append(line)
                    elif capture and '|' in line:
                        table_lines.append(line)
                    elif capture and not line.strip():
                        # 空行结束表格
                        break
                
                if table_lines:
                    table_section = '\n'.join(table_lines)
                    self.logger.info(f"直接查找找到部门职责表，长度: {len(table_section)}")
            
            if table_section:
                # 解析表格
                rows = table_section.strip().split('\n')
                
                # 识别表头，找到列索引
                header = rows[0] if rows else ""
                step_col = -1
                dept_col = -1
                role_col = -1
                
                # 检查表头中的列名
                headers = [h.strip() for h in header.split('|')]
                for i, h in enumerate(headers):
                    h_lower = h.lower()
                    if any(term in h_lower for term in ['流程环节', '步骤', '节点']):
                        step_col = i
                        self.logger.info(f"找到流程环节列: {i} - {h}")
                    elif any(term in h_lower for term in ['负责部门', '责任部门', '部门']):
                        dept_col = i
                        self.logger.info(f"找到部门列: {i} - {h}")
                    elif any(term in h_lower for term in ['职责类型', '角色', '职责']):
                        role_col = i
                        self.logger.info(f"找到职责类型列: {i} - {h}")
                
                # 如果找不到列索引，尝试基于位置猜测
                if step_col < 0 or dept_col < 0:
                    cols_count = len(headers)
                    if cols_count >= 3:
                        if step_col < 0:
                            step_col = 1  # 通常第一列是流程环节
                            self.logger.info(f"基于位置猜测流程环节列: {step_col}")
                        if dept_col < 0:
                            dept_col = 2  # 通常第二列是部门
                            self.logger.info(f"基于位置猜测部门列: {dept_col}")
                        if role_col < 0 and cols_count >= 4:
                            role_col = 3  # 通常第三列是职责
                            self.logger.info(f"基于位置猜测职责类型列: {role_col}")
                
                # 跳过表头和分隔行
                data_rows = [r for r in rows if r and '---' not in r]
                if len(data_rows) > 1:
                    data_rows = data_rows[1:]
                
                for row in data_rows:
                    cols = [col.strip() for col in row.split('|')]
                    if len(cols) > max(step_col, dept_col, role_col) if max(step_col, dept_col, role_col) >= 0 else 0:
                        try:
                            step_name = cols[step_col] if step_col >= 0 and step_col < len(cols) else ""
                            department = cols[dept_col] if dept_col >= 0 and dept_col < len(cols) else ""
                            role_type = cols[role_col] if role_col >= 0 and role_col < len(cols) else ""
                            
                            if step_name and department:
                                departments.append({
                                    "step_name": step_name,
                                    "department": department,
                                    "role_type": role_type
                                })
                                self.logger.info(f"添加部门职责: {step_name} - {department} - {role_type}")
                        except Exception as e:
                            self.logger.warning(f"解析表格行失败: {e}")
            
            # 如果仍然没有找到部门职责，尝试从文本中提取
            if not departments:
                # 从流程步骤中提取部门职责
                steps_pattern = re.compile(r'(\d+)[\.、]\s*([^（]+)（([^）]+)）')
                for match in steps_pattern.finditer(text):
                    step_name = match.group(2).strip()
                    department = match.group(3).strip()
                    if step_name and department:
                        departments.append({
                            "step_name": step_name,
                            "department": department,
                            "role_type": "负责"
                        })
                        self.logger.info(f"从文本提取部门职责: {step_name} - {department}")
            
            self.logger.info(f"提取到 {len(departments)} 个部门职责")
        except Exception as e:
            self.logger.error(f"提取部门职责表失败: {e}")
        
        return departments
    
    def _create_steps_sheet(self, writer: pd.ExcelWriter, steps_data: List[Dict[str, str]]):
        """创建流程步骤工作表"""
        if not steps_data:
            self.logger.warning("没有流程步骤数据，跳过创建流程步骤工作表")
            return
        
        try:
            # 创建DataFrame
            df = pd.DataFrame(steps_data)
            
            # 重命名列
            df.rename(columns={
                'step_number': '步骤编号',
                'step_name': '步骤名称',
                'original_text': '原文描述'
            }, inplace=True)
            
            # 写入Excel
            df.to_excel(writer, sheet_name='流程步骤', index=False)
            
            # 调整列宽
            self._adjust_column_width(writer, '流程步骤', df)
            
            self.logger.info("成功创建流程步骤工作表")
        except Exception as e:
            self.logger.error(f"创建流程步骤工作表失败: {e}")
    
    def _create_departments_sheet(self, writer: pd.ExcelWriter, departments_data: List[Dict[str, str]]):
        """创建部门职责工作表"""
        if not departments_data:
            self.logger.warning("没有部门职责数据，跳过创建部门职责工作表")
            return
        
        try:
            # 创建DataFrame
            df = pd.DataFrame(departments_data)
            
            # 重命名列
            df.rename(columns={
                'step_name': '流程环节',
                'department': '负责部门',
                'role_type': '职责类型'
            }, inplace=True)
            
            # 写入Excel
            df.to_excel(writer, sheet_name='部门职责', index=False)
            
            # 调整列宽
            self._adjust_column_width(writer, '部门职责', df)
            
            self.logger.info("成功创建部门职责工作表")
        except Exception as e:
            self.logger.error(f"创建部门职责工作表失败: {e}")
    
    def _create_api_data_sheet(self, writer, process_data: Dict[str, Any]):
        """创建API数据表，直接展示API返回的数据"""
        # 检查是否有steps数据
        if 'steps' in process_data and isinstance(process_data['steps'], list) and process_data['steps']:
            # 创建步骤数据的DataFrame
            steps_data = []
            for step in process_data['steps']:
                # 将步骤数据直接转换为字典
                step_dict = {}
                for key, value in step.items():
                    # 处理列表类型的值，转换为字符串
                    if isinstance(value, list):
                        step_dict[key] = ', '.join(map(str, value))
                    else:
                        step_dict[key] = value
                steps_data.append(step_dict)
            
            # 创建DataFrame并写入Excel
            df_steps = pd.DataFrame(steps_data)
            df_steps.to_excel(writer, sheet_name='API数据', index=False)
            
            # 自动调整列宽
            self._adjust_column_width(writer, 'API数据')
        else:
            # 如果没有steps数据，则将整个process_data转换为DataFrame
            # 将嵌套结构扁平化
            flat_data = self._flatten_dict(process_data)
            
            # 创建单行DataFrame
            df = pd.DataFrame([flat_data])
            df.to_excel(writer, sheet_name='API数据', index=False)
            
            # 自动调整列宽
            self._adjust_column_width(writer, 'API数据')
    
    def _adjust_column_width(self, writer, sheet_name, df=None):
        """自动调整列宽"""
        worksheet = writer.sheets[sheet_name]
        if df is None:
            for col in worksheet.columns:
                max_length = 0
                column = col[0].column_letter  # Get the column letter
                for cell in col:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column].width = adjusted_width
        else:
            # 获取列字母
            for i, col in enumerate(df.columns):
                max_length = len(str(col))
                column_letter = openpyxl.utils.get_column_letter(i+1)  # 获取列字母
                for cell in df[col]:
                    try:
                        if len(str(cell)) > max_length:
                            max_length = len(str(cell))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def _flatten_dict(self, d, parent_key='', sep='_'):
        """将嵌套字典扁平化为单层字典"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # 对于列表，将其转换为字符串
                if all(isinstance(item, dict) for item in v):
                    # 如果列表中的所有元素都是字典，则转换为JSON字符串
                    items.append((new_key, json.dumps(v, ensure_ascii=False)))
                else:
                    # 否则简单地将列表转换为字符串
                    items.append((new_key, ', '.join(map(str, v))))
            else:
                items.append((new_key, v))
        return dict(items)


def create_excel_report(process_data: Dict[str, Any], output_path: str) -> str:
    """创建Excel格式的审批流程报告"""
    generator = ExcelGenerator()
    return generator.create_excel_report(process_data, output_path)


if __name__ == "__main__":
    # 测试代码
    test_data = {
        "process_name": "采购审批流程",
        "total_steps": 4,
        "steps": [
            {
                "step_number": 1,
                "description": "员工提交采购申请",
                "approver": "申请人",
                "action": "提交申请",
                "time_limit": "1工作日"
            },
            {
                "step_number": 2,
                "description": "部门经理审批",
                "approver": "部门经理",
                "action": "审批",
                "time_limit": "2工作日"
            }
        ]
    }
    
    create_excel_report(test_data, "test_report.xlsx")
    print("测试Excel报告生成完成") 