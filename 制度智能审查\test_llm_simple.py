#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的LLMProcessor测试脚本
"""

import sys
import os
import logging

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test')

def test_llm_processor():
    """测试LLMProcessor类"""
    try:
        logger.info("开始测试LLMProcessor类...")
        
        # 导入LLMProcessor
        from llm_processor import LLMProcessor
        logger.info("成功导入LLMProcessor类")
        
        # 创建实例
        processor = LLMProcessor()
        logger.info("成功创建LLMProcessor实例")
        
        # 检查是否有_is_sinopec_api_configured方法
        if hasattr(processor, '_is_sinopec_api_configured'):
            logger.info("✓ _is_sinopec_api_configured方法存在")
            
            # 调用方法
            result = processor._is_sinopec_api_configured()
            logger.info(f"✓ _is_sinopec_api_configured方法调用成功，返回: {result}")
        else:
            logger.error("✗ _is_sinopec_api_configured方法不存在")
            return False
        
        # 检查config属性
        if hasattr(processor, 'config'):
            logger.info(f"✓ config属性存在: {type(processor.config)}")
            logger.info(f"  config内容: {processor.config}")
        else:
            logger.error("✗ config属性不存在")
            return False
        
        logger.info("✓ LLMProcessor类测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ LLMProcessor类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_llm_processor()
    if success:
        print("测试成功!")
    else:
        print("测试失败!")
        sys.exit(1) 