{% extends "base.html" %}

{% block title %}API响应查看器{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-code me-2"></i>
                            API响应查看器
                        </h4>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-home me-1"></i>返回首页
                        </a>
                    </div>
                    <p class="mb-0 mt-2">直接查看各个AI提供商的原始API响应数据</p>
                </div>
                <div class="card-body">
                    <!-- 输入区域 -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="testContent" class="form-label">
                                    <i class="fas fa-edit me-1"></i>测试内容
                                </label>
                                <textarea id="testContent" class="form-control" rows="6" placeholder="请输入要分析的制度文本内容...">海南炼化公司域长负责制运行管理细则

第一条 为了规范域长负责制的运行管理，确保各项工作有序进行，特制定本细则。

第二条 域长负责制是指在特定区域内，由域长统一负责该区域的安全、生产、环保等各项工作。

第三条 审批流程：
1. 申请人提交申请材料
2. 直接主管进行初步审核  
3. 域长进行最终审批
4. 相关部门进行备案</textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="apiProvider" class="form-label">
                                    <i class="fas fa-robot me-1"></i>AI提供商
                                </label>
                                <select id="apiProvider" class="form-select">
                                    {% for provider in api_providers %}
                                    <option value="{{ provider.id }}">{{ provider.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mt-3">
                                <button id="testApiBtn" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-play me-2"></i>测试API
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 加载状态 -->
                    <div id="loadingIndicator" class="text-center mb-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">正在调用API，请稍候...</p>
                    </div>

                    <!-- 响应展示区域 -->
                    <div id="responseContainer" style="display: none;">
                        <!-- API信息概览 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h5 id="providerName">-</h5>
                                        <small>AI提供商</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5 id="processingTime">-</h5>
                                        <small>处理耗时</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h5 id="contentLength">-</h5>
                                        <small>内容长度</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-secondary text-white">
                                    <div class="card-body text-center">
                                        <h5 id="responseLength">-</h5>
                                        <small>响应长度</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 详细响应数据 -->
                        <div class="accordion" id="responseAccordion">
                            <!-- 请求数据 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="requestHeader">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#requestCollapse">
                                        <i class="fas fa-arrow-up me-2"></i>
                                        发送的数据
                                        <span id="requestBadge" class="badge bg-primary ms-2">-</span>
                                    </button>
                                </h2>
                                <div id="requestCollapse" class="accordion-collapse collapse show" data-bs-parent="#responseAccordion">
                                    <div class="accordion-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted" id="requestInfo">-</small>
                                            <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('requestContent')">
                                                <i class="fas fa-copy me-1"></i>复制
                                            </button>
                                        </div>
                                        <pre id="requestContent" class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-size: 0.85em;">-</pre>
                                        <div id="requestNote" class="alert alert-info mt-3" style="display: none;">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <span id="requestNoteText">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 原始响应 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="responseHeader">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#responseCollapse">
                                        <i class="fas fa-arrow-down me-2"></i>
                                        API原始响应
                                        <span id="responseBadge" class="badge bg-success ms-2">-</span>
                                    </button>
                                </h2>
                                <div id="responseCollapse" class="accordion-collapse collapse" data-bs-parent="#responseAccordion">
                                    <div class="accordion-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-server me-1"></i>
                                                <span id="responseInfo">来源: API响应</span>
                                            </small>
                                            <button class="btn btn-sm btn-outline-success" onclick="copyToClipboard('responseContent')">
                                                <i class="fas fa-copy me-1"></i>复制
                                            </button>
                                        </div>
                                        <pre id="responseContent" class="bg-dark text-light p-3 rounded" style="max-height: 500px; overflow-y: auto; font-size: 0.85em;">-</pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 错误信息 -->
                    <div id="errorContainer" class="alert alert-danger" style="display: none;">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>API调用失败</h5>
                        <p id="errorMessage">-</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 复制到剪贴板功能
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(() => {
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>已复制';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-primary', 'btn-outline-success');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(err => {
        alert('复制失败，请手动选择文本复制');
    });
}

// 格式化JSON
function formatJSON(jsonString) {
    try {
        const obj = JSON.parse(jsonString);
        return JSON.stringify(obj, null, 2);
    } catch (e) {
        return jsonString;
    }
}

// 测试API
async function testAPI() {
    const content = document.getElementById('testContent').value.trim();
    const provider = document.getElementById('apiProvider').value;
    
    if (!content) {
        alert('请输入测试内容');
        return;
    }
    
    // 显示加载状态
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('responseContainer').style.display = 'none';
    document.getElementById('errorContainer').style.display = 'none';
    
    // 禁用按钮
    const btn = document.getElementById('testApiBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';
    
    try {
        const response = await fetch('/api/raw_response', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: content,
                llm_provider: provider
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayAPIResponse(result);
        } else {
            displayError(result.error || '未知错误');
        }
        
    } catch (error) {
        displayError('网络错误: ' + error.message);
    } finally {
        document.getElementById('loadingIndicator').style.display = 'none';
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-play me-2"></i>测试API';
    }
}

// 显示API响应
function displayAPIResponse(result) {
    // 更新概览信息
    document.getElementById('providerName').textContent = result.provider || '-';
    document.getElementById('processingTime').textContent = result.processing_time || '-';
    document.getElementById('contentLength').textContent = (result.content_length || 0) + ' 字符';
    document.getElementById('responseLength').textContent = (result.response_length || 0) + ' 字符';
    
    // 更新请求数据
    document.getElementById('requestBadge').textContent = (result.content_length || 0) + ' 字符';
    document.getElementById('requestContent').textContent = result.content_preview || result.content || '-';
    document.getElementById('requestInfo').textContent = result.content_info || '发送的内容';
    
    // 显示说明信息
    if (result.note) {
        document.getElementById('requestNote').style.display = 'block';
        document.getElementById('requestNoteText').textContent = result.note;
    } else {
        document.getElementById('requestNote').style.display = 'none';
    }
    
    // 更新原始响应
    document.getElementById('responseBadge').textContent = (result.response_length || 0) + ' 字符';
    document.getElementById('responseContent').textContent = formatJSON(result.raw_response || '-');
    document.getElementById('responseInfo').textContent = `来源: ${result.provider || '未知'} API`;
    
    // 显示响应容器
    document.getElementById('responseContainer').style.display = 'block';
}

// 显示错误
function displayError(errorMessage) {
    document.getElementById('errorMessage').textContent = errorMessage;
    document.getElementById('errorContainer').style.display = 'block';
}

// 事件监听
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('testApiBtn').addEventListener('click', testAPI);
    
    // 快捷键支持
    document.getElementById('testContent').addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            testAPI();
        }
    });
});
</script>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

#testContent {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}
</style>
{% endblock %} 