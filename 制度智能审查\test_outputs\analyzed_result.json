{"success": true, "file_path": "test_outputs/raw_response_20250619_150524.txt", "text_content": "### 输出结果  \n\n#### 1. 审批流程环节（按顺序列出）  \n引用原文：  \n> 第五条 运行管理机制  \n> （一）域长提出变更申请  \n> （二）部门主管进行初审  \n> （三）安全生产部进行合规性审核  \n> （四）总经理办公会审批  \n> （五）人力资源部备案  \n\n**流程环节顺序**：  \n1. 提出变更申请  \n2. 初审  \n3. 合规性审核  \n4. 审批  \n5. 备案  \n\n---  \n\n#### 2. 部门职责节点表  \n\n| 流程环节         | 负责部门       | 职责类型       |  \n|------------------|----------------|----------------|  \n| 提出变更申请     | 域长           | 发起端         |  \n| 初审             | 部门主管       | 审核端         |  \n| 合规性审核       | 安全生产部     | 审核端         |  \n| 审批             | 总经理办公会   | 确认端         |  \n| 备案             | 人力资源部     | 协作端（存档） |  \n\n---  \n\n### 说明  \n- **职责类型分类依据**：  \n  - **发起端**：域长作为流程起点提出申请，符合“启动流程”特征。  \n  - **审核端**：部门主管和安全生产部分别进行内容初审与合规性审核，属于逐级核查。  \n  - **确认端**：总经理办公会拥有最终决策权（“审批”），对应签字生效环节。  \n  - **协作端**：人力资源部不参与决策但需存档备案，属于流程支持角色。  \n\n- **关键词匹配**：  \n  - “申请”“初审”“审核”“审批”“备案”均直接对应核心审批词汇。  \n  - 无隐性节点需补充。", "process_steps": [{"step_number": "1", "step_name": "提出变更申请", "original_text": ""}, {"step_number": "2", "step_name": "初审", "original_text": ""}, {"step_number": "3", "step_name": "合规性审核", "original_text": ""}, {"step_number": "4", "step_name": "审批", "original_text": ""}, {"step_number": "5", "step_name": "备案", "original_text": ""}], "departments": [], "errors": []}