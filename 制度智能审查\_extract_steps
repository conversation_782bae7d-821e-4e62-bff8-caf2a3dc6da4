from typing import List, Dict
import re

class ProcessExtractor:
    def _extract_steps(self, text: str) -> List[Dict[str, str]]:
        """从文本中提取流程步骤"""
        steps = []
        try:
            # 查找流程步骤部分
            # 多种模式匹配，提高兼容性
            
            # 模式1: 查找"审批流程环节"部分
            process_section = None
            patterns = [
                r'审批流程环节.*?\n(.*?)(?:\n\n---|\n\n###|$)',
                r'流程环节.*?\n(.*?)(?:\n\n---|\n\n###|$)',
                r'[0-9]+\.\s+\*\*([^*]+)\*\*'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text, re.DOTALL)
                if match:
                    if pattern.startswith('[0-9]+'):
                        # 直接搜索步骤的模式
                        process_section = text
                    else:
                        # 搜索章节的模式
                        process_section = match.group(1)
                    break
            
            if process_section:
                # 提取步骤
                # 尝试多种步骤格式模式
                step_patterns = [
                    # 模式1: 数字.空格**步骤名称**
                    re.compile(r'(\d+)\.\s+\*\*([^*]+)\*\*'),
                    # 模式2: 数字.空格步骤名称
                    re.compile(r'(\d+)\.\s+([^\n]+)'),
                    # 模式3: **步骤名称**(数字)
                    re.compile(r'\*\*([^*]+)\*\*\s*\((\d+)\)')
                ]
                
                for step_pattern in step_patterns:
                    steps_matches = step_pattern.finditer(process_section)
                    steps_found = False
                    
                    for match in steps_matches:
                        steps_found = True
                        if len(match.groups()) >= 2:
                            if step_pattern.pattern.startswith(r'(\d+)'):
                                # 模式1和2
                                step_number = match.group(1)
                                step_name = match.group(2).strip()
                            else:
                                # 模式3
                                step_name = match.group(1).strip()
                                step_number = match.group(2)
                            
                            # 查找引用原文
                            original_text = ""
                            try:
                                # 尝试多种引用格式
                                quote_patterns = [
                                    re.compile(r'\*\*' + re.escape(step_name) + r'\*\*.*?>\s+([^;]+);', re.DOTALL),
                                    re.compile(r'\*\*' + re.escape(step_name) + r'\*\*.*?`([^`]+)`', re.DOTALL),
                                    re.compile(r'' + re.escape(step_name) + r'.*?引用原文.*?[`\'"]([^`\'"]+)[`\'"]', re.DOTALL)
                                ]
                                
                                for quote_pattern in quote_patterns:
                                    quote_match = quote_pattern.search(process_section)
                                    if quote_match:
                                        original_text = quote_match.group(1).strip()
                                        break
                            except Exception as e:
                                self.logger.warning(f"提取引用原文失败: {e}")
                            
                            steps.append({
                                "step_number": step_number,
                                "step_name": step_name,
                                "original_text": original_text
                            })
                    
                    # 如果找到了步骤，不再尝试其他模式
                    if steps_found:
                        break
                
                # 如果没有找到步骤，尝试直接查找数字序号的行
                if not steps:
                    line_pattern = re.compile(r'(\d+)[\.、]\s*(.+)')
                    for line in process_section.split('\n'):
                        match = line_pattern.match(line.strip())
                        if match:
                            step_number = match.group(1)
                            step_name = match.group(2).strip()
                            
                            steps.append({
                                "step_number": step_number,
                                "step_name": step_name,
                                "original_text": ""
                            })
            
            self.logger.info(f"提取到 {len(steps)} 个流程步骤")
        except Exception as e:
            self.logger.error(f"提取流程步骤失败: {e}")
        
        return steps 