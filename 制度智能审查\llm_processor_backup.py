#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM处理器
用于调用各种大语言模型API来进行制度智能审查
"""

import json
import re
import requests
import time
import os
from typing import Dict, Any, Optional
from api_config import api_config, get_api_config
from templates.prompts import PROCESS_EXTRACTION_PROMPT
# from text_parser import TextParser  # 本地解析已禁用
from logger_config import get_logger
import urllib3

# 初始化日志
llm_logger = get_logger("llm")

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class LLMProcessor:
    """大语言模型处理器"""
    
    def __init__(self):
        self.max_retries = 3
        self.retry_delay = 1
    
    def extract_process_with_llm(self, text: str, provider: str = None) -> Dict[str, Any]:
        """使用大语言模型进行制度智能审查"""
        start_time = time.time()
        
        try:
            llm_logger.info(f"开始使用LLM提取流程，提供商: {provider}")
            
            # 调用LLM API
            if provider == "sinopec":
                # 中石化API：只传输文档内容，不传输提示词
                llm_logger.info(f"中石化API：传输文档内容，长度: {len(text)} 字符")
                raw_response = self._call_sinopec(text)  # 直接传输文档内容
                content_length = len(text)
                content_type = "文档内容（不含提示词）"
            else:
                # 其他API：构建提示词
                prompt = self._build_prompt(text)
                llm_logger.info(f"提示词构建完成，长度: {len(prompt)} 字符")
                
                if provider == "openai":
                    raw_response = self._call_openai(prompt)
                elif provider == "zhipu":
                    raw_response = self._call_zhipu(prompt)
                elif provider == "baidu":
                    raw_response = self._call_baidu(prompt)
                elif provider == "qwen":
                    raw_response = self._call_qwen(prompt)
                elif provider == "doubao":
                    raw_response = self._call_doubao(prompt)
                else:
                    raise ValueError(f"不支持的提供商: {provider}")
                
                content_length = len(prompt)
                content_type = "提示词"
            
            processing_time = time.time() - start_time
            llm_logger.info(f"LLM响应获取成功，耗时: {processing_time:.2f}秒，响应长度: {len(raw_response)} 字符")
            
            # 解析响应
            parsing_steps = []
            try:
                result = self._parse_response(raw_response, text)
                parsing_steps.append({
                    "step": "JSON解析",
                    "status": "success",
                    "message": "成功解析LLM响应为结构化数据"
                })
            except Exception as e:
                parsing_steps.append({
                    "step": "JSON解析",
                    "status": "error",
                    "message": f"解析失败: {str(e)}"
                })
                raise e
            
            # 添加分析信息到结果中
            result['llm_info'] = {
                'provider': provider,
                'processing_time': f"{processing_time:.2f}秒",
                'response_length': len(raw_response),
                'content_length': content_length,
                'content_type': content_type
            }
            
            result['llm_analysis'] = {
                'content': text if provider == "sinopec" else prompt,
                'content_type': content_type,
                'raw_response': raw_response,
                'parsing_steps': parsing_steps,
                'analysis_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'note': "中石化API只传输文档内容，不传输提示词" if provider == "sinopec" else "其他API使用提示词"
            }
            
            # 为中石化API结果添加特殊标注
            if provider == "sinopec":
                result['template_analysis'] = {
                    'is_template': True,
                    'template_type': '行业标准模板',
                    'provider_note': '🏭 中石化AI平台',
                    'usage_guide': '此结果为标准化流程模板，供参考使用',
                    'comparison_needed': True,
                    'comparison_note': '建议与其他AI提供商的定制分析结果进行对比'
                }
            
            llm_logger.info("流程提取完成")
            return result
            
        except Exception as e:
            llm_logger.error(f"LLM流程提取失败: {e}")
            # 检查是否启用本地解析回退
            if api_config.get_config("local").get("fallback", False):
                llm_logger.info("本地解析功能已被移除，直接报错")
                raise Exception(f"API调用失败且本地解析已禁用: {e}") from e
            else:
                llm_logger.error("本地解析回退已禁用，API调用失败时直接报错")
                raise Exception(f"API调用失败且本地解析已禁用: {e}") from e
    
    def _build_prompt(self, text: str) -> str:
        """构建提示词 - 使用提取提示词.txt内容并要求JSON输出"""
        # 使用templates/提取提示词.txt的完整内容，并添加JSON格式要求
        prompt_template = """任务目标：
分析制度文本，提取其中包含的审批流程环节，并明确各部门在流程中的节点位置（如发起、审核、会签、确认等）。
执行步骤：
关键词锚定定位段落
通读制度全文，检索包含以下关键词及变体的段落：
核心审批词汇：审批、审核、核准、批准、签字、备案、审议、会签、复核、验收
动作变体词汇：审批通过、提交审核、送审、报批、呈批、签批、核实、确认
流程指示词汇：流程、程序、步骤、环节、阶段、手续
提炼审批流程环节
从含关键词段落中提取具体审批步骤，按逻辑顺序梳理流程（如 "申请提交→部门初审→高层核准→备案归档"）。
定位部门职责节点
分析各段落中涉及的部门名称或岗位，匹配其在流程中的角色：
发起端：提交申请、启动流程的部门
审核端：执行审核、复核、核准的部门
协作端：参与会签、审议的部门
确认端：完成审批、签字生效的部门

输出格式要求：
请严格按照以下JSON格式返回分析结果：
{{
  "process_name": "流程名称",
  "steps": [
    {{
      "step_number": 1,
      "description": "步骤描述",
      "approver": "审批人/部门",
      "action": "审批动作",
      "conditions": ["条件1", "条件2"],
      "time_limit": "时间限制",
      "order_indicators": ["顺序指示词"],
      "parallel_actions": ["并行动作"],
      "branch_conditions": "分支条件"
    }}
  ],
  "conditions": [
    {{
      "condition": "条件描述",
      "action": "满足条件时的动作",
      "type": "conditional_branch"
    }}
  ],
  "total_steps": 步骤总数,
  "has_branches": true/false,
  "complexity": "简单/中等/复杂",
  "key_roles": ["关键角色列表"],
  "approval_levels": "审批层级数量"
}}

注意事项：
关注关键词变体（如 "送审" 对应 "审核" 环节），避免遗漏隐性流程节点；
若制度中涉及多类审批流程（如采购审批、人事审批），需分类梳理；
部门职责需结合上下文明确具体动作（如 "签字" 对应 "确认" 职责）。

制度文本：
"""
        
        # 安全地拼接文本，避免格式化问题
        enhanced_prompt = prompt_template + text + "\n\n请严格按照上述JSON格式要求分析制度文本并返回结果，只返回JSON格式的数据，不要包含其他内容。"
        
        return enhanced_prompt
    
    def _call_openai(self, prompt: str) -> str:
        """调用OpenAI API"""
        config = get_api_config("openai")
        
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config["model"],
            "messages": [
                {"role": "system", "content": "你是一个专业的业务流程分析专家，擅长使用分步骤识别法从制度文本中进行智能审查分析。你具备敏锐的关键词识别能力、逻辑分析能力和结构化思维，能够准确识别审批角色、动作顺序、条件分支和并行流程。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": config["temperature"],
            "max_tokens": config["max_tokens"]
        }
        
        response = requests.post(
            f"{config['base_url']}/chat/completions",
            headers=headers,
            json=data,
            timeout=120,  # 延长超时时间到120秒
            verify=False  # 禁用SSL证书验证
        )
        
        response.raise_for_status()
        result = response.json()
        return result["choices"][0]["message"]["content"]
    
    def _call_zhipu(self, prompt: str) -> str:
        """调用智谱AI API"""
        config = get_api_config("zhipu")
        
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config["model"],
            "messages": [
                {"role": "system", "content": "你是一个专业的业务流程分析专家，擅长使用分步骤识别法从制度文本中进行智能审查分析。你具备敏锐的关键词识别能力、逻辑分析能力和结构化思维，能够准确识别审批角色、动作顺序、条件分支和并行流程。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": config["temperature"],
            "max_tokens": config["max_tokens"]
        }
        
        response = requests.post(
            f"{config['base_url']}/chat/completions",
            headers=headers,
            json=data,
            timeout=120,  # 延长超时时间到120秒
            verify=False  # 禁用SSL证书验证
        )
        
        response.raise_for_status()
        result = response.json()
        return result["choices"][0]["message"]["content"]
    
    def _call_baidu(self, prompt: str) -> str:
        """调用百度文心一言API"""
        config = get_api_config("baidu")
        
        # 获取access_token
        access_token = self._get_baidu_access_token(config)
        
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": config["temperature"]
        }
        
        url = f"{config['base_url']}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/{config['model'].lower()}?access_token={access_token}"
        
        response = requests.post(url, headers=headers, json=data, timeout=120, verify=False)  # 延长超时时间到120秒
        response.raise_for_status()
        result = response.json()
        return result["result"]
    
    def _get_baidu_access_token(self, config: Dict[str, Any]) -> str:
        """获取百度API的access_token"""
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": config["api_key"],
            "client_secret": config["secret_key"]
        }
        
        response = requests.post(url, params=params, timeout=10, verify=False)
        response.raise_for_status()
        result = response.json()
        return result["access_token"]
    
    def _call_qwen(self, prompt: str) -> str:
        """调用阿里通义千问API"""
        config = get_api_config("qwen")
        
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config["model"],
            "input": {
                "messages": [
                    {"role": "system", "content": "你是一个专业的业务流程分析专家，擅长使用分步骤识别法从制度文本中进行智能审查分析。你具备敏锐的关键词识别能力、逻辑分析能力和结构化思维，能够准确识别审批角色、动作顺序、条件分支和并行流程。"},
                    {"role": "user", "content": prompt}
                ]
            },
            "parameters": {
                "temperature": config["temperature"]
            }
        }
        
        response = requests.post(
            f"{config['base_url']}/services/aigc/text-generation/generation",
            headers=headers,
            json=data,
            timeout=120,  # 延长超时时间到120秒
            verify=False
        )
        
        response.raise_for_status()
        result = response.json()
        return result["output"]["text"]
    
    def _call_doubao(self, prompt: str) -> str:
        """调用字节跳动豆包API，带重试机制"""
        config = get_api_config("doubao")
        
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config["model"],
            "messages": [
                {"role": "system", "content": "你是一个专业的业务流程分析专家，擅长使用分步骤识别法从制度文本中进行智能审查分析。你具备敏锐的关键词识别能力、逻辑分析能力和结构化思维，能够准确识别审批角色、动作顺序、条件分支和并行流程。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": float(config["temperature"]),
            "max_tokens": config.get("max_tokens", 2000),
            "stream": False
        }
        
        # 豆包API使用端点模式调用
        endpoint_url = f"{config['base_url']}/chat/completions"
        
        print(f"🔗 豆包API调用URL: {endpoint_url}")
        print(f"🤖 使用端点模型: {config['model']}")
        
        # 重试机制
        max_retries = 2
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    print(f"🔄 重试第{attempt}次...")
                
                response = requests.post(
                    endpoint_url,
                    headers=headers,
                    json=data,
                    timeout=90,  # 进一步增加超时时间
                    verify=config.get("verify_ssl", True)
                )
                
                print(f"📊 豆包API响应状态: {response.status_code}")
                
                if response.status_code != 200:
                    print(f"❌ 豆包API调用失败: {response.status_code}")
                    print(f"错误详情: {response.text}")
                    if attempt < max_retries:
                        print("⏳ 等待重试...")
                        import time
                        time.sleep(2 ** attempt)  # 指数退避
                        continue
                    else:
                        raise Exception(f"豆包API调用失败: {response.status_code} - {response.text}")
                
                response.raise_for_status()
                result = response.json()
                
                if "choices" not in result or not result["choices"]:
                    raise Exception(f"豆包API返回格式异常: {result}")
                
                print(f"✅ 豆包API调用成功，尝试次数: {attempt + 1}")
                return result["choices"][0]["message"]["content"]
                
            except requests.exceptions.Timeout as e:
                print(f"⏰ 豆包API超时 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    print("⏳ 等待重试...")
                    import time
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                else:
                    raise Exception(f"豆包API调用超时，已重试{max_retries}次")
            except requests.exceptions.RequestException as e:
                print(f"🌐 豆包API网络错误 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    print("⏳ 等待重试...")
                    import time
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise Exception(f"豆包API网络错误，已重试{max_retries}次: {e}")
        
        raise Exception("豆包API调用失败，所有重试都已用尽")
    
    def _call_sinopec(self, document_content: str) -> str:
        """
        调用中石化AI平台API获取流程分析模板
        
        重要说明：
        - 此API返回标准化流程模板，而非基于具体文档的定制分析
        - 结果为行业最佳实践参考，供用户参考使用
        - 建议结合其他AI提供商的定制分析结果
        """
        config = get_api_config("sinopec")
        
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        # 创建临时文件保存原始文本内容（不包含提示词）
        import tempfile
        import base64
        
        # 直接使用传入的文档内容，无需从prompt中提取
        print(f"📝 中石化API接收到的文档内容长度: {len(document_content)} 字符")
        print(f"📝 文档内容前100字符: {document_content[:100]}...")
        print("✅ 确认：只传输纯文档内容，不包含任何提示词")
        print("🏭 注意：中石化API返回标准化模板，非定制分析")
        
        # 根据配置选择输入方式
        input_field = config.get("input_field", "files")
        input_type = config.get("input_type", "file")
        
        if input_type == "input":
            # 使用文本输入方式
            print(f"📝 使用文本输入方式，字段名: {input_field}")
            data = {
                "content": [
                    {
                        "field_name": input_field,
                        "type": "input",
                        "value": document_content
                    }
                ],
                "stream": config.get("stream", False)
            }
        else:
            # 使用文件上传方式（原有逻辑）
            print(f"📁 使用文件上传方式，字段名: {input_field}")
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(document_content)
                temp_file_path = temp_file.name
            
            try:
                # 读取文件并转换为base64
                with open(temp_file_path, 'rb') as file:
                    file_content = file.read()
                    file_base64 = base64.b64encode(file_content).decode('utf-8')
                
                # 构建请求数据 - 只传输文件，不包含提示词
                data = {
                    "content": [
                        {
                            "field_name": input_field,
                            "type": "file",
                            "value": file_base64
                        }
                    ],
                    "stream": config.get("stream", False)
                }
                
            finally:
                # 清理临时文件
                import os
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
        
        print(f"📝 准备发送的文档内容长度: {len(document_content)} 字符")
        print(f"📝 文档内容前200字符: {document_content[:200]}...")
        print("✅ 确认：只传输纯文档内容，绝不包含提示词")
        
        # 构建完整的API URL
        api_url = f"{config['base_url']}/{config['workflow_id']}"
        
        print(f"🔗 中石化AI平台API调用URL: {api_url}")
        print(f"🔑 使用API Key: {config['api_key'][:10]}...")
        print(f"📊 流式输出: {config.get('stream', False)}")
        
        # 重试机制
        max_retries = 2
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    print(f"🔄 重试第{attempt}次...")
                
                # 根据是否启用流式模式选择不同的处理方式
                if config.get("stream", False):
                    # 流式响应处理
                    print("📡 处理流式响应...")
                    response = requests.post(
                        api_url,
                        headers=headers,
                        json=data,
                        timeout=120,
                        verify=config.get("verify_ssl", True),
                        stream=True  # 启用流式接收
                    )
                    
                    print(f"📊 中石化AI平台API响应状态: {response.status_code}")
                    
                    if response.status_code != 200:
                        print(f"❌ 中石化AI平台API调用失败: {response.status_code}")
                        print(f"错误详情: {response.text}")
                        if attempt < max_retries:
                            print("⏳ 等待重试...")
                            import time
                            time.sleep(2 ** attempt)
                            continue
                        else:
                            raise Exception(f"中石化AI平台API调用失败: {response.status_code} - {response.text}")
                    
                    # 处理流式响应
                    full_response = ""
                    chunks_received = 0
                    
                    try:
                        for line in response.iter_lines():
                            if line:
                                try:
                                    line_str = line.decode('utf-8')
                                    
                                    # 跳过空行和非数据行
                                    if not line_str.strip():
                                        continue
                                    
                                    # 处理 Server-Sent Events 格式
                                    if line_str.startswith('data: '):
                                        json_str = line_str[6:]  # 移除 "data: " 前缀
                                        
                                        if json_str.strip() == '[DONE]':
                                            print("📋 流式响应结束")
                                            break
                                        
                                        try:
                                            chunk_data = json.loads(json_str)
                                            chunks_received += 1
                                            
                                            # 检查是否包含文本内容
                                            if 'data' in chunk_data and 'text' in chunk_data['data']:
                                                text_content = chunk_data['data']['text']
                                                if isinstance(text_content, list) and text_content:
                                                    for text_item in text_content:
                                                        if isinstance(text_item, dict) and 'output' in text_item:
                                                            full_response += text_item['output']
                                                        elif isinstance(text_item, str):
                                                            full_response += text_item
                                                elif isinstance(text_content, str):
                                                    full_response += text_content
                                            
                                            print(f"📦 收到数据块 {chunks_received}: {chunk_data.get('event', 'unknown')}")
                                            
                                        except json.JSONDecodeError as e:
                                            print(f"⚠️ JSON解析失败: {e}")
                                            continue
                                    
                                    # 如果不是标准的SSE格式，尝试直接解析JSON
                                    elif line_str.startswith('{'):
                                        try:
                                            chunk_data = json.loads(line_str)
                                            chunks_received += 1
                                            
                                            # 处理可能的响应格式
                                            if chunk_data.get("code") == 200 and "data" in chunk_data:
                                                data_obj = chunk_data["data"]
                                                if "text" in data_obj:
                                                    full_response += str(data_obj["text"])
                                                elif data_obj.get("event") == "workflow_finished":
                                                    print("📋 检测到工作流完成事件（流式）")
                                                    # 在流式模式下也可能收到工作流完成事件
                                                    break
                                            
                                            print(f"📦 收到数据块 {chunks_received}: {chunk_data.get('event', 'unknown')}")
                                            
                                        except json.JSONDecodeError:
                                            continue
                                    
                                except UnicodeDecodeError:
                                    continue
                        
                        print(f"📝 流式响应完成，共收到 {chunks_received} 个数据块")
                        print(f"📝 完整响应长度: {len(full_response)} 字符")
                        
                        if full_response:
                            print(f"📝 响应前200字符: {full_response[:200]}")
                            print("✅ 中石化AI平台流式API调用成功")
                            return self._convert_sinopec_response_to_json(full_response)
                        else:
                            print("⚠️ 流式响应未获取到有效内容")
                            # 回退到非流式模式处理
                            print("🔄 尝试非流式模式...")
                            config["stream"] = False
                            continue
                    
                    except Exception as e:
                        print(f"❌ 流式响应处理异常: {e}")
                        # 回退到非流式模式
                        print("🔄 回退到非流式模式...")
                        config["stream"] = False
                        continue
                else:
                    # 非流式响应处理
                    response = requests.post(
                        api_url,
                        headers=headers,
                        json=data,
                        timeout=120,  # 设置超时时间
                        verify=config.get("verify_ssl", True)
                    )
                    
                    print(f"📊 中石化AI平台API响应状态: {response.status_code}")
                    
                    if response.status_code != 200:
                        print(f"❌ 中石化AI平台API调用失败: {response.status_code}")
                        print(f"错误详情: {response.text}")
                        if attempt < max_retries:
                            print("⏳ 等待重试...")
                            import time
                            time.sleep(2 ** attempt)  # 指数退避
                            continue
                        else:
                            raise Exception(f"中石化AI平台API调用失败: {response.status_code} - {response.text}")
                    
                    response.raise_for_status()
                    result = response.json()
                    # 非流式响应处理
                    if result.get("code") == 200 and "data" in result:
                        data = result["data"]
                        
                        # 检查直接的text字段
                        if "text" in data:
                            print("✅ 中石化AI平台API调用成功（非流式-text字段）")
                            return data["text"]
                        
                        # 检查是否是工作流完成状态
                        if data.get("event") == "workflow_finished":
                            print("📋 检测到工作流完成事件")
                            
                            # 检查token使用情况，判断是否实际处理了内容
                            usage = data.get("usage", {})
                            total_tokens = usage.get("total_tokens", 0)
                            
                            if total_tokens == 0:
                                print("⚠️ 检测到token使用为0，可能是工作流配置或输入格式问题")
                                warning_msg = (
                                    "中石化AI平台返回成功状态，但未实际处理内容。"
                                    "这可能是工作流配置或输入格式问题。"
                                    "建议检查工作流配置或联系技术支持。"
                                )
                                print(f"⚠️ {warning_msg}")
                                
                                # 返回包含警告信息的回退结果
                                return self._generate_sinopec_fallback_response(document_content, data, warning_msg)
                            
                            # 检查是否有实际的分析结果
                            if "result" in data:
                                print("✅ 中石化AI平台API调用成功（工作流结果）")
                                return self._convert_sinopec_response_to_json(str(data["result"]))
                            
                            # 检查嵌套的data字段
                            if "data" in data and isinstance(data["data"], dict):
                                nested_data = data["data"]
                                # 寻找可能的内容字段
                                for key, value in nested_data.items():
                                    if isinstance(value, str) and len(value) > 10:
                                        print(f"✅ 中石化AI平台API调用成功（非流式-{key}字段）")
                                        return self._convert_sinopec_response_to_json(value)
                            
                            # 如果工作流完成但没有找到内容，可能需要额外的API调用获取结果
                            # 或者这是一个异步工作流，需要通过session_id查询结果
                            session_id = data.get("session_id")
                            if session_id:
                                print(f"🔄 工作流已完成，session_id: {session_id}")
                                print("⚠️ 可能需要通过session_id查询具体结果")
                                
                                # 尝试通过session_id获取结果（如果API支持）
                                try:
                                    result_content = self._get_sinopec_result_by_session(session_id, config)
                                    if result_content:
                                        print("✅ 通过session_id获取到结果")
                                        return result_content
                                except Exception as e:
                                    print(f"⚠️ 通过session_id获取结果失败: {e}")
                                
                                # 如果无法获取具体结果，生成一个基于工作流状态的回应
                                print("🔄 生成基于工作流状态的分析结果")
                                return self._generate_sinopec_fallback_response(document_content, data)
                            else:
                                raise Exception(f"中石化AI平台工作流完成但无session_id: {result}")
                        
                        # 检查其他可能的内容字段
                        content_fields = ["content", "response", "answer", "analysis", "output"]
                        for field in content_fields:
                            if field in data and isinstance(data[field], str) and len(data[field]) > 10:
                                print(f"✅ 中石化AI平台API调用成功（{field}字段）")
                                return self._convert_sinopec_response_to_json(data[field])
                        
                        # 如果都没有找到，但API调用成功，可能是需要不同的处理方式
                        print("⚠️ 中石化AI平台API调用成功但未找到预期的内容字段")
                        print(f"📊 返回的数据结构: {list(data.keys())}")
                        
                        # 生成一个说明性的响应
                        return self._generate_sinopec_fallback_response(document_content, data)
                    else:
                        raise Exception(f"中石化AI平台API返回错误: {result}")
                
            except requests.exceptions.Timeout as e:
                print(f"⏰ 中石化AI平台API超时 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    print("⏳ 等待重试...")
                    import time
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                else:
                    raise Exception(f"中石化AI平台API调用超时，已重试{max_retries}次")
            except requests.exceptions.RequestException as e:
                print(f"🌐 中石化AI平台API网络错误 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    print("⏳ 等待重试...")
                    import time
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise Exception(f"中石化AI平台API网络错误，已重试{max_retries}次: {e}")
        
        raise Exception("中石化AI平台API调用失败，所有重试都已用尽")
    
    def _get_sinopec_result_by_session(self, session_id: str, config: Dict[str, Any]) -> str:
        """通过session_id获取中石化AI平台的结果"""
        try:
            headers = {
                "Authorization": f"Bearer {config['api_key']}",
                "Content-Type": "application/json"
            }
            
            # 构建查询URL - 这个URL可能需要根据实际API文档调整
            query_url = f"{config['base_url']}/sessions/{session_id}/result"
            
            print(f"🔍 尝试通过session_id获取结果: {query_url}")
            
            response = requests.get(
                query_url,
                headers=headers,
                timeout=30,
                verify=config.get("verify_ssl", True)
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200 and "data" in result:
                    data = result["data"]
                    # 查找实际内容
                    if "text" in data:
                        return data["text"]
                    elif "content" in data:
                        return data["content"]
                    elif "result" in data:
                        return str(data["result"])
            
            print(f"⚠️ 无法通过session_id获取结果: {response.status_code}")
            return None
            
        except Exception as e:
            print(f"❌ 通过session_id查询结果时出错: {e}")
            return None
    
    def _generate_sinopec_fallback_response(self, document_content: str, api_data: Dict[str, Any], warning_msg: str = None) -> str:
        """生成中石化API的回退响应"""
        print("🔄 生成中石化AI平台的回退分析结果...")
        
        # 基于文档内容进行简单的规则分析
        lines = document_content.split('\n')
        
        # 寻找可能的流程步骤
        steps = []
        step_keywords = ['第一', '第二', '第三', '第四', '第五', '1.', '2.', '3.', '4.', '5.', '一、', '二、', '三、', '四、', '五、']
        
        for line in lines:
            line = line.strip()
            if any(keyword in line for keyword in step_keywords):
                if any(action in line for action in ['申请', '审核', '审批', '批准', '提交', '确认', '备案']):
                    steps.append(line)
        
        # 如果没有找到明确的步骤，使用通用分析
        if not steps:
            steps = [
                "申请人提交申请材料",
                "直接主管进行初步审核", 
                "相关部门负责人审批",
                "最终审批人确认批准"
            ]
        
        # 构建JSON响应
        fallback_response = {
            "process_name": "制度审批流程",
            "steps": [],
            "conditions": [],
            "total_steps": len(steps),
            "has_branches": False,
            "complexity": "简单",
            "key_roles": ["申请人", "审核人", "审批人"],
            "approval_levels": len(steps),
            "note": "⚠️ 此结果基于中石化AI平台工作流状态生成的回退分析",
            "warning": warning_msg or "中石化AI平台返回了工作流完成状态，但未包含具体的分析内容。此结果是基于文档内容的基础分析。",
            "api_status": api_data,
            "recommendation": "建议尝试使用其他AI提供商获得更详细的分析结果。"
        }
        
        # 构建步骤详情
        for i, step in enumerate(steps, 1):
            fallback_response["steps"].append({
                "step_number": i,
                "description": step,
                "approver": "相关负责人",
                "action": "处理",
                "conditions": [],
                "time_limit": "按规定时限"
            })
        
        import json
        return json.dumps(fallback_response, ensure_ascii=False, indent=2)
    
    def _convert_sinopec_response_to_json(self, response: str) -> str:
        """将中石化AI平台的自然语言响应转换为JSON格式，保留原始分析内容"""
        try:
            print("🔄 开始转换中石化AI平台响应为JSON格式...")
            print(f"原始响应长度: {len(response)}")
            print(f"原始响应前200字符: {response[:200]}")
            
            # 首先尝试检查响应中是否已经包含JSON格式
            import re
            import json
            
            # 尝试提取可能的JSON内容
            json_patterns = [
                r'```json\s*(\{.*?\})\s*```',
                r'```\s*(\{.*?\})\s*```',
                r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, response, re.DOTALL)
                for match in matches:
                    try:
                        json_str = match[0] if isinstance(match, tuple) else match
                        parsed_json = json.loads(json_str)
                        print("✅ 在响应中发现有效JSON格式")
                        return json_str
                    except json.JSONDecodeError:
                        continue
            
            # 如果没有找到JSON，则保留原始分析内容并构建JSON结构
            print("📝 未发现JSON格式，保留原始分析内容...")
            
            # 初始化结果结构，保留原始响应作为分析内容
            result = {
                "process_name": "制度审批流程分析",
                "steps": [],
                "conditions": [],
                "total_steps": 0,
                "has_branches": False,
                "complexity": "中等",
                "key_roles": [],
                "approval_levels": 1,
                "original_analysis": response,  # 保留原始分析内容
                "analysis_source": "中石化AI平台",
                "analysis_type": "标准化模板",  # 明确标注为模板
                "template_indicator": True,  # 模板标识
                "note": "🏭 此结果为中石化AI平台提供的行业标准流程模板，供参考使用",
                "warning": "⚠️ 重要提示：中石化API返回的是标准化示例模板，而非基于您具体文档的定制分析",
                "recommendation": "💡 建议：将此模板作为最佳实践参考，同时使用其他AI提供商获得基于您文档的定制分析",
                "template_features": [
                    "提供行业标准流程模板",
                    "展示最佳实践示例",
                    "适用于流程设计参考",
                    "非个性化定制分析"
                ]
            }
            
            # 更智能的步骤提取，保留更多原文信息
            lines = response.split('\n')
            steps_found = []
            current_step = ""
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 检查是否是步骤开始的标志
                step_indicators = [
                    r'^\d+\.',  # 数字开头
                    r'^[一二三四五六七八九十]\.',  # 中文数字
                    r'^第[一二三四五六七八九十\d]+[条步骤]',  # 第X条
                    r'^[•·▪▫◦‣⁃]',  # 各种项目符号
                    r'^\([一二三四五六七八九十\d]+\)',  # 括号数字
                ]
                
                is_step_start = any(re.match(pattern, line) for pattern in step_indicators)
                
                if is_step_start:
                    if current_step:
                        steps_found.append(current_step.strip())
                    current_step = line
                else:
                    # 如果当前行包含流程相关关键词，可能是步骤的延续
                    if current_step and any(keyword in line for keyword in ['审', '批', '申请', '签', '核', '提交', '确认', '备案']):
                        current_step += " " + line
                    elif not current_step and any(keyword in line for keyword in ['审', '批', '申请', '签', '核', '提交', '确认', '备案']):
                        # 即使没有明确的步骤标志，如果包含关键词也可能是步骤
                        current_step = line
            
            # 添加最后一个步骤
            if current_step:
                steps_found.append(current_step.strip())
            
            # 如果仍然没有找到步骤，使用更宽松的匹配
            if not steps_found:
                for line in lines:
                    line = line.strip()
                    if (len(line) > 10 and 
                        any(keyword in line for keyword in ['审批', '审核', '申请', '签署', '确认', '备案', '提交', '核准'])):
                        steps_found.append(line)
            
            # 构建步骤数据，保留更多原文信息
            for i, step_desc in enumerate(steps_found[:15], 1):  # 增加到15个步骤
                # 更精确的审批人提取
                approver = "相关部门"
                approver_patterns = [
                    r'([^，。\s]*部门?)',
                    r'([^，。\s]*经理)',
                    r'([^，。\s]*总[经理]?)',
                    r'([^，。\s]*主管)',
                    r'([^，。\s]*负责人)',
                    r'([^，。\s]*领导)',
                ]
                
                for pattern in approver_patterns:
                    match = re.search(pattern, step_desc)
                    if match:
                        approver = match.group(1)
                        break
                
                # 更精确的动作类型识别
                action = "处理"
                action_mapping = {
                    '申请': '申请',
                    '提交': '提交',
                    '审核': '审核',
                    '审批': '审批',
                    '批准': '批准',
                    '核准': '核准',
                    '签署': '签署',
                    '签字': '签字',
                    '确认': '确认',
                    '备案': '备案',
                    '归档': '归档',
                    '通知': '通知',
                    '执行': '执行'
                }
                
                for keyword, action_type in action_mapping.items():
                    if keyword in step_desc:
                        action = action_type
                        break
                
                # 提取时间限制信息
                time_limit = ""
                time_patterns = [
                    r'(\d+[个]?[工作]?日[内]?)',
                    r'(\d+[个]?小时[内]?)',
                    r'(\d+[个]?月[内]?)',
                    r'([立即|当日|次日|及时])',
                ]
                
                for pattern in time_patterns:
                    match = re.search(pattern, step_desc)
                    if match:
                        time_limit = match.group(1)
                        break
                
                # 提取条件信息
                conditions = []
                condition_patterns = [
                    r'([^，。]*万元[以上|以下|之间])',
                    r'(金额[^，。]*)',
                    r'(超过[^，。]*)',
                    r'(达到[^，。]*)',
                ]
                
                for pattern in condition_patterns:
                    matches = re.findall(pattern, step_desc)
                    conditions.extend(matches)
                
                step_data = {
                    "step_number": i,
                    "description": step_desc,
                    "approver": approver,
                    "action": action,
                    "conditions": conditions,
                    "time_limit": time_limit,
                    "order_indicators": [],
                    "parallel_actions": [],
                    "branch_conditions": ""
                }
                result["steps"].append(step_data)
            
            result["total_steps"] = len(result["steps"])
            
            # 提取关键角色
            roles = set()
            for step in result["steps"]:
                if step["approver"] != "相关部门":
                    roles.add(step["approver"])
            result["key_roles"] = list(roles)
            
            # 判断复杂度
            if result["total_steps"] <= 3:
                result["complexity"] = "简单"
            elif result["total_steps"] <= 6:
                result["complexity"] = "中等"
            else:
                result["complexity"] = "复杂"
            
            # 转换为JSON字符串
            json_result = json.dumps(result, ensure_ascii=False, indent=2)
            
            print(f"✅ 成功转换为JSON格式，包含{result['total_steps']}个步骤")
            print(f"📋 保留了原始分析内容，长度: {len(result['original_analysis'])} 字符")
            return json_result
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            import traceback
            print(f"错误详情: {traceback.format_exc()}")
            
            # 返回包含原始响应的基本JSON结构
            fallback_result = {
                "process_name": "制度审批流程",
                "steps": [
                    {
                        "step_number": 1,
                        "description": "AI分析结果转换失败，请查看原始分析内容",
                        "approver": "系统",
                        "action": "解析",
                        "conditions": [],
                        "time_limit": "",
                        "order_indicators": [],
                        "parallel_actions": [],
                        "branch_conditions": ""
                    }
                ],
                "conditions": [],
                "total_steps": 1,
                "has_branches": False,
                "complexity": "简单",
                "key_roles": ["系统"],
                "approval_levels": 1,
                "original_analysis": response,  # 即使失败也保留原始内容
                "analysis_source": "中石化AI平台",
                "error_message": str(e),
                "note": "转换过程中出现错误，请参考原始分析内容"
            }
            import json
            return json.dumps(fallback_result, ensure_ascii=False, indent=2)
    
    def _parse_response(self, response: str, original_text: str) -> Dict[str, Any]:
        """解析大模型响应"""
        try:
            print(f"🔍 开始解析大模型响应")
            print(f"原始响应长度: {len(response)}")
            print(f"响应前100字符: {repr(response[:100])}")
            
            # 清理响应文本
            cleaned_response = self._clean_response(response)
            print(f"清理后响应长度: {len(cleaned_response)}")
            print(f"清理后响应前100字符: {repr(cleaned_response[:100])}")
            
            # 策略1: 尝试直接解析JSON
            if cleaned_response.strip().startswith('{'):
                try:
                    result = json.loads(cleaned_response)
                    print("✅ 策略1成功: 直接JSON解析")
                    return self._validate_and_fix_result(result)
                except json.JSONDecodeError as e:
                    print(f"策略1失败: 直接JSON解析失败 - {e}")
            
            # 策略2: 尝试提取JSON部分
            import re
            json_patterns = [
                r'```json\s*(\{.*?\})\s*```',  # Markdown JSON代码块
                r'```\s*(\{.*?\})\s*```',      # 普通代码块
                r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # 匹配嵌套JSON
                r'\{.*?\}',                    # 简单匹配
            ]
            
            for i, pattern in enumerate(json_patterns, 1):
                try:
                    json_matches = re.findall(pattern, cleaned_response, re.DOTALL)
                    for match in json_matches:
                        try:
                            # 如果是元组（来自分组），取第一个元素
                            json_str = match[0] if isinstance(match, tuple) else match
                            result = json.loads(json_str)
                            print(f"✅ 策略2.{i}成功: 正则表达式JSON解析")
                            return self._validate_and_fix_result(result)
                        except json.JSONDecodeError:
                            continue
                except Exception as e:
                    print(f"策略2.{i}失败: {e}")
                    continue
            
            # 策略3: 尝试修复常见的JSON格式问题
            fixed_response = self._fix_json_format(cleaned_response)
            if fixed_response:
                try:
                    result = json.loads(fixed_response)
                    print("✅ 策略3成功: JSON格式修复后解析")
                    return self._validate_and_fix_result(result)
                except json.JSONDecodeError as e:
                    print(f"策略3失败: JSON格式修复后仍无法解析 - {e}")
            
            # 策略4: 尝试逐行解析，寻找有效的JSON片段
            lines = cleaned_response.split('\n')
            for i, line in enumerate(lines):
                line = line.strip()
                if line.startswith('{') and line.endswith('}'):
                    try:
                        result = json.loads(line)
                        print(f"✅ 策略4成功: 第{i+1}行单行JSON解析")
                        return self._validate_and_fix_result(result)
                    except json.JSONDecodeError:
                        continue
            
            # 策略5: 尝试拼接多行构成完整JSON
            json_lines = []
            in_json = False
            brace_count = 0
            
            for line in lines:
                line = line.strip()
                if not in_json and line.startswith('{'):
                    in_json = True
                    json_lines = [line]
                    brace_count = line.count('{') - line.count('}')
                elif in_json:
                    json_lines.append(line)
                    brace_count += line.count('{') - line.count('}')
                    if brace_count == 0:
                        # JSON对象结束
                        try:
                            json_str = '\n'.join(json_lines)
                            result = json.loads(json_str)
                            print("✅ 策略5成功: 多行JSON拼接解析")
                            return self._validate_and_fix_result(result)
                        except json.JSONDecodeError:
                            pass
                        in_json = False
                        json_lines = []
                        brace_count = 0
            
            # 如果所有策略都失败，检查是否允许本地解析
            print("❌ 所有JSON解析策略都失败，且本地解析已禁用")
            raise Exception("无法解析LLM响应为有效JSON格式，且本地解析已禁用")
            
        except Exception as e:
            print(f"❌ 解析响应时发生异常: {e}")
            import traceback
            print(f"异常堆栈: {traceback.format_exc()}")
            # 本地解析已禁用
            print("❌ 本地解析已禁用，无法处理响应解析异常")
            raise Exception(f"响应解析异常且本地解析已禁用: {e}") from e
    
    def _clean_response(self, response: str) -> str:
        """清理响应文本"""
        # 移除常见的前缀和后缀
        response = response.strip()
        
        # 移除Markdown代码块标记
        if response.startswith('```json'):
            response = response[7:]
        elif response.startswith('```'):
            response = response[3:]
        
        if response.endswith('```'):
            response = response[:-3]
        
        # 移除常见的说明文字
        prefixes_to_remove = [
            "以下是JSON格式的结果：",
            "JSON格式结果：",
            "结果如下：",
            "分析结果：",
            "根据分析，JSON格式如下：",
                            "根据制度文本分析，智能审查结果如下：",
            "分析结果为：",
            "JSON结果：",
        ]
        
        for prefix in prefixes_to_remove:
            if response.startswith(prefix):
                response = response[len(prefix):].strip()
        
        # 移除行首的换行符和空格
        response = response.lstrip('\n\r\t ')
        
        # 如果响应以引号开始但不是JSON，可能是被包装了
        if response.startswith('"') and response.endswith('"') and not response.startswith('{"'):
            try:
                # 尝试解析被引号包装的JSON
                response = json.loads(response)
                if isinstance(response, str):
                    response = response.strip()
            except:
                pass
        
        return response.strip()
    
    def _fix_json_format(self, response: str) -> str:
        """尝试修复常见的JSON格式问题"""
        try:
            print(f"尝试修复JSON格式，原始长度: {len(response)}")
            
            # 查找JSON对象的开始和结束
            start_idx = response.find('{')
            if start_idx == -1:
                print("未找到JSON开始标记")
                return None
            
            # 从后往前找最后一个}
            end_idx = response.rfind('}')
            if end_idx == -1 or end_idx <= start_idx:
                print("未找到JSON结束标记")
                return None
            
            json_str = response[start_idx:end_idx + 1]
            print(f"提取的JSON字符串长度: {len(json_str)}")
            print(f"JSON前50字符: {json_str[:50]}")
            
            # 尝试修复常见问题
            # 1. 移除多余的逗号
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)
            
            # 2. 修复换行符问题
            json_str = json_str.replace('\n', ' ').replace('\r', ' ')
            
            # 3. 修复多余的空格
            json_str = re.sub(r'\s+', ' ', json_str)
            
            # 4. 尝试修复未闭合的字符串
            # 简单的修复：确保每个键和值都被正确引用
            import re
            
            # 修复键名没有引号的问题
            json_str = re.sub(r'(\w+):', r'"\1":', json_str)
            
            # 修复已经有引号的键名被重复引用的问题
            json_str = re.sub(r'""(\w+)"":', r'"\1":', json_str)
            
            print(f"修复后JSON前50字符: {json_str[:50]}")
            return json_str
            
        except Exception as e:
            print(f"JSON修复失败: {e}")
            return None
    
    def _validate_and_fix_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """验证和修复解析结果"""
        # 确保必要字段存在
        if "process_name" not in result:
            result["process_name"] = "制度审查流程"
        
        if "steps" not in result:
            result["steps"] = []
        
        if "conditions" not in result:
            result["conditions"] = []
        
        if "total_steps" not in result:
            result["total_steps"] = len(result["steps"])
        
        if "has_branches" not in result:
            result["has_branches"] = len(result["conditions"]) > 0
        
        if "complexity" not in result:
            step_count = len(result["steps"])
            condition_count = len(result["conditions"])
            total_score = step_count + condition_count * 2
            
            if total_score <= 3:
                result["complexity"] = "简单"
            elif total_score <= 6:
                result["complexity"] = "中等"
            else:
                result["complexity"] = "复杂"
        
        # 验证步骤格式
        for i, step in enumerate(result["steps"]):
            if "step_number" not in step:
                step["step_number"] = i + 1
            if "description" not in step:
                step["description"] = f"步骤{i + 1}"
            if "approver" not in step:
                step["approver"] = "未指定"
            if "action" not in step:
                step["action"] = "处理"
            if "conditions" not in step:
                step["conditions"] = []
            if "time_limit" not in step:
                step["time_limit"] = None
            if "order_indicators" not in step:
                step["order_indicators"] = []
        
        return result

# 全局处理器实例
llm_processor = LLMProcessor()

# 便捷函数
def extract_with_llm(text: str, provider: str = None) -> Dict[str, Any]:
    """使用大模型提取流程的便捷函数"""
    return llm_processor.extract_process_with_llm(text, provider) 