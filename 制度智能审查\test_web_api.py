#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web API端点
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('api_test')

def test_web_api():
    """测试Web API端点"""
    try:
        logger.info("开始测试Web API端点...")
        
        # 测试文本
        test_text = """
        第五条  运行管理机制
        （一）域长提出变更申请
        （二）相关部门审核
        （三）最终批准执行
        """
        
        # 准备POST数据
        data = {
            'text_content': test_text,
            'use_llm': 'true',
            'provider': 'sinopec'
        }
        
        # 发送POST请求到/upload端点
        url = 'http://localhost:5000/upload'
        logger.info(f"发送POST请求到: {url}")
        
        response = requests.post(url, data=data)
        
        logger.info(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                logger.info("✓ API调用成功")
                logger.info(f"  成功状态: {result.get('success', False)}")
                logger.info(f"  处理时间: {result.get('processing_time', 'N/A')}")
                logger.info(f"  LLM成功: {result.get('llm_success', False)}")
                logger.info(f"  下载文件数: {len(result.get('download_files', []))}")
                
                if result.get('success'):
                    logger.info("✓ 流程解析成功!")
                    return True
                else:
                    logger.error(f"✗ 流程解析失败: {result.get('error', 'Unknown error')}")
                    return False
                    
            except json.JSONDecodeError:
                logger.error("✗ 响应不是有效的JSON")
                logger.error(f"响应内容: {response.text[:500]}...")
                return False
        else:
            logger.error(f"✗ HTTP请求失败: {response.status_code}")
            logger.error(f"响应内容: {response.text[:500]}...")
            return False
        
    except Exception as e:
        logger.error(f"✗ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_web_api()
    if success:
        print("Web API测试成功!")
    else:
        print("Web API测试失败!")
        exit(1) 