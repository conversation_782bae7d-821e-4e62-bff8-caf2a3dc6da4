<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试 - 制度智能审查系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .response-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .response-content {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-all;
            background: #ffffff;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .info-badge {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .info-badge.provider {
            background-color: #007bff;
            color: white;
        }
        
        .info-badge.time {
            background-color: #28a745;
            color: white;
        }
        
        .info-badge.length {
            background-color: #ffc107;
            color: #212529;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: 8px;
        }
        
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 15px;
            border-radius: 6px 6px 0 0;
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .section-content {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 6px 6px;
            padding: 15px;
        }
        
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }
        
        .response-section {
            position: relative;
            margin-bottom: 20px;
        }
        
        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .json-key { color: #63b3ed; }
        .json-string { color: #68d391; }
        .json-number { color: #fbb6ce; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="/">制度智能审查系统</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4 debug-container">
        <!-- 页面标题 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-bug me-2"></i>API调试工具
                </h4>
                <p class="mb-0 mt-2">直接查看AI提供商的原始响应数据</p>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>使用说明：</strong>
                    输入制度文本内容，选择AI提供商，点击"获取原始响应"即可查看API返回的原始数据。
                </div>
            </div>
        </div>
        
        <!-- 输入区域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-keyboard me-2"></i>输入参数</h5>
            </div>
            <div class="card-body">
                <form id="debugForm">
                    <div class="mb-3">
                        <label for="textContent" class="form-label">
                            <i class="fas fa-file-text me-2"></i>制度文本内容
                        </label>
                        <textarea class="form-control" id="textContent" rows="8" 
                                  placeholder="请输入制度文本内容..." required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label for="llmProvider" class="form-label">
                                <i class="fas fa-robot me-2"></i>AI提供商
                            </label>
                            <select class="form-select" id="llmProvider" required>
                                {% for provider in api_providers %}
                                    {% if provider.id != 'local' %}
                                        <option value="{{ provider.id }}" 
                                                {% if not provider.enabled or not provider.valid %}disabled{% endif %}>
                                            {{ provider.name }}
                                            {% if not provider.enabled %}(未启用){% endif %}
                                            {% if not provider.valid %}(配置不完整){% endif %}
                                        </option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary" id="debugBtn">
                                <i class="fas fa-play me-2"></i>获取原始响应
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="useExampleText()">
                                <i class="fas fa-magic me-2"></i>使用示例
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 响应展示区域 -->
        <div id="responseArea" style="display: none;">
            <!-- 基本信息 -->
            <div class="response-section">
                <h5 class="section-header">
                    <i class="fas fa-info-circle me-2"></i>响应信息
                </h5>
                <div class="section-content">
                    <div id="responseInfo"></div>
                </div>
            </div>
            
            <!-- 文档内容 -->
            <div class="response-section">
                <h5 class="section-header">
                    <i class="fas fa-file-text me-2"></i>发送的文档内容
                </h5>
                <div class="section-content">
                    <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyToClipboard('documentContent')">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                    <div class="response-content" id="documentContent"></div>
                </div>
            </div>
            
            <!-- 原始响应 -->
            <div class="response-section">
                <h5 class="section-header">
                    <i class="fas fa-code me-2"></i>API原始响应
                </h5>
                <div class="section-content">
                    <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyToClipboard('rawResponse')">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                    <div class="response-content" id="rawResponse"></div>
                </div>
            </div>
            
            <!-- JSON格式化显示 -->
            <div class="response-section">
                <h5 class="section-header">
                    <i class="fas fa-brackets-curly me-2"></i>格式化JSON
                </h5>
                <div class="section-content">
                    <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyToClipboard('formattedJson')">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                    <div class="json-viewer" id="formattedJson"></div>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div id="loadingArea" style="display: none;">
            <div class="card">
                <div class="card-body text-center">
                    <div class="spinner-border text-primary" role="status"></div>
                    <p class="mt-3 mb-0">正在调用API，请稍候...</p>
                </div>
            </div>
        </div>
        
        <!-- 错误显示 -->
        <div id="errorArea" style="display: none;">
            <div class="alert alert-danger">
                <h5 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>调用失败
                </h5>
                <div id="errorMessage"></div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const debugForm = document.getElementById('debugForm');
        const debugBtn = document.getElementById('debugBtn');
        const responseArea = document.getElementById('responseArea');
        const loadingArea = document.getElementById('loadingArea');
        const errorArea = document.getElementById('errorArea');
        
        debugForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const textContent = document.getElementById('textContent').value.trim();
            const llmProvider = document.getElementById('llmProvider').value;
            
            if (!textContent) {
                alert('请输入制度文本内容');
                return;
            }
            
            // 显示加载状态
            responseArea.style.display = 'none';
            errorArea.style.display = 'none';
            loadingArea.style.display = 'block';
            debugBtn.disabled = true;
            
            // 调用API
            fetch('/api/raw_response', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text: textContent,
                    llm_provider: llmProvider
                })
            })
            .then(response => response.json())
            .then(data => {
                loadingArea.style.display = 'none';
                debugBtn.disabled = false;
                
                if (data.success) {
                    displayResponse(data);
                    responseArea.style.display = 'block';
                } else {
                    displayError(data.error || '未知错误');
                }
            })
            .catch(error => {
                loadingArea.style.display = 'none';
                debugBtn.disabled = false;
                displayError('网络请求失败: ' + error.message);
            });
        });
    });

    function displayResponse(data) {
        // 显示响应信息
        const responseInfo = document.getElementById('responseInfo');
        responseInfo.innerHTML = `
            <div class="d-flex flex-wrap gap-2">
                <span class="info-badge provider">提供商: ${data.provider}</span>
                <span class="info-badge time">耗时: ${data.processing_time}</span>
                <span class="info-badge length">文档长度: ${data.content_length} 字符</span>
                <span class="info-badge length">响应长度: ${data.response_length} 字符</span>
            </div>
        `;
        
        // 显示文档内容
        const documentContent = document.getElementById('documentContent');
        documentContent.textContent = data.content_preview || '无文档数据';
        
        // 显示原始响应
        const rawResponse = document.getElementById('rawResponse');
        rawResponse.textContent = data.raw_response || '无响应数据';
        
        // 尝试格式化JSON
        const formattedJson = document.getElementById('formattedJson');
        try {
            const jsonData = JSON.parse(data.raw_response);
            formattedJson.innerHTML = syntaxHighlight(JSON.stringify(jsonData, null, 2));
        } catch (e) {
            formattedJson.textContent = '响应不是有效的JSON格式:\n' + (data.raw_response || '无数据');
        }
    }

    function displayError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        document.getElementById('errorArea').style.display = 'block';
    }

    function copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        const text = element.textContent;
        
        navigator.clipboard.writeText(text).then(function() {
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
            btn.classList.add('btn-success');
            btn.classList.remove('btn-outline-primary');
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-primary');
            }, 2000);
        }).catch(function(err) {
            alert('复制失败: ' + err);
        });
    }

    function syntaxHighlight(json) {
        json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
        return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
            var cls = 'json-number';
            if (/^"/.test(match)) {
                if (/:$/.test(match)) {
                    cls = 'json-key';
                } else {
                    cls = 'json-string';
                }
            } else if (/true|false/.test(match)) {
                cls = 'json-boolean';
            } else if (/null/.test(match)) {
                cls = 'json-null';
            }
            return '<span class="' + cls + '">' + match + '</span>';
        });
    }

    function useExampleText() {
        const exampleText = `海南炼化公司域长负责制运行管理细则

第一条 为规范域长负责制运行管理，明确各级职责，特制定本细则。

第二条 域长负责制实行分级管理：
1. 公司级域长由总经理担任
2. 部门级域长由部门经理担任  
3. 班组级域长由班组长担任

第三条 域长职责：
1. 负责本域内安全生产管理
2. 组织开展安全检查
3. 审批本域内重要事项
4. 签字确认安全措施

第四条 域长任免程序：
1. 由生产部门提名
2. 经安全委员会审核
3. 报公司领导批准
4. 发布任免通知`;
        
        document.getElementById('textContent').value = exampleText;
    }
    </script>
</body>
</html> 