#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终系统功能测试
验证流程解析功能是否完全修复
"""

import json
import time
from llm_processor import extract_with_llm

def test_complete_system():
    """完整系统功能测试"""
    print("🚀 完整系统功能测试")
    print("=" * 60)
    
    # 测试文本
    test_content = """
    海南炼化公司域长负责制运行管理细则
    
    第一章 总则
    第一条 为了规范公司域长负责制的运行管理，明确各级域长的职责权限，建立健全管理制度，特制定本细则。
    
    第二章 审批流程
    第五条 域长任免审批流程：
    1. 人力资源部提出域长任免建议
    2. 分管副总经理初步审核
    3. 总经理办公会议讨论决定
    4. 党委会审议通过
    5. 公司总经理签发任免通知
    6. 人力资源部办理相关手续
    7. 各部门配合执行到位
    
    第六条 域长考核审批流程：
    1. 各域长提交月度工作报告
    2. 直接上级进行初步评价
    3. 人力资源部汇总考核材料
    4. 分管副总经理综合评估
    5. 总经理最终确认考核结果
    6. 考核结果反馈给相关域长
    
    第三章 监督管理
    第十条 域长工作监督流程：
    1. 建立日常监督检查机制
    2. 定期开展专项检查活动
    3. 收集各方面反馈意见
    4. 及时处理发现的问题
    5. 形成监督检查报告
    6. 向上级领导汇报情况
    """
    
    print(f"📝 测试内容：海南炼化公司域长负责制运行管理细则")
    print(f"📊 内容长度：{len(test_content)} 字符")
    
    print(f"\n🚀 开始调用LLM进行流程提取...")
    start_time = time.time()
    
    try:
        result = extract_with_llm(test_content, "sinopec")
        end_time = time.time()
        
        print(f"⏱️ 总耗时: {end_time - start_time:.2f}秒")
        
        if result.get("success"):
            print("✅ 系统功能测试成功!")
            
            data = result.get("data", {})
            print(f"\n📊 解析结果:")
            print(f"  - 流程名称: {data.get('process_name', '未知')}")
            print(f"  - 步骤数量: {data.get('total_steps', 0)}")
            print(f"  - 分析来源: {data.get('analysis_source', '未知')}")
            
            # 显示原始分析内容
            if "original_analysis" in data:
                original_content = data["original_analysis"]
                print(f"\n📄 原始分析内容 ({len(original_content)} 字符):")
                print("-" * 40)
                print(original_content[:800] + "..." if len(original_content) > 800 else original_content)
                print("-" * 40)
            
            # 显示提取的步骤
            steps = data.get("steps", [])
            if steps and len(steps) > 0:
                print(f"\n📋 提取的流程步骤 (共{len(steps)}个):")
                for i, step in enumerate(steps):
                    step_num = step.get('step_number', i+1)
                    step_name = step.get('step_name', '未知步骤')
                    description = step.get('description', '无描述')
                    department = step.get('department', '未指定')
                    action = step.get('action', '处理')
                    
                    print(f"\n  步骤 {step_num}: {step_name}")
                    print(f"    📝 描述: {description}")
                    print(f"    🏢 部门: {department}")
                    print(f"    ⚡ 动作: {action}")
                
                # 统计信息
                departments = set()
                actions = set()
                for step in steps:
                    if step.get('department'):
                        departments.add(step['department'])
                    if step.get('action'):
                        actions.add(step['action'])
                
                print(f"\n📈 统计信息:")
                print(f"  - 涉及部门: {len(departments)} 个 ({', '.join(list(departments)[:5])}{'...' if len(departments) > 5 else ''})")
                print(f"  - 动作类型: {len(actions)} 种 ({', '.join(list(actions)[:5])}{'...' if len(actions) > 5 else ''})")
                
                # 验证质量
                quality_score = 0
                if len(steps) >= 5:
                    quality_score += 30  # 步骤数量充足
                if len(departments) >= 3:
                    quality_score += 25  # 涉及多个部门
                if any('审核' in step.get('step_name', '') or '审批' in step.get('step_name', '') for step in steps):
                    quality_score += 25  # 包含审核审批环节
                if any(len(step.get('description', '')) > 10 for step in steps):
                    quality_score += 20  # 描述详细
                
                print(f"  - 提取质量: {quality_score}/100 分")
                
                if quality_score >= 80:
                    print("    🌟 优秀 - 流程提取完整准确")
                elif quality_score >= 60:
                    print("    ✅ 良好 - 流程提取基本准确")
                elif quality_score >= 40:
                    print("    ⚠️ 一般 - 流程提取部分准确")
                else:
                    print("    ❌ 较差 - 流程提取不够准确")
                
            else:
                print("⚠️ 未提取到具体流程步骤")
                return False
            
            return True
            
        else:
            print("❌ 系统功能测试失败!")
            error_msg = result.get("message", "未知错误")
            print(f"错误信息: {error_msg}")
            return False
            
    except Exception as e:
        end_time = time.time()
        print(f"❌ 测试过程中出现异常: {e}")
        print(f"⏱️ 异常前耗时: {end_time - start_time:.2f}秒")
        return False

def test_web_interface():
    """测试Web界面是否正常"""
    print(f"\n" + "=" * 60)
    print("🌐 Web界面可用性测试")
    print("=" * 60)
    
    try:
        import web_app
        print("✅ Web应用模块导入成功")
        
        # 检查关键函数是否存在
        if hasattr(web_app, 'app'):
            print("✅ Flask应用实例存在")
        else:
            print("❌ Flask应用实例不存在")
            return False
        
        print("✅ Web界面基本可用")
        print("💡 建议：运行 python web_app.py 启动Web服务")
        return True
        
    except ImportError as e:
        print(f"❌ Web应用模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ Web界面测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 制度智能审查系统 - 最终功能测试")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔧 测试目标: 验证流程解析问题是否完全修复")
    
    # 核心功能测试
    core_success = test_complete_system()
    
    # Web界面测试
    web_success = test_web_interface()
    
    # 总结报告
    print(f"\n" + "=" * 60)
    print("🏁 最终测试报告")
    print("=" * 60)
    
    print(f"📊 测试结果:")
    print(f"  - 核心流程解析功能: {'✅ 通过' if core_success else '❌ 失败'}")
    print(f"  - Web界面可用性: {'✅ 通过' if web_success else '❌ 失败'}")
    
    overall_success = core_success and web_success
    print(f"\n🎯 总体评估: {'✅ 系统修复成功' if overall_success else '❌ 系统仍有问题'}")
    
    if overall_success:
        print(f"\n🎉 恭喜！制度智能审查系统已完全修复")
        print(f"✨ 主要改进:")
        print(f"  1. 修正了API字段名配置（使用document而非files）")
        print(f"  2. 优化了流式响应解析逻辑")
        print(f"  3. 增强了多种数据格式的兼容性")
        print(f"  4. 建立了完善的错误处理机制")
        print(f"\n🚀 系统现在可以正常使用:")
        print(f"  - 运行: python web_app.py")
        print(f"  - 访问: http://localhost:5000")
    else:
        print(f"\n🔧 需要进一步排查的问题:")
        if not core_success:
            print(f"  - 核心流程解析功能异常")
        if not web_success:
            print(f"  - Web界面模块问题")

if __name__ == "__main__":
    main() 