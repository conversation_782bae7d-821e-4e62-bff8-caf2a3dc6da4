cURL请求信息

类型	信息
cURL	
curl -X POST 'https://agent.ai.sinopec.com/aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349' -H 'Content-Type: application/json' 
  -H 'Authorization: Bearer {你的API key}' 
  -d '{"content":[{"field_name":"test","type":"input","value":""}],"stream":true}'  
        
cURL示例	
curl -X POST 'https://agent.ai.sinopec.com/aicoapi/gateway/v2/workflow/api_run/b6e3a50794484497a9983743040ae349' -H 'Content-Type: application/json' 
  -H 'Authorization: Bearer txWkpKYK****************' 
  -d '{"content":[{"field_name":"test","type":"input","value":""}],"stream":true}'  
        
请求参数

参数名称	类型	必填	说明	示例
Authorization	string	是	Api Key，用于访问权限校验	8D97abKWTgSBOECFFuEVOyWYfxdPgPTG
stream	boolean	是	是否为流式输出，true为流式输出，false为非流式输出	true
content	array	否	数组类型的输入内容列表:每组包括field_name、type、required、value	[{"field_name":"text","type":"input","required":true,"value":""},{"field_name":"user_ input","type":"input","required":false,"value":""}]
field_name	string	是	字段名	"field_name":"text"
type	string	是	字段类型，包括input、file、image_path、media_file_path。input是文本输入，file为文件，image_path为图片地址，media_file_path为视频地址	"type":"input"
value	string	否	该字段的值，默认可以为空	"value":"帮我生成一个报告"
返回参数(流式)

参数名称	类型	说明
event	string	固定值，取自列表[message_start、node_start、node_chunk、node_finished、node_error、tool_start、tool_finished、tool_error、message_finished]
session_id	string	会话ID，用于多轮会话
step_id	string	当前会话工作流步骤的唯一ID
created	int64	会话创建时间的时间戳，单位为秒级
data	object	
text	array	
output	boolean	返回的最终结果
url_list	array	非必传，地址数组，记录了涉及到网页搜索时的溯源URL。
search_results	array	非必传，检索结果数组，用于涉及到知识库检索节点时的片段溯源。
model_stop_reason	string	非必传，模型截断原因，包括：stop为模型停止，length为模型超长，其他error类型均在 event中返回。
node_id	string	节点ID
record_id	string	数据记录ID
task_name	string	节点任务名称，结构为节点名称拼接任务名称
node_name	string	节点名称
reset_name	string	任务名称
usage	object	使用情况
total_tokens	int	输入和输出总token数
prompt_tokens	int	模型所有输入消耗的token数
completion_tokens	int	返回结果的token数
max_token	string	模型支持的最大上下文长度
返回参数(非流式)

参数名称	类型	说明
code	int	返回代码，表示返回状态
msg	string	返回说明，返回体的解释
data	object	返回数据
event	string	固定值，取自列表[message_start、node_start、node_chunk、node_finished、node_error、tool_start、tool_finished、tool_error、message_finished]
session_id	string	会话ID，用于多轮会话
step_id	string	当前会话工作流步骤的唯一ID
created	int64	会话创建时间的时间戳，单位为秒级
data	object	
text	array	
output	boolean	返回的最终结果
url_list	array	非必传，地址数组，记录了涉及到网页搜索时的溯源URL。
search_results	array	非必传，检索结果数组，用于涉及到知识库检索节点时的片段溯源。
model_stop_reason	string	非必传，模型截断原因，包括：stop为模型停止，length为模型超长，其他error类型均在 event中返回。
node_id	string	节点ID
record_id	string	数据记录ID
task_name	string	节点任务名称，结构为节点名称拼接任务名称
node_name	string	节点名称
reset_name	string	任务名称
usage	object	使用情况
total_tokens	int	输入和输出总token数
prompt_tokens	int	模型所有输入消耗的token数
completion_tokens	int	返回结果的token数
max_token	string	模型支持的最大上下文长度
状态码列表

状态码	信息描述
1001	认证失败
1002	用户无效
1003	请求参数错误
1005	无效 API
1006	大模型服务错误
1007	工作流内部错误
1009	数据库错误
1010	服务内部错误